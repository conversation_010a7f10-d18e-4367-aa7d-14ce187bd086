import React from "react";
import { Plus } from "lucide-react";

interface AddMoreItemsProps {
  onClick: () => void;
}

export const AddMoreItems: React.FC<AddMoreItemsProps> = ({ onClick }) => {
  return (
    <button
      className="mx-3 px-3 py-2.5 flex bg-white align-center object-center justify-center shadow-lg rounded-xl focus:border focus:border-primary hover:border hover:border-primary"
      onClick={onClick}
    >
      <div className="flex gap-2 border-b border-dashed border-primary p-1">
        <Plus size="1rem" color="#00A38F" />
        <span className="text-primary text-sm leading-none">
          Add more items
        </span>
      </div>
    </button>
  );
};
