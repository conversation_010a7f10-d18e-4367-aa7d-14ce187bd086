import { useEffect, useState, useRef } from "react";
import { RPPaymentDetails } from "~/types";
import {
  RazorpayOptions,
  RazorpayResponse,
  RazorpayError,
  RazorpayInstance
} from "~/types/payment.types";

interface RazorpayHandlerProps {
  rpPaymentDetails: RPPaymentDetails;
  customerInfo?: {
    name?: string;
    email?: string;
    contact?: string;
  };
  onSuccess?: () => void;
  onFailure?: (error: Error) => void;
  onClose?: () => void;
  companyName?: string;
  theme?: {
    color?: string;
  };
}

declare global {
  interface Window {
    Razorpay: new (options: RazorpayOptions) => RazorpayInstance;
  }
}

/**
 * RazorpayHandler component handles the Razorpay payment flow
 * It loads the Razorpay script and initializes the checkout
 */
export const RazorpayHandler = ({
  rpPaymentDetails,
  customerInfo = {},
  onSuccess,
  onFailure,
  onClose,
  companyName = "mNet",
  theme = { color: "#2d7bf3" }
}: RazorpayHandlerProps) => {
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const razorpayInstanceRef = useRef<RazorpayInstance | null>(null);
  const hasInitializedRef = useRef(false);

  // Load Razorpay script
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Check if script is already loaded
    if (window.Razorpay) {
      setIsScriptLoaded(true);
      return;
    }

    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.async = true;
    script.onload = () => {
      setIsScriptLoaded(true);
    };

    document.body.appendChild(script);

    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  // Cleanup function for Razorpay instance
  useEffect(() => {
    return () => {
      // Clean up any existing Razorpay instance
      if (razorpayInstanceRef.current) {
        razorpayInstanceRef.current = null;
        hasInitializedRef.current = false;
      }
    };
  }, []);

  // Initialize Razorpay checkout when script is loaded
  useEffect(() => {
    // Only proceed if script is loaded, we have payment details, and haven't initialized yet
    if (!isScriptLoaded || !rpPaymentDetails || hasInitializedRef.current)
      return;

    console.log("Initializing Razorpay with options:", rpPaymentDetails);

    try {
      // Set initialization flag to prevent multiple instances
      hasInitializedRef.current = true;

      const options: RazorpayOptions = {
        key: rpPaymentDetails.razorPayKey,
        amount: rpPaymentDetails.amount,
        currency: rpPaymentDetails.currency || "INR",
        name: companyName,
        description: "Payment for your order",
        order_id: rpPaymentDetails.pgRefId,
        handler: function (response: RazorpayResponse) {
          console.log("Razorpay payment successful:", response);
          if (onSuccess) {
            onSuccess();
          }
          // Reset initialization flag after successful payment
          hasInitializedRef.current = false;
        },
        prefill: {
          name: customerInfo.name || "",
          email: customerInfo.email || "",
          contact: customerInfo.contact || ""
        },

        modal: {
          ondismiss: function () {
            console.log("Razorpay modal dismissed");
            if (onClose) {
              onClose();
            }
            // Reset initialization flag after modal is closed
            hasInitializedRef.current = false;
          }
        }
      };

      // Create a new Razorpay instance and store it in the ref
      razorpayInstanceRef.current = new window.Razorpay(options);

      // Set up payment failure handler
      razorpayInstanceRef.current.on(
        "payment.failed",
        function (response: RazorpayError) {
          console.log("Razorpay payment failed:", response);
          if (onFailure) {
            onFailure(
              new Error(response.error.description || "Payment failed")
            );
          }
          // Reset initialization flag after payment failure
          hasInitializedRef.current = false;
        }
      );

      // Open the Razorpay modal
      razorpayInstanceRef.current.open();
    } catch (error) {
      console.error("Failed to initialize Razorpay:", error);
      if (onFailure) {
        onFailure(
          error instanceof Error
            ? error
            : new Error("Failed to initialize payment gateway")
        );
      }
      // Reset initialization flag on error
      hasInitializedRef.current = false;
    }
  }, [
    isScriptLoaded,
    rpPaymentDetails,
    onSuccess,
    onFailure,
    onClose,
    companyName,
    theme,
    customerInfo
  ]);

  // This component doesn't render anything
  return null;
};

export default RazorpayHandler;
