# Multi-Outlet/Seller Selection Documentation

## Overview

The Multi-Outlet/Seller Selection feature enables users to choose from multiple restaurant outlets or sellers based on their location and delivery preferences. This feature is particularly designed for networks that support multiple sellers in the same area, providing users with options for delivery time, distance, and available items.

## Key Features

- **Location-Based Seller Discovery**: Automatically finds sellers near the user's location
- **Interactive Outlet Selection**: Bottom sheet UI for selecting different outlets
- **Real-Time Location Access**: Uses device GPS for accurate seller filtering
- **Delivery Time Estimation**: Shows estimated delivery times for each outlet
- **Network Configuration Driven**: Feature enabled/disabled based on network settings

## Architecture Components

### 1. Network Configuration (`app/types/index.ts`)

The multi-seller feature is controlled by network configuration:

```typescript
export interface NetworkConfig {
  domain: string;
  businessLogo: string;
  homePageBanner: string;
  networkId: number;
  multiSeller: boolean; // Key flag to enable/disable multi-seller
  defaultStartPage: RouteNames;
  wabMobileNumber: string;
  ondcDomain: AppDomain;
  networkType: NetworkType;
  sellerName: string;
}
```

**Key Configuration:**

- `multiSeller: boolean` - Enables/disables the multi-seller feature
- Set via network configuration API based on domain

### 2. Location Management (`app/hooks/useCurrentLocation.tsx`)

Handles device location access and cookie storage:

```typescript
export const useCurrentLocation = () => {
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [isRequesting, setIsRequesting] = useState(false);
  const [error, setError] = useState<GeolocationPositionError | null>(null);

  const request = (onSuccess?, onError?) => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        };
        // Store in cookie for server-side access
        document.cookie = `coordinates=${encodeURIComponent(
          JSON.stringify(coords)
        )}; path=/; max-age=3600`;
      },
      (error) => {
        // Clear cookie on error
        document.cookie = `coordinates=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC; max-age=0`;
      },
      { enableHighAccuracy: true, maximumAge: 0 }
    );
  };
};
```

**Features:**

- Automatic location detection on component mount
- Cookie-based coordinate storage for server access
- Permission handling and error management
- High accuracy GPS positioning

### 3. Seller List API (`app/services/buyer.service.ts`)

#### Get Seller List

```typescript
export async function getSellerList(
  latitude: number,
  longitude: number,
  deliveryDate: string,
  request: Request
): Promise<ApiResponse<SellerInfo[]>> {
  const params = {
    latitude,
    longitude,
    deliveryDate,
    sortByNearest: true
  };

  const url = getApiUrl("/srp-sellers", undefined, params, domain);
  return await apiRequest<SellerInfo[]>(
    url,
    "GET",
    undefined,
    {},
    true,
    request
  );
}
```

#### Get Item Options with Location

```typescript
export async function getItemOptionsAPI(
  mobileNumber: string | null,
  request: Request,
  sellerId?: number,
  deliveryDate?: string,
  categoryId?: number,
  matchBy?: string,
  parentCategoryId?: number,
  lat?: number,
  long?: number
): Promise<ApiResponse<ItemOptionsData>> {
  const params: {
    deliveryDate?: string;
    sellerId?: number;
    categoryId?: number;
    matchBy?: string;
    categoryParentId?: number;
    latitude?: number;
    longitude?: number;
  } = {};

  // Add location parameters for filtering
  if (lat) params.latitude = lat;
  if (long) params.longitude = long;

  const url = getApiUrl("/itemoptions", undefined, params, domain);
  return await apiRequest<ItemOptionsData>(
    url,
    "GET",
    undefined,
    {},
    true,
    request
  );
}
```

**Features:**

- Delivery date display with relative formatting
- Seller card display with selection handling
- Navigation to item selection with seller context

#### Multi-Outlet Bottom Sheet (`app/components/restaurant/MultiOutletBottomSheet.tsx`)

Modal component for outlet selection within the restaurant page:

```typescript
export default function MultiOutletBottomSheet({
  showOutletSheet,
  setShowOutletSheet,
  sellerList,
  itemOptionsData,
  handleOutletSelection
}: {
  showOutletSheet: boolean;
  setShowOutletSheet: (show: boolean) => void;
  sellerList: SellerInfo[];
  itemOptionsData: ItemOptionsData;
  handleOutletSelection: (seller: SellerInfo) => void;
}) {
  return (
    <BottomSheet
      isOpen={showOutletSheet}
      onClose={() => setShowOutletSheet(false)}
    >
      <div className="space-y-2 max-h-[65vh] overflow-y-auto">
        {sellerList.map((seller) => (
          <div
            key={seller.id}
            className={cn(
              "relative rounded-xl p-3 cursor-pointer transition-all duration-300 border-2",
              seller.id === itemOptionsData?.sellerId
                ? "border-primary bg-gradient-to-r from-primary/5 to-primary/10"
                : "border-gray-200 bg-white hover:border-primary/30"
            )}
            onClick={() => {
              setShowOutletSheet(false);
              handleOutletSelection(seller);
            }}
          >
            {/* Seller information display */}
          </div>
        ))}
      </div>
    </BottomSheet>
  );
}
```

#### Restaurant Outlet Delivery Info (`app/components/restaurant/RestaurantOutletDeliveryInfo.tsx`)

Header component showing current outlet with selection option:

```typescript
const RestaurantOutletDeliveryInfo: React.FC<
  RestaurantOutletDeliveryInfoProps
> = ({
  sellerList,
  estDeliveryTime,
  defaultAddress,
  onSellerClick,
  onAddressClick,
  onProfileClick
}) => {
  const { itemOptionsData } = chooseitemsStore((state) => state);
  const { networkConfig } = useAppConfigStore((state) => state);

  const currentSeller = sellerList.find(
    (seller) => seller.id === itemOptionsData?.sellerId
  );
  const currentSellerName =
    currentSeller?.name || itemOptionsData?.sellerName || "Select Outlet";

  return (
    <div className="bg-gradient-to-r from-primary to-primary-600 text-white p-4">
      <Button onClick={onSellerClick} className="bg-black bg-opacity-30">
        <Store size={18} className="text-white shrink-0" />
        <div className="text-sm font-medium">{currentSellerName}</div>
        {networkConfig?.multiSeller && (
          <ChevronDown className="w-4 h-4 text-white" />
        )}
      </Button>
    </div>
  );
};
```

### 5. Location Confirmation Modal (`app/components/location/LocationConfirmModal.tsx`)

Modal for requesting location permissions:

```typescript
const LocationConfirmModal: React.FC<LocationConfirmModalProps> = ({
  address = "Your current address",
  title = "Confirm Your Location",
  message = "We need your accurate location to",
  benefits = defaultBenefits,
  buttonText = "Update My Location",
  onUpdateLocation,
  className = ""
}) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg p-6 max-w-md w-full">
        <div className="flex flex-col items-center mb-4">
          <div className="bg-primary/10 p-4 rounded-full mb-2">
            <LocationPinIcon />
          </div>
          <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
        </div>

        <div className="text-center mb-6">
          <p className="text-gray-600 mb-4">{message}</p>
          <div className="space-y-3">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-3">
                {benefit.icon}
                <span className="text-sm text-gray-700">{benefit.text}</span>
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={onUpdateLocation}
          className="w-full bg-primary text-white py-3 rounded-lg font-medium"
        >
          {buttonText}
        </button>
      </div>
    </div>
  );
};
```

## Implementation Flow

### 1. Feature Activation

1. **Network Configuration Check** (`app/root.tsx`):

   ```typescript
   // Set multiSeller cookie based on networkConfig
   if (networkConfig?.multiSeller !== undefined) {
     headers.set(
       "Set-Cookie",
       `multiSeller=${networkConfig.multiSeller}; Path=/;`
     );
   }
   ```

2. **Server-Side Detection** (`app/utils/loader.ts`):
   ```typescript
   // Get multiSeller from cookie
   const multiSeller = request.headers
     .get("Cookie")
     ?.split(";")
     .find((cookie) => cookie.trim().startsWith("multiSeller="));
   isMultiSeller =
     typeof multiSeller === "string" && multiSeller.split("=")[1] === "true";
   ```

### 2. Location-Based Flow

1. **Location Access Required**:

   ```typescript
   if ((!lat || !long) && isMultiSeller) {
     return { error: "LOCATION_ACCESS_REQUIRED" };
   }
   ```

2. **Coordinate Retrieval**:
   ```typescript
   // Get lat, long from cookie
   const coordinates = request.headers
     .get("Cookie")
     ?.split(";")
     .find((cookie) => cookie.trim().startsWith("coordinates="));
   if (coordinates) {
     const decodedCoordinates = decodeURIComponent(coordinates.split("=")[1]);
     const { latitude, longitude } = JSON.parse(decodedCoordinates);
     lat = latitude;
     long = longitude;
   }
   ```

### 4. Restaurant Page Integration

1. **Seller List Loading** (`app/routes/home.rsrp.tsx`):

   ```typescript
   let sellerList: SellerInfo[] = [];
   if (lat && long && deliveryDate && isMultiSeller) {
     try {
       const sellerListResponse = await getSellerList(
         lat,
         long,
         deliveryDate,
         request
       );
       sellerList = sellerListResponse.data;
     } catch (error) {
       console.log("Error fetching seller list");
     }
   }
   ```

2. **Outlet Selection Handling**:
   ```typescript
   const handleOutletSelection = (seller: SellerInfo) => {
     // Update item options with new seller
     itemFetcher.submit(
       { sellerId: seller.id, deliveryDate, intent: "L1" },
       { method: "get", action: "/api/get-item-options" }
     );
   };
   ```

## Data Types

### SellerInfo Interface

```typescript
export interface SellerInfo {
  id: number;
  name: string;
  businessName: string;
  address: string;
  enabled: boolean;
  latitude: number;
  longitude: number;
  distance?: number;
  estimatedDeliveryTime?: number;
}
```

## Configuration Requirements

### Network Configuration

For multi-seller to be enabled:

```typescript
{
  "multiSeller": true,
  "networkType": "B2C",
  "ondcDomain": "RET11"
}
```

### Location Permissions

- Browser geolocation API access required
- Coordinates stored in cookies for server access
- Fallback handling for permission denied scenarios

## Usage Examples

### 1. Enabling Multi-Seller in Component

```typescript
function DeliveryOptions() {
  const { networkConfig } = useAppConfigStore();

  const handleDeliverySelection = (deliveryDate: string, sellerId?: number) => {
    if (networkConfig?.multiSeller) {
      navigate(`/selectseller?deliveryDate=${deliveryDate}`);
    } else {
      navigate(
        `/chooseitems?deliveryDate=${deliveryDate}&sellerId=${sellerId}`
      );
    }
  };
}
```

### 2. Location-Based Seller Filtering

```typescript
function SellerList() {
  const { latitude, longitude, request } = useCurrentLocation();

  useEffect(() => {
    if (latitude && longitude) {
      fetchSellers(latitude, longitude, deliveryDate);
    }
  }, [latitude, longitude]);
}
```

### 3. Outlet Selection UI

```typescript
function RestaurantPage() {
  const [showOutletSheet, setShowOutletSheet] = useState(false);
  const { networkConfig } = useAppConfigStore();

  return (
    <>
      <RestaurantOutletDeliveryInfo
        sellerList={sellerList}
        onSellerClick={() =>
          networkConfig?.multiSeller && setShowOutletSheet(true)
        }
      />

      {networkConfig?.multiSeller && (
        <MultiOutletBottomSheet
          showOutletSheet={showOutletSheet}
          setShowOutletSheet={setShowOutletSheet}
          sellerList={sellerList}
          handleOutletSelection={handleOutletSelection}
        />
      )}
    </>
  );
}
```

## Benefits

1. **Enhanced User Choice**: Multiple outlet options for better delivery times
2. **Location Optimization**: Nearest sellers prioritized for faster delivery
3. **Real-Time Availability**: Live seller status and item availability
4. **Seamless Integration**: Works with existing cart and order flow
5. **Network Flexibility**: Can be enabled/disabled per network configuration

## Troubleshooting

### Common Issues

1. **Location not detected**: Check browser permissions and HTTPS requirement
2. **No sellers found**: Verify coordinates are being sent to API
3. **Multi-seller not enabled**: Check network configuration `multiSeller` flag
4. **Outlet selection not working**: Verify seller list is populated

### Debug Steps

1. Check browser console for geolocation errors
2. Verify `coordinates` cookie is set with valid JSON
3. Check `multiSeller` cookie value
4. Validate API responses for seller list
5. Confirm network configuration in loader data
