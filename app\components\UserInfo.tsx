import React, { useState } from "react";
import ProfileUpdateBottomSheet from "./ProfileUpdateBottomSheet";

interface UserInfoProps {
  name: string | null | undefined;
  email: string | null | undefined;
  phone?: string | undefined;
  profileImage?: string;
}

export const UserInfo: React.FC<UserInfoProps> = ({
  name,
  email,
  phone,
  profileImage
}) => {
  const [imageError, setImageError] = React.useState(false);
  const [isEditProfileOpen, setIsEditProfileOpen] = useState(false);

  const displayName = name?.trim() || "Guest User";
  const displayEmail = email?.trim() || "Email not provided";

  const handleEditClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsEditProfileOpen(true);
  };

  return (
    <>
      <div className="relative flex flex-col items-start gap-2.5 p-4 bg-white rounded-[10px] w-full">
        <button
          onClick={handleEditClick}
          className="absolute right-4 top-4 text-[#fa4a0c] text-sm font-light text-right tracking-[0] leading-[normal] bg-transparent border-none cursor-pointer z-[1]"
        >
          edit
        </button>

        <div className="flex items-center gap-4 relative self-stretch w-full">
          {profileImage && !imageError ? (
            <img
              className="relative w-20 h-20 object-cover rounded-full bg-gray-100"
              alt={`${displayName}'s profile`}
              src={profileImage}
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center text-white text-3xl font-bold shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
              {displayName.charAt(0).toUpperCase()}
            </div>
          )}

          <div className="flex flex-col items-start gap-1.5 p-1 relative flex-1 grow">
            <div className="relative self-stretch mt-[-1.00px] font-semibold text-typography-700 text-md tracking-[0] leading-[normal]">
              {displayName}
            </div>

            <div
              className={`relative self-stretch font-normal text-[15px] tracking-[0] leading-[normal] ${
                email?.trim() ? "opacity-50 text-black" : "text-gray-400"
              }`}
            >
              {displayEmail}
            </div>

            <div className="relative w-fit opacity-50 font-normal text-black text-[15px] tracking-[0] leading-[normal]">
              {phone}
            </div>
          </div>
        </div>
      </div>

      <ProfileUpdateBottomSheet
        isOpen={isEditProfileOpen}
        onClose={() => setIsEditProfileOpen(false)}
        userData={{
          name: displayName,
          phone,
          email: displayEmail === "Email not provided" ? "" : displayEmail,
          profileImage
        }}
      />
    </>
  );
};
