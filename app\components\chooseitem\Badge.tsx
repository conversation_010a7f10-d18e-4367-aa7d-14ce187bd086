// Badge component for "Newly Added" and discount badges
import React from "react";
import { cn } from "~/utils/cn";

export const Badge: React.FC<{
  color: string;
  text: string;
  className?: string;
}> = ({ color, text, className }) => {
  const bgColor = color === "orange" ? "bg-orange-500" : "bg-blue-500";
  return (
    <div className={cn("rounded-bl-md px-2 text-[.62rem]", bgColor, className)}>
      {text}
    </div>
  );
};

export default Badge;
