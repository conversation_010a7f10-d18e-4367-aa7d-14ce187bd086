/**
 * Get a cookie value by name from request headers
 * @param request - The request object
 * @param cookieName - The name of the cookie to retrieve
 * @returns The cookie value or null if not found
 */
export const getCookie = (
  request: Request,
  cookieName: string
): string | null => {
  const cookieHeader = request.headers.get("Cookie");
  if (!cookieHeader) return null;

  const cookie = cookieHeader
    .split(";")
    .find((cookie) => cookie.trim().startsWith(`${cookieName}=`));

  if (!cookie) return null;

  return cookie.split("=")[1] || null;
};

/**
 * Get a cookie value and decode it as URI component
 * @param request - The request object
 * @param cookieName - The name of the cookie to retrieve
 * @returns The decoded cookie value or null if not found
 */
export const getDecodedCookie = (
  request: Request,
  cookieName: string
): string | null => {
  const cookieValue = getCookie(request, cookieName);
  if (!cookieValue) return null;

  try {
    return decodeURIComponent(cookieValue);
  } catch (error) {
    console.error(`Error decoding cookie ${cookieName}:`, error);
    return null;
  }
};

/**
 * Get a cookie value, decode it, and parse as J<PERSON>N
 * @param request - The request object
 * @param cookieName - The name of the cookie to retrieve
 * @returns The parsed JSON object or null if not found/invalid
 */
export const getJsonCookie = <T = unknown>(
  request: Request,
  cookieName: string
): T | null => {
  const decodedValue = getDecodedCookie(request, cookieName);
  if (!decodedValue) return null;

  try {
    return JSON.parse(decodedValue) as T;
  } catch (error) {
    console.error(`Error parsing JSON cookie ${cookieName}:`, error);
    return null;
  }
};

/**
 * Get the selectedSeller cookie and parse its JSON content
 * @param request - The request object
 * @returns Object with sellerId and deliveryDate or null if not found
 */
export const getSelectedSellerCookie = (
  request: Request
): { sellerId: string; deliveryDate: string } | null => {
  return getJsonCookie<{ sellerId: string; deliveryDate: string }>(
    request,
    "selectedSeller"
  );
};

/**
 * Get the multiSeller cookie value
 * @param request - The request object
 * @returns Boolean value or null if not found
 */
export const getMultiSellerCookie = (request: Request): boolean | null => {
  const value = getCookie(request, "multiSeller");
  return value === "true" ? true : value === "false" ? false : null;
};

/**
 * Get the coordinates cookie and parse its JSON content
 * @param request - The request object
 * @returns Object with latitude and longitude or null if not found
 */
export const getCoordinatesCookie = (
  request: Request
): { latitude: number; longitude: number } | null => {
  return getJsonCookie<{ latitude: number; longitude: number }>(
    request,
    "coordinates"
  );
};
