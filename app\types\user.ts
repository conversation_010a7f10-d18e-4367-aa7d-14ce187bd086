export interface UserDetails {
  userId: number;
  userName: string;
  businessName: string;
  mobileNumber: string;
  buyerId: number;
  sellerId: number | null;
  agentId: number | null;
  minVersion: number;
  roles: string[];
  cashWithUser: number;
}

export interface UserProfileDetails {
  userName: string;
  walletBalance: number;
  email: string;
  role: string;
  mobileNumber: string;
}

export interface DecodedToken {
  sub: string;
  lastName: string;
  address: string;
  mobileNumber: string;
  appInfo: {
    app: string;
    networkId: number;
    version: string;
  };
  roles: string[];
  iss: string;
  userDetails: UserDetails;
  firstName: string;
  nbf: number;
  exp: number;
  iat: number;
  email: string;
}

export interface WalletDetails {
  walletBalance: number;
  withdrawEnabled: boolean;
  addMoneyEnabled: boolean;
}

export interface Transaction {
  id: number;
  transactionTime: string; // ISO date string format
  creditValue: number;
  debitValue: number;
  balance: number;
  narration: string;
  note: string;
  transactionType: string;
  transactionId: number;
}

export interface WalletRecentTransaction {
  transaction: Transaction[];
}

export interface SupportTicket {
  ticketId: number;
  ticketType: string;
  createdDate: string;
  status: string;
  appSellerId: number;
  description: string;
  orderGroupId?: number;
  sellerName?: string;
}

export interface BuyerDetails {
  userId: number;
  userName: string;
  mobileNumber: string;
  enabled: boolean;
}

export interface Address {
  address: string;
  latitude: number;
  longitude: number;
}
