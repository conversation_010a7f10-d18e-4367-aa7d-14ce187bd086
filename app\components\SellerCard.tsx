// app/components/SellerCard.tsx

import React from "react";
import dayjs from "dayjs";
import { DeliveryOption } from "~/types";
import Button from "./Button";
import { WLApp } from "@utils/wlapp-settings";
import { PackageIcon } from "lucide-react";

interface SellerCardProps {
  deliveryOption: DeliveryOption;
  isToday: boolean;
  onAdd: () => void;
  onPress: (tabName: string) => void;
}

const SellerCard: React.FC<SellerCardProps> = ({
  deliveryOption,
  isToday,
  onAdd,
  onPress
}) => {
  const isPlaceOrderDisabled =
    !deliveryOption.bookingAllowed ||
    (WLApp.appSellerId !== 0 && deliveryOption.appSellerSellerOption === null);

  if (
    isToday &&
    !deliveryOption.bookingAllowed &&
    deliveryOption.completedOrderCount === 0 &&
    deliveryOption.pendingOrderCount === 0
  ) {
    return null;
  }

  return (
    <div className="p-6 rounded-lg bg-white my-4 shadow-md">
      <div className="flex justify-between items-center mb-4">
        {deliveryOption.displayDeliveryDate ? <div>
          <p className="text-sm font-semibold text-gray-700">
            {dayjs(deliveryOption.deliveryDate).format("dddd, DD MMM")}
          </p>
          <p className="text-xs font-normal text-blue-500 mt-2">
            {isToday ? "TODAY" : "TOMORROW"}
          </p>
        </div> : 
        <div>
          <PackageIcon className="w-9 h-9 text-gray-700" strokeWidth={1.4} />
        </div>}
        {isToday && !deliveryOption.bookingAllowed ? null : (
          <Button
            onClick={onAdd}
            disabled={isPlaceOrderDisabled}
            className={`flex items-center ${
              isToday ? "bg-misc-purple" : "bg-teal-500"
            } hover:${
              isToday ? "bg-misc-purpleDark" : "bg-teal-600"
            } text-white font-bold py-2 px-4 rounded-md`}
          >
            <div className={`flex items-center px-2 h-7 rounded-md`}>
              {/* Keeping text-xs for "PLACE ORDER" */}
              <p className="text-xs font-semibold text-white mr-1">
                PLACE ORDER
              </p>
              <div className="w-3 h-3 rounded-full bg-white flex items-center justify-center ml-1">
                <img src="/add_item.png" alt="Add Item" className="w-1 h-1.5" />
              </div>
            </div>
          </Button>
        )}
      </div>
      {(deliveryOption.pendingOrderCount > 0 ||
        deliveryOption.completedOrderCount > 0) && (
        <div className="space-y-3">
          {deliveryOption.pendingOrderItemCount > 0 && (
            <button
              className="flex items-center bg-red-100 hover:bg-red-200 rounded-md px-3 py-1 w-full transition duration-300"
              onClick={() => onPress("pending")}
            >
              <span className="text-sm font-light  text-red-600">
                {`${deliveryOption.pendingOrderCount} Orders Pending`}
              </span>
              {deliveryOption.pendingOrderCount > 0 && (
                <span className="text-sm font-light text-red-600">
                  ({deliveryOption.pendingOrderItemCount} items)
                </span>
              )}
            </button>
          )}
          {deliveryOption.completedOrderCount > 0 && (
            <button
              className="flex justify-between items-center bg-green-100 hover:bg-teal-200 rounded-md p-3 w-full transition duration-300"
              onClick={() => onPress("completed")}
            >
              <p className="text-sm text-teal-600">
                {`${deliveryOption.completedOrderCount} Orders Completed`}
              </p>
              <p className="text-sm text-teal-600">
                ({deliveryOption.completedOrderItemCount} items)
              </p>
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default SellerCard;
