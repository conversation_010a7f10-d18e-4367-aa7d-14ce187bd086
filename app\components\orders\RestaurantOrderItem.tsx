import { FC } from "react";
import { OrderItem as OrderItemType } from "~/types";
import { formatCurrency } from "~/utils/format";
import { DietaryImage } from "../common/DietaryImage";
interface OrderItemProps {
  item: OrderItemType;
}

const RestaurantOrderItem: FC<OrderItemProps> = ({ item }) => {
  return (
    <div className="py-4 first:pt-0 last:pb-0 flex flex-col text-xs">
      <div className="flex w-full gap-2">
      <div className="flex w-full gap-2 flex-col items-start justify-between">
        <div className="flex flex-col items-start gap-1">
        <div className="flex gap-2 items-center">
          <div>
            <DietaryImage dietary={item.diet} />
          </div>
          <p className="text-sm text-typography-800">{item.itemName}</p>
        </div>

        {item.addOns?.length > 0 && (
          <p className="text-xs text-typography-400 line-clamp-3">
            {item.addOns
              .map(
                (addOn) =>
                  `${addOn.name}: ${addOn.addOnItemList
                    .map((item) => item.name)
                    .join(", ")}`
              )
              .join("; ")}
          </p>
        )}
        </div>
        <div className="flex gap-2 items-center justify-between w-full">
          <div className="flex flex-row gap-1 items-center">
            <p className="text-sm text-primary  border border-primary rounded-md py-1 px-2 flex items-center justify-center bg-primary-50">
              {item.qty}
            </p>
            <p className="mx-1 text-sm font-light">{"x"}</p>
            <div className="flex gap-1 items-center">
              <p className="text-sm text-gray-800">
                {formatCurrency(item.price)}
              </p>
            </div>
          </div>
          
          </div>
          
        </div>
        <div className="flex gap-1 items-end pb-[5px]">
            {item.strikeOffAmount && item.strikeOffAmount > item.amount && (
              <p className="text-xs text-gray-500 line-through">
                {formatCurrency(item.strikeOffAmount)}
              </p>
            )}
            <p className="text-sm text-gray-800">
              {formatCurrency(item.amount)}
            </p>
          </div>
      </div>

      {(item.cancelledQty > 0 || item.returnedQty > 0) && (
        <div className="flex flex-row justify-between items-center text-xs mt-2 py-2 rounded-md   bg-gradient-to-r from-red-50 to-orange-50">
          {item.cancelledQty > 0 && (
            <div className="flex items-center px-2">
              <span className=" text-red-600">
                Cancelled qty : {item.cancelledQty} {item.unit}
              </span>
            </div>
          )}
          {item.returnedQty > 0 && (
            <div className="flex items-center px-2">
              <span className="text-orange-600">
                Returned qty : {item.returnedQty} {item.unit}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RestaurantOrderItem;
