import React from "react";

import { ItemCategoryDtos } from "~/types";
interface CategoryItemProps {
  category: ItemCategoryDtos;
  isSelected: boolean;
  onSelect: () => void;
}
export const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  isSelected,
  onSelect
}) => {
  return (
    <div
      tabIndex={0}
      role="button"
      onKeyDown={() => {}}
      className={`relative flex items-center w-full h-24 my-3 transition-all duration-1000 ease-in-out `}
      onClick={onSelect}
    >
      <button className=" w-full flex text-center flex-col items-center">
        <div
          className={`flex items-center justify-center w-12 h-12 rounded-full ${
            isSelected ? "bg-primary-50" : "bg-neutral-50"
          } relative overflow-hidden`}
        >
          <img src={category.picture} alt="" className={`absolute mx-1 p-2`} />
        </div>
        <p
          className={`text-center w-full mt-1 text-xs ${
            isSelected ? "text-primary font-semibold" : "font-normal text-typography-300"
          }`}
        >
          {category.name}
        </p>
      </button>
      <span
        className={`absolute right-0 bottom-0 w-1 rounded-md bg-primary transition-all duration-300 ease-in-out ${
          isSelected ? "h-full" : "h-0"
        }`}
      ></span>
    </div>
  );
};

export default CategoryItem;
