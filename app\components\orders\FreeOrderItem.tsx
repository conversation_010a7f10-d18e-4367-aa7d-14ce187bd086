import SupplierBadge from "../common/SupplierBadge";
import CustomImage from "../CustomImage";
import { OrderItem as OrderItemType } from "~/types";

interface freeItemProps {
  item: OrderItemType;
}

export function FreeOrderItem({ item }: freeItemProps): React.ReactNode {
  return (
    <div className="relative flex justify-between items-center py-3 backdrop-blur-md bg-[linear-gradient(88deg,#DFF9F8_0%,#FFF_100%)] rounded-md px-2 my-2 ">
      <div className="flex items-stretch gap-2">
        <CustomImage
          src={item.itemUrl}
          alt={item.itemName}
          className="w-12 h-12 bg-neutral-50 rounded-lg aspect-square border border-neutral-100 object-fill"
        />
        <div className="flex flex-col justify-between gap-1">
          <div className="flex flex-col gap-0">
            <h3 className="text-sm font-medium w-full overflow-hidden text-typography-700 line-clamp-1">
              {item.itemName}
            </h3>
            {item.packaging && (
              <div className="text-xs font-normal text-typography-200">
                {item.packaging}
              </div>
            )}
          </div>
          {item.qty > 0 && (
            <p className="text-xs font-light text-typography-300">
              {`${item.qty} ${item.unit} `}
            </p>
          )}
          {/* {item.supplier !== "" ? (
            <div className="mt-1">
              <SupplierBadge supplier={item.supplier ?? ""} />
            </div>
          ) : null} */}
        </div>
      </div>
      <span
        className={`${
          item.status === "Cancelled" || item.qty == 0
            ? "text-red-800"
            : "text-[#00B6BE]"
        } font-semibold`}
      >
        {`${
          item?.freeItem
            ? "Free"
            : item?.status === "Cancelled" || item?.qty == 0
            ? "Cancelled"
            : ""
        }`}
      </span>
    </div>
  );
}
