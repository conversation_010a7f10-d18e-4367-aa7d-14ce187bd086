import { RPPaymentDetails } from ".";

export interface RazorpayOrderPayload {
  amount: number;
  currency: string;
  receipt: string;
  notes?: string;
  customerName?: string;
  customerEmail?: string;
  customerContact?: string;
}

export interface RazorpayOrder {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  status: string;
  attempts: number;
  notes: string;
  created_at: number;
  razorpayKey: string;
  orderId: string;
}

export interface RazorpayPaymentVerificationPayload {
  razorpayOrderId: string;
  razorpayPaymentId: string;
  razorpaySignature: string;
}

export interface RazorpayPaymentVerification {
  status: "success" | "failure";
  message: string;
  orderId: string;
  paymentId: string;
}

export interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  order_id: string;
  handler: (response: RazorpayResponse) => void;
  prefill: {
    name: string;
    email: string;
    contact: string;
  };
  theme?: {
    color?: string;
  };
  modal: {
    ondismiss: () => void;
  };
}

export interface RazorpayResponse {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

export interface PaymentRequest {
  amount: number;
  currency?: string;
  receipt?: string;
  notes?: string | Record<string, string>;
  customerName?: string;
  customerEmail?: string;
  customerContact?: string;
  description?: string;
  preconfirmUid?: string;
}

// Order structure expected from the precheck response
export interface OrderDetails {
  id?: string;
  balancePayableAmount?: number;
  preconfirmUid?: string;
  buyerName?: string;
  buyerEmail?: string;
  buyerPhone?: string;
  sellerName?: string;
  sellerId?: number;
  buyerId?: number;
  // Using unknown instead of any for additional properties
  [key: string]:
    | string
    | number
    | boolean
    | undefined
    | null
    | Record<string, unknown>;
}

// Helper to transform API response to payment request format
export const precheckResponseToPaymentRequest = (
  order: OrderDetails
): PaymentRequest => {
  if (!order) return {} as PaymentRequest;

  return {
    id: order.id || "",
    amount: order.balancePayableAmount || 0,
    currency: "INR",
    receipt: `order_${Date.now()}`,
    preconfirmUid: order.preconfirmUid,
    customerName: order.buyerName || "",
    customerEmail: order.buyerEmail || "",
    customerContact: order.buyerPhone || "",
    description: `Payment for ${order.sellerName || "Purchase"}`,
    notes: JSON.stringify({
      sellerId: order.sellerId?.toString() || "",
      sellerName: order.sellerName || "",
      buyerId: order.buyerId?.toString() || ""
    })
  };
};

export interface PaymentInitiateRequest {
  amount: number;
  orderGroupId?: number;
  note?: string;
}

export interface RazorpayError {
  error: {
    code: string;
    description: string;
    source: string;
    step: string;
    reason: string;
  };
}

export interface RazorpayInstance {
  open: () => void;
  on: (event: string, callback: (response: RazorpayError) => void) => void;
}

export interface PaymentResponse {
  success?: boolean;
  message?: string;
  errors?: {
    [key: string]: string;
  };
  paymentUrl?: string;
  refId?: number | string;
  paymentStatus?: "SUCCESS" | "FAILED" | "PENDING" | "SUCCESS_NOT_CNF";
  razorpayOptions?: RPPaymentDetails;
}

export interface PaymentState {
  isOpen: boolean;
  isLoading: boolean;
  status: "idle" | "processing" | "success" | "failed" | "success_not_cnf";
  message: string;
  refId?: number;
  paymentUrl?: string;
  razorpayOptions?: RPPaymentDetails;
}

export interface UsePaymentOptions {
  onToast?: (message: string, type: "success" | "error") => void;
}
