export interface GroceryItem {
  value: string;
  label: string;
}

export const localGroceryList: GroceryItem[] = [
  // { value: "apple", label: "Apple (Fruit)" },
  // { value: "banana", label: "Banana (Fruit)" },
  // { value: "orange", label: "Orange (Fruit)" },
  // { value: "grapes", label: "Grapes (Fruit)" },
  // { value: "strawberries", label: "Strawberries (Fruit)" },
  // { value: "carrot", label: "Carrot (Vegetable)" },
  // { value: "broccoli", label: "Broccoli (Vegetable)" },
  // { value: "spinach", label: "Spinach (Vegetable)" },
  // { value: "potato", label: "Potato (Vegetable)" },
  // { value: "tomato", label: "Tomato (Vegetable)" },
  // { value: "milk", label: "Milk (Dairy)" },
  // { value: "cheese", label: "Cheese (Dairy)" },
  // { value: "butter", label: "Butter (Dairy)" },
  // { value: "yogurt", label: "Yogurt (Dairy)" },
  // { value: "cream", label: "Cream (Dairy)" },
  // { value: "rice", label: "Rice (Grain)" },
  // { value: "pasta", label: "Pasta (Grain)" },
  // { value: "bread", label: "Bread (Grain)" },
  // { value: "oats", label: "Oats (Grain)" },
  // { value: "flour", label: "Flour (Grain)" },
  // { value: "chips", label: "Chips (Snack)" },
  // { value: "cookies", label: "Cookies (Snack)" },
  // { value: "popcorn", label: "Popcorn (Snack)" },
  // { value: "nuts", label: "Nuts (Snack)" },
  // { value: "chocolate", label: "Chocolate (Snack)" }
];
