import { LoaderFunction, json } from "@remix-run/node";
import { requireAuth } from "~/utils/clientReponse";
import { getItemOptions } from "~/utils/loader";

export const loader: LoaderFunction = async ({ request }) => {
  // Ensure user is authenticated
  const auth = await requireAuth(request);
  if (auth && auth.authRequired) {
    return json({ success: false, error: "Authentication required" }, { status: 401 });
  }

  try {
    const { itemOptions, error } = await getItemOptions(request);
    if (error) {
      return json({ success: false, error }, { status: 400 });
    }

    if (!itemOptions?.data) {
      return json({ success: false, error: "No item options found" }, { status: 400 });
    }

    return json({
      success: true,
      itemOptionsData: itemOptions.data
    });

  } catch (error) {
    console.error("Error in get-item-options API:", error);
    return json({ success: false, error: "Failed to get item options" }, { status: 500 });
  }
};
