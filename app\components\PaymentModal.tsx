import { useCallback, useEffect, useState } from "react";
import { Loader2, CheckCircle2, XCir<PERSON> } from "lucide-react";
import { useNavigate } from "@remix-run/react";

const TIME_LEFT = 120; // 2 minutes in seconds

interface PaymentSuccessNotConfirmedProps {
  message: string;
  retryCallback?: () => void;
  refId?: number;
}

const PaymentSuccessNotConfirmed = ({
  message,
  retryCallback,
  refId
}: PaymentSuccessNotConfirmedProps) => {
  const navigate = useNavigate();
  const stopPropagation = useCallback(
    (e: React.MouseEvent | React.KeyboardEvent) => {
      e.stopPropagation();
    },
    []
  );

  return (
    <div className="flex flex-col items-center justify-center py-4">
      <div className="text-center mb-6">
        <h3 className="text-gray-900 font-semibold text-lg">{message}</h3>
      </div>
      <div className="text-center flex flex-col items-center gap-2">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold text-primary-500 mb-2">
            Payment Successful
          </h3>
          <div className="flex items-center gap-4 mb-4">
            <CheckCircle2 className="w-6 h-6 text-green-500" />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <h3 className="text-gray-500 text-lg font-semibold text-yellow-500">
            Order confirm
          </h3>
          <XCircle className="w-6 h-6 text-yellow-500" />
        </div>
        {refId && (
          <p className="text-sm text-gray-500 mt-2">Reference ID: {refId}</p>
        )}
      </div>

      <div className="mt-6">
        <button
          onClick={(e) => {
            stopPropagation(e);
            if (retryCallback) {
              retryCallback();
            } else {
              navigate(0);
            }
          }}
          className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
  );
};

interface PaymentModalProps {
  isOpen: boolean;
  isLoading: boolean;
  status: "idle" | "processing" | "success" | "failed" | "success_not_cnf";
  message: string;
  handleClose: () => void;
  onSuccess?: () => void;
  retryCallback?: () => void;
  refId?: number;
}

export const PaymentModal = ({
  isOpen,
  isLoading,
  status,
  message,
  handleClose,
  onSuccess,
  retryCallback,
  refId
}: PaymentModalProps) => {
  const [timeLeft, setTimeLeft] = useState(TIME_LEFT);

  // Stop propagation for all events in the modal
  const stopPropagation = useCallback(
    (e: React.MouseEvent | React.KeyboardEvent) => {
      e.stopPropagation();
    },
    []
  );

  useEffect(() => {
    // Lock body scroll when modal is open
    if (isOpen) {
      document.body.style.overflow = "hidden";
      // Reset timer when modal opens
      setTimeLeft(TIME_LEFT);
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  // Handle success state
  useEffect(() => {
    if (status === "success" && onSuccess) {
      const timer = setTimeout(() => {
        onSuccess();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [status, onSuccess]);

  // Timer effect
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isOpen && status === "processing" && timeLeft > 0) {
      intervalId = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(intervalId);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    // Clear timer when status changes to failed
    if (status === "failed") {
      setTimeLeft(0);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isOpen, status, timeLeft]);

  if (!isOpen) return null;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const showCloseButton = status === "failed" || timeLeft === 0;

  console.log("PaymentModal - Status:", status);
  console.log("PaymentModal - onSuccess prop:", !!onSuccess);

  return (
    <div
      role="presentation"
      className="fixed inset-0 z-[100]"
      onClick={stopPropagation}
      onKeyDown={stopPropagation}
    >
      <div className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>

      <div className="flex items-center justify-center h-full">
        <div
          role="dialog"
          aria-modal="true"
          className="bg-white rounded-lg p-6 w-[90%] max-w-md relative z-10"
        >
          {status === "success_not_cnf" ? (
            <PaymentSuccessNotConfirmed
              message={message}
              retryCallback={retryCallback}
              refId={refId}
            />
          ) : (
            <div className="flex flex-col items-center justify-center py-4">
              {(isLoading || status === "processing") && (
                <Loader2 className="w-16 h-16 text-teal-500 animate-spin mb-4" />
              )}

              {status === "success" && !isLoading && (
                <CheckCircle2 className="w-16 h-16 text-green-500 mb-4 animate-bounce" />
              )}

              {status === "failed" && !isLoading && (
                <XCircle className="w-16 h-16 text-red-500 mb-4" />
              )}

              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {status === "success"
                    ? "Payment Successful!"
                    : status === "failed"
                    ? "Payment Failed"
                    : isLoading
                    ? "Processing Payment"
                    : "Initiating Payment"}
                </h3>
                <p className="text-gray-600">{message}</p>
                {refId && (
                  <p className="text-sm text-gray-500 mt-2">
                    Reference ID: {refId}
                  </p>
                )}
              </div>

              {status === "processing" && timeLeft > 0 && (
                <div className="mt-4 text-sm text-gray-500">
                  Time remaining: {formatTime(timeLeft)}
                </div>
              )}

              {(showCloseButton || timeLeft === 0) && (
                <div className="mt-6 flex gap-3">
                  <button
                    onClick={(e) => {
                      stopPropagation(e);
                      handleClose();
                    }}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Close
                  </button>
                  {status === "failed" && (
                    <button
                      onClick={(e) => {
                        stopPropagation(e);
                        handleClose();
                        if (retryCallback) {
                          retryCallback();
                        } else {
                          window.location.reload();
                        }
                      }}
                      className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                    >
                      Try Again
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
