
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogOverlay,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";

/**
 * InfoModel2 - A modal component using shadcn dialog
 *
 * Features:
 * - Opens automatically upon rendering
 * - Customizable icon, title, message, and button
 *
 * Example usage:
 * ```tsx
 * import { Info, CheckCircle, AlertTriangle } from "lucide-react";
 *
 * <InfoModel2
 *   title="Success!"
 *   message="Your action was completed successfully."
 *   icon={<CheckCircle />}
 *   buttonType="primary"
 *   buttonText="Continue"
 *   onClose={() => console.log('Modal closed')}
 * />
 * ```
 */
interface InfoModel2Props {
  title: string;
  message: string;
  icon: React.ReactNode;
  buttonType?: "primary" | "secondary" | "destructive" | "outline" | "ghost";
  buttonText: string;
  onClose: () => void;
}

const InfoModel2: React.FC<InfoModel2Props> = ({
  title,
  message,
  icon,
  buttonType = "primary",
  buttonText,
  onClose
}) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(true);
  }, [title, message]);

  const handleClose = () => {
    setIsOpen(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        handleClose();
      }
    }}>
      <DialogOverlay className="fixed inset-0 z-[100] bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
      <DialogContent
        className="fixed left-[50%] top-[50%] z-[100] grid w-full max-w-[400px] translate-x-[-50%] translate-y-[-50%] gap-4 bg-white p-0 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg border-0"
        showX={false}
      >
        <div className="flex flex-col items-center text-center p-5">
          {/* Icon */}
          {icon && (
            <div className="mb-4 p-2 rounded-full bg-primary/10 text-primary">
              {React.cloneElement(icon as React.ReactElement, {
                size: 32,
                className: "text-primary"
              })}
            </div>
          )}

          {/* Header */}
          <DialogHeader className="space-y-2 mb-6 sm:text-center">
            {title && (
              <DialogTitle className="text-xl font-semibold text-gray-900 leading-tight">
                {title}
              </DialogTitle>
            )}
            {message && (
              <DialogDescription className="text-gray-600 text-base leading-relaxed">
                {message}
              </DialogDescription>
            )}
          </DialogHeader>

          {/* Footer */}
          <DialogFooter className="w-full">
            <Button
              onClick={handleClose}
              size="lg"
              variant={buttonType === "primary" ? "default" : buttonType as any}
              className="w-full text-base font-medium rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
            >
              {buttonText}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InfoModel2;