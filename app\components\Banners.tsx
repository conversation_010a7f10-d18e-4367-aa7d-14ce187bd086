import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules";
import type { SwiperProps } from "swiper/react";
import type { AutoplayOptions, PaginationOptions } from "swiper/types";
import { cn } from "~/utils/cn";

interface BannersProps {
  images: string[];
  autoplay?: boolean;
  pagination?: boolean;
  imageClassName?: string;
  className?: string;
  swiperConfig?: Partial<SwiperProps>;
}

const defaultSwiperConfig: Partial<SwiperProps> = {
  centeredSlides: true,
  spaceBetween: 10,
  slidesPerView: 1,
  loop: true,
  breakpoints: {
    640: {
      slidesPerView: 2.5
    },
    768: {
      slidesPerView: 2.5
    },
    1024: {
      slidesPerView: 2.5
    }
  },
  autoplay: {
    delay: 3000,
    disableOnInteraction: false
  }
};

const Banners: React.FC<BannersProps> = ({
  autoplay = true,
  pagination = true,
  images = [],
  className,
  imageClassName,
  swiperConfig = {}
}) => {
  const modules = [];

  if (autoplay) {
    modules.push(Autoplay);
  }

  if (pagination) {
    modules.push(Pagination);
  }

  if (images.length === 1) {
    return (
      <div className={cn("", className)}>
        <img
          src={images[0]}
          alt={""}
          className={cn(
            "rounded-xl w-full max-h-44 object-fill",
            imageClassName
          )}
        />
      </div>
    );
  }

  if (images.length === 0) {
    return <></>;
  }

  const finalSwiperConfig: Partial<SwiperProps> = {
    ...defaultSwiperConfig,
    ...swiperConfig,
    modules,
    pagination: pagination
      ? {
          clickable: true,
          ...((swiperConfig.pagination as Partial<PaginationOptions>) || {})
        }
      : false,
    autoplay: autoplay
      ? {
          ...(defaultSwiperConfig.autoplay as AutoplayOptions),
          ...((swiperConfig.autoplay as Partial<AutoplayOptions>) || {})
        }
      : false
  };

  return (
    <div className={cn("", className)}>
      <Swiper {...finalSwiperConfig} className="mySwiper">
        {images.map((image, index) => (
          <SwiperSlide key={index}>
            <img
              src={image}
              alt={""}
              className={cn(
                "rounded-xl w-full max-h-44 object-fill",
                imageClassName
              )}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Banners;
