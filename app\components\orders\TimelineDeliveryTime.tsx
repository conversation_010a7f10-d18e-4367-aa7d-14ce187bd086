import { Truck } from "lucide-react";
import { FC } from "react";

interface TimelineDeliveryTimeProps {
  label: string;
  time: string;
}

const TimelineDeliveryTime: FC<TimelineDeliveryTimeProps> = ({
  label,
  time
}) => {
  return (
    <div className="flex items-center text-xs tracking-wide text-gray-500 mb-6">
      <Truck className="w-4 h-4 mr-1 text-primary-600" />
      {label}
      {","}
      <span className="text-blue-600 ml-1">{time}</span>
    </div>
  );
};

export default TimelineDeliveryTime;
