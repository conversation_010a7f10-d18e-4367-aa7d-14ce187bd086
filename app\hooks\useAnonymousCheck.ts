import { useRouteLoaderData } from "@remix-run/react";
import { useLoginStore } from "~/stores/login.store";

interface RootLoaderData {
  isAnonymous?: boolean;
  networkConfig?: any;
  decode?: any;
  minVersion?: number;
}

/**
 * Hook to check if the current user is anonymous and provide login functionality
 * @returns Object with isAnonymous status and requireRealAuth function
 */
export function useAnonymousCheck() {
  const rootData = useRouteLoaderData<RootLoaderData>("root");
  const { openLogin } = useLoginStore();

  const isAnonymous = rootData?.isAnonymous || false;

  /**
   * Function to check if user is anonymous and show login modal if needed
   * Use this before performing actions that require real authentication
   * @returns true if user is real (can proceed), false if anonymous (login modal shown)
   */
  const requireRealAuth = (): boolean => {
    if (isAnonymous) {
      openLogin(true);
      return true;
    }
    return false;
  };

  return {
    isAnonymous,
    requireRealAuth
  };
}
