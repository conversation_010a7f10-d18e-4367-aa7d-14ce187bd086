import { FC, ReactNode } from "react";

interface BillRowProps {
  label: string;
  value: string | number;
  valueColor?: string;
  isTotal?: boolean;
  children?: ReactNode;
  className?: string;
}

const BillRow: FC<BillRowProps> = ({
  label,
  value,
  valueColor = "text-gray-900",
  isTotal = false,
  children,
  className = ""
}) => {
  return (
    <div className={`flex items-center justify-between py-2 ${className}`}>
      <span
        className={`text-xs ${
          isTotal ? "text-gray-900" : "font-light text-gray-600"
        }  ${label === "GST & Other Charges" ? "underline underline-offset-[3px] decoration-dashed decoration-[1px]" : ""}`}
      >
        {label}
      </span>
      {children || (
        <span className={`text-xs ${valueColor}`}>
          {typeof value === "number" ? (value >= 0 ? `₹ ${value}` : `- ₹ ${-1* value}`  ) : value}
        </span>
      )}
    </div>
  );
};

export default BillRow;
