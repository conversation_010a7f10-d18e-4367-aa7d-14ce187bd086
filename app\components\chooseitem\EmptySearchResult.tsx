import React from "react";

interface EmptySearchResultProps {
  searchStr: string;
  selectedCategoryId: number | undefined;
}
export const EmptySearchResult: React.FC<EmptySearchResultProps> = ({
  searchStr,
  selectedCategoryId
}) => {
  let messageText = "No Items Found. Please try after sometime.";
  let messageTitle = "Uh-oh!";

  if (selectedCategoryId === -2) {
    messageText = "Place your first order to get started!";
  } else if (searchStr) {
    messageText = `No results for "${searchStr}". Please try something else.`;
  }

  if (selectedCategoryId === -2) {
    messageTitle = "Uh-oh, No Orders Found!";
  }

  return (
    <div className="flex flex-col items-center justify-center  mt-40 mx-2 text-center">
      <p className="text-sm font-medium text-gray-800 mb-4 ">{messageTitle}</p>
      <p className="text-sm font-light text-gray-600 text-center">
        {messageText}
      </p>
    </div>
  );
};

export default EmptySearchResult;
