import React, { useEffect, useState } from "react";
import { GroceryItem, localGroceryList } from "~/constants/searchItems";
import { SearchableDropdown } from "~/components/common/SearchableDropdown";

// TODO: 1. handle clear focus, 2. set focus to last entered search
const DEBOUNCE_DELAY = 500; // 300ms debounce delay

const SearchItems: React.FC<{
  searchValue: string;
  onClick: () => void;
  onSelect: (value: string) => void;
  onClear: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
}> = ({
  searchValue,
  className,
  onSelect,
  onClick,
  onClear,
  onFocus,
  onBlur
}) => {
  const [filteredOptions, setFilteredOptions] = useState<GroceryItem[]>([]);
  const [debounceTimeout, setDebounceTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  const handleInputChange = (inputValue: string) => {
    if (inputValue.length > 0) {
      const filtered = localGroceryList.filter((item) =>
        item.label.toLowerCase().includes(inputValue.toLowerCase())
      );

      if (
        !filtered.some(
          (item) => item.label.toLowerCase() === inputValue.toLowerCase()
        )
      ) {
        filtered.unshift({
          value: inputValue,
          label: inputValue
        });
      }

      setFilteredOptions(filtered);
    } else {
      setFilteredOptions([]);
    }
  };

  const debouncedClear = () => {
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }

    const timeoutId = setTimeout(() => {
      onClear();
    }, DEBOUNCE_DELAY);

    setDebounceTimeout(timeoutId);
  };

  useEffect(() => {
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [debounceTimeout]);

  return (
    <SearchableDropdown
      value={searchValue}
      options={filteredOptions}
      onSelect={onSelect}
      onClear={debouncedClear}
      onClick={onClick}
      onFocus={onFocus}
      onBlur={onBlur}
      onInputChange={handleInputChange}
      className={className}
      placeholder="Search items..."
    />
  );
};

export default SearchItems;
