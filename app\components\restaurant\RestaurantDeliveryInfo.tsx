import { But<PERSON> } from "@headlessui/react";
// import dayjs from "dayjs";
import React from "react";
import TruncatedText from "../TruncatedText";
import { useNavigate } from "@remix-run/react";
import { AddressDto } from "~/types/address.types";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import {
  ChevronDown,
  CircleUserRound,
  MapPin,
  User,
  UserCircle,
  UserRound
} from "lucide-react";
import { useAppConfigStore } from "~/stores/appConfig.store";

interface NotDeliveringMessageProps {
  onAddressClick: () => void;
  visible?: boolean;
}

const NotDeliveringMessage: React.FC<NotDeliveringMessageProps> = ({
  onAddressClick,
  visible = true
}) => {
  if (!visible) return null;

  return (
    <Button className="w-full" onClick={onAddressClick}>
      <div className="flex flex-col w-full items-start gap-2 rounded-md bg-orange-100 p-2 text-black mt-2">
        <div className="text-xs font-bold p-2 bg-orange-800 rounded-md text-white">
          NOT DELIVERING
        </div>
        <p className="text-xs font-light">
          {"We do not deliver in your area since it's far away."}
        </p>
      </div>
    </Button>
  );
};

interface SellerProps {
  defaultAddress: AddressDto | undefined;
  estDeliveryTime: number;
  onAddressClick?: () => void;
  onProfileClick?: () => void;
}

const RestaurantDeliveryInfo: React.FC<SellerProps> = ({
  defaultAddress,
  estDeliveryTime,
  onAddressClick,
  onProfileClick
}) => {
  const navigate = useNavigate();
  const { itemOptionsData } = chooseitemsStore((state) => state);
  const { appDomain } = useAppConfigStore((state) => state);

  const handleAddressClick = () => {
    if (onAddressClick) {
      onAddressClick();
    } else {
      if (appDomain === "RET11") {
        navigate(`/changeaddress?redirectTo=/home/<USER>/rsrp`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      } else {
        navigate(`/changeaddress?redirectTo=/chooseitems`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      }
    }
  };
  return (
    <>
      <div className="flex flex-row justify-between p-3 pt-4 pb-1">
        <div className="flex flex-col gap-1 flex-1 min-w-0">
          <div className="text-sm text-white">
            Delivery in
          </div>
          <div className="text-white text-[1.125rem] leading-5 font-bold tracking-wider">
            {estDeliveryTime ? `${estDeliveryTime} minutes` : "30 - 45 mins"}
          </div>
          <Button
            className="flex flex-row items-center gap-1 pr-6 pt-0.5 text-sm text-white w-full"
            onClick={handleAddressClick}
          >
            <MapPin size={20} className="text-white shrink-0" />
            {
              defaultAddress?.address ? (
                <div className="flex items-center overflow-hidden min-w-0">
                  {defaultAddress?.name && (
                    <span className="font-semibold whitespace-nowrap">
                      {defaultAddress.name.toUpperCase().slice(0, 8)}{defaultAddress.name.length > 8 ? '...' : ''}&nbsp;-&nbsp;
                    </span>
                  )}
                  <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                    {defaultAddress?.address}
                  </span>
                </div>
              ) : (
                <div className="flex items-center overflow-hidden min-w-0 mr-2">
                  <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                    Add a delivery address
                  </span>
                </div>
              )
            }
            <ChevronDown className="w-5 h-5 text-white shrink-0" />
          </Button>
        </div>

        <Button
          onClick={onProfileClick}
          className="rounded-full bg-black bg-opacity-30 p-3 h-fit my-auto ml-4 shrink-0"
        >
          <UserRound size={20} fill="#FFF" strokeWidth={0} />
        </Button>
      </div>
    </>
  );
};

export default RestaurantDeliveryInfo;
