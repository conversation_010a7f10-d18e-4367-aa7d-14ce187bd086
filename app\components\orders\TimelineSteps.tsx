import { FC } from "react";

export interface TimelineStep {
  label: string;
  status: "completed" | "active" | "pending";
}

interface TimelineStepsProps {
  steps: TimelineStep[];
}

const TimelineSteps: FC<TimelineStepsProps> = ({ steps }) => {
  return (
    <div className="relative z-0 w-full px-4 sm:px-6">
      <div className="flex justify-between items-center min-w-[280px]">
        {steps.map((step, index) => (
          <div
            key={step.label}
            className="flex flex-col items-center relative w-full last:w-auto"
          >
            {/* Connecting Line */}
            {index < steps.length && (
              <div
                className={`absolute w-full h-0.5 top-2 -translate-y-1/2 ${
                  step.status === "completed" ? "bg-teal-500" : "bg-gray-200"
                }`}
              />
            )}
            {/* Status Circle */}
            <div
              className={`w-4 h-4 rounded-full z-10 flex items-center justify-center shrink-0 ${
                step.status === "completed"
                  ? "bg-teal-500"
                  : step.status === "active"
                  ? "bg-teal-500"
                  : "bg-gray-200"
              }`}
            >
              {step.status === "completed" && (
                <svg
                  className="w-3 h-3 text-white"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              )}
            </div>
            <span className="text-xs mt-2 text-gray-500 text-center w-16 break-words leading-tight">
              {step.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TimelineSteps;
