import { useEffect } from "react";
import { NetworkConfig } from "~/types";

// Define the type for the heap function
interface HeapAnalytics {
  identify: (userId: string | number) => void;
  addUserProperties: (properties: Record<string, string | number>) => void;
  track: (event: string, properties?: Record<string, unknown>) => void;
  load: (envId: string, config?: Record<string, unknown>) => void;
  [key: string]: unknown;
}

// Extend the Window interface to include heap
declare global {
  interface Window {
    heap?: HeapAnalytics;
    heapReadyCb?: Array<{ name: string; fn: () => void }>;
  }
}

interface UserProperties {
  userId: string | number;
  userName?: string;
  mobileNumber?: string;
  buyerId?: string | number;
}

/**
 * Custom hook to initialize Heap Analytics after page render
 */
export const useHeapAnalytics = (
  userProperties?: UserProperties,
  networkConfig?: NetworkConfig
): void => {
  useEffect(() => {
    // Prevent initialization in non-browser environments
    if (typeof window === "undefined") return;

    // Prevent duplicate initialization
    if (window.heap) return;

    const ondcDomain = networkConfig?.ondcDomain;

    console.log("ondcDomain", ondcDomain);

    // Create and append Heap script
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.async = true;

    if (ondcDomain === "RET11") {
      script.innerHTML = `window.heapReadyCb=window.heapReadyCb||[],window.heap=window.heap||[],heap.load=function(e,t){window.heap.envId=e,window.heap.clientConfig=t=t||{},window.heap.clientConfig.shouldFetchServerConfig=!1;var a=document.createElement("script");a.type="text/javascript",a.async=!0,a.src="https://cdn.us.heap-api.com/config/"+e+"/heap_config.js";var r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(a,r);var n=["init","startTracking","stopTracking","track","resetIdentity","identify","getSessionId","getUserId","getIdentity","addUserProperties","addEventProperties","removeEventProperty","clearEventProperties","addAccountProperties","addAdapter","addTransformer","addTransformerFn","onReady","addPageviewProperties","removePageviewProperty","clearPageviewProperties","trackPageview"],i=function(e){return function(){var t=Array.prototype.slice.call(arguments,0);window.heapReadyCb.push({name:e,fn:function(){heap[e]&&heap[e].apply(heap,t)}})}};for(var p=0;p<n.length;p++)heap[n[p]]=i(n[p])};
  heap.load("**********");`;
    } else if (ondcDomain === "RET10") {
      script.innerHTML = `
      window.heapReadyCb=window.heapReadyCb||[],window.heap=window.heap||[],heap.load=function(e,t){window.heap.envId=e,window.heap.clientConfig=t=t||{},window.heap.clientConfig.shouldFetchServerConfig=!1;var a=document.createElement("script");a.type="text/javascript",a.async=!0,a.src="https://cdn.us.heap-api.com/config/"+e+"/heap_config.js";var r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(a,r);var n=["init","startTracking","stopTracking","track","resetIdentity","identify","getSessionId","getUserId","getIdentity","addUserProperties","addEventProperties","removeEventProperty","clearEventProperties","addAccountProperties","addAdapter","addTransformer","addTransformerFn","onReady","addPageviewProperties","removePageviewProperty","clearPageviewProperties","trackPageview"],i=function(e){return function(){var t=Array.prototype.slice.call(arguments,0);window.heapReadyCb.push({name:e,fn:function(){heap[e]&&heap[e].apply(heap,t)}})}};for(var p=0;p<n.length;p++)heap[n[p]]=i(n[p])};
      heap.load("**********");
    `;
    }

    document.head.appendChild(script);

    // Set up user properties if provided
    if (userProperties?.userId) {
      // Wait a bit for heap to initialize before setting properties
      setTimeout(() => {
        if (window.heap?.identify) {
          window.heap.identify(userProperties.userId);

          if (
            userProperties.userName ||
            userProperties.mobileNumber ||
            userProperties.buyerId
          ) {
            window.heap.addUserProperties({
              Name: userProperties.userName || "",
              "Mobile Number": userProperties.mobileNumber || "",
              "Buyer Id": userProperties.buyerId || ""
            });
          }
        }
      }, 100);
    }
  }, [userProperties, networkConfig]);
};
