import { useEffect } from "react";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { AppConfig, AppSource } from "~/types/app";

export const useUpdateAppConfig = ({
  appSource = "buyer-app",
  appConfig
}: {
  appSource?: AppSource;
  appConfig?: Partial<AppConfig>;
}) => {
  const updateAppConfig = useAppConfigStore((state) => state.setAppConfig);
  const updateAppSource = useAppConfigStore((state) => state.setAppSource);

  useEffect(() => {
    if (appConfig) {
      updateAppConfig(appConfig || {});
    }
    if (appSource) {
      updateAppSource(appSource || "buyer-web");
    }
  }, [appSource, appConfig, updateAppConfig, updateAppSource]);
};
