import { forwardRef } from "react";
import { Order, OrderItem } from "~/types";
import { useAppConfigStore } from "~/stores/appConfig.store";
import dayjs from "dayjs";

interface InvoicePdfProps {
  orderDetails: Order;
  items?: Partial<OrderItem>[];
}

const InvoicePdf = forwardRef<HTMLDivElement, InvoicePdfProps>(
  ({ orderDetails, items = [] }, ref) => {
    const { networkConfig } = useAppConfigStore();

    const getFormattedDate = (date: string) => {
      return dayjs(date).format("DD MMM YYYY");
    };

    // Header Section Component
    const Header = () => (
      <div
        className="invoice-header"
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          alignItems: "start",
          justifyContent: "space-between",
          marginBottom: "1rem"
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "start",
            gap: "1rem",
            width: "65%",
            maxWidth: "65%"
          }}
        >
          <div style={{ width: "130px", height: "130px", overflow: "hidden" }}>
            <img
              src={networkConfig?.businessLogo || "/images/logo.png"}
              alt="Business Logo"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "contain",
                objectPosition: "top left"
              }}
            />
          </div>
          <div>
            <h1
              style={{
                fontWeight: "600",
                color: "#333843",
                lineHeight: "140%"
              }}
            >
              {orderDetails.sellerName}
            </h1>
            <p
              style={{
                fontSize: "0.875rem",
                lineHeight: "132%",
                color: "#667085"
              }}
            >
              {orderDetails.farmers?.[0]?.sellerAddress || "Seller Address"}
            </p>
            <p
              style={{
                fontSize: "0.875rem",
                lineHeight: "132%",
                color: "#667085"
              }}
            >
              Phone: {orderDetails.sellerContactNumber}
            </p>
          </div>
        </div>
        <div
          style={{
            width: "30%",
            maxWidth: "30%",
            gap: "1.5rem",
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            alignItems: "end"
          }}
        >
          <div
            style={{
              fontSize: "0.875rem",
              fontWeight: "600",
              width: "fit-content",
              color: "#333843",
              backgroundColor: "#F4F5F6",
              padding: "0.5rem 1rem",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: "8px"
            }}
          >
            #{orderDetails.id}
          </div>
          <div>
            <p
              style={{
                fontSize: "0.875rem",
                fontWeight: "500",
                lineHeight: "132%",
                color: "#667085",
                whiteSpace: "nowrap",
                textAlign: "right"
              }}
            >
              Total Amount
            </p>
            <p
              style={{
                fontSize: "1.25rem",
                fontWeight: "700",
                color: "#333843",
                marginTop: "0.25rem",
                textAlign: "right"
              }}
            >{`₹ ${orderDetails.totalAmount.toFixed(2)}`}</p>
          </div>
        </div>
      </div>
    );

    // Main Section - 1. Order Dates and Details Component
    const OrderDetails = () => (
      <div
        className="order-details"
        style={{
          width: "28%",
          minWidth: "28%",
          backgroundColor: "#FAFAFA",
          borderRadius: "0.5rem",
          padding: "1rem",
          display: "flex",
          flexDirection: "column",
          gap: "0.75rem"
        }}
      >
        <div>
          <p
            style={{
              fontSize: "0.875rem",
              fontWeight: "500",
              lineHeight: "132%",
              color: "#667085",
              whiteSpace: "nowrap"
            }}
          >
            Bill Date
          </p>
          <p
            style={{
              fontWeight: "600",
              lineHeight: "140%",
              color: "#333843",
              marginTop: "0.25rem"
            }}
          >
            {getFormattedDate(orderDetails.createdOn)}
          </p>
        </div>
        <div>
          <p
            style={{
              fontSize: "0.875rem",
              fontWeight: "500",
              lineHeight: "132%",
              color: "#667085",
              whiteSpace: "nowrap"
            }}
          >
            Delivery Date
          </p>
          <p
            style={{
              fontWeight: "600",
              lineHeight: "140%",
              color: "#333843",
              marginTop: "0.25rem"
            }}
          >
            {getFormattedDate(orderDetails.deliveryDate)}
          </p>
        </div>
        <div>
          <p
            style={{
              fontSize: "0.875rem",
              fontWeight: "500",
              lineHeight: "132%",
              color: "#667085",
              whiteSpace: "nowrap"
            }}
          >
            Payment Status
          </p>
          <p
            style={{
              fontWeight: "600",
              lineHeight: "140%",
              color: "#333843",
              marginTop: "0.25rem"
            }}
          >
            {orderDetails.balanceTobePaid > 0 ? "Payment Pending" : "Paid"}
          </p>
        </div>
      </div>
    );

    // Main Section - 2. Billing Details Component
    const BillingDetails = () => (
      <div className="billing-details">
        <p
          style={{
            fontSize: "0.875rem",
            lineHeight: "132%",
            color: "#667085"
          }}
        >
          Billing Address
        </p>
        <p
          style={{
            fontWeight: "600",
            lineHeight: "140%",
            color: "#333843",
            marginTop: "0.5rem"
          }}
        >
          {orderDetails.buyerName ? orderDetails.buyerName : ""}
        </p>
        <p
          style={{
            fontSize: "0.875rem",
            lineHeight: "132%",
            color: "#667085",
            marginTop: "0.5rem"
          }}
        >
          {orderDetails.buyerAddress}
        </p>
      </div>
    );

    // Main Section - 3. Items Table Component
    const ItemsTable = () => (
      <div className="items-table" style={{ marginBottom: "1rem" }}>
        <table
          style={{
            width: "100%",
            borderCollapse: "collapse"
          }}
        >
          <thead>
            <tr style={{ backgroundColor: "#FAFAFA" }}>
              <th
                style={{
                  textAlign: "center",
                  padding: "0.75rem",
                  borderTop: "1px solid #E0E2E7",
                  borderBottom: "1px solid #E0E2E7",
                  fontSize: "0.75rem",
                  fontWeight: "500",
                  color: "#667085"
                }}
              >
                NO
              </th>
              <th
                style={{
                  textAlign: "center",
                  padding: "0.75rem",
                  borderTop: "1px solid #E0E2E7",
                  borderBottom: "1px solid #E0E2E7",
                  fontSize: "0.75rem",
                  fontWeight: "500",
                  color: "#667085"
                }}
              >
                ITEM
              </th>
              <th
                style={{
                  textAlign: "center",
                  padding: "0.75rem",
                  borderTop: "1px solid #E0E2E7",
                  borderBottom: "1px solid #E0E2E7",
                  fontSize: "0.75rem",
                  fontWeight: "500",
                  color: "#667085"
                }}
              >
                QUANTITY
              </th>
              <th
                style={{
                  textAlign: "center",
                  padding: "0.75rem",
                  borderTop: "1px solid #E0E2E7",
                  borderBottom: "1px solid #E0E2E7",
                  fontSize: "0.75rem",
                  fontWeight: "500",
                  color: "#667085",
                  whiteSpace: "nowrap"
                }}
              >
                UNIT PRICE
              </th>
              <th
                style={{
                  textAlign: "center",
                  padding: "0.75rem",
                  borderTop: "1px solid #E0E2E7",
                  borderBottom: "1px solid #E0E2E7",
                  fontSize: "0.75rem",
                  fontWeight: "500",
                  color: "#667085"
                }}
              >
                AMOUNT
              </th>
              <th
                style={{
                  textAlign: "center",
                  padding: "0.75rem",
                  borderTop: "1px solid #E0E2E7",
                  borderBottom: "1px solid #E0E2E7",
                  fontSize: "0.75rem",
                  fontWeight: "500",
                  color: "#667085"
                }}
              >
                DISCOUNT
              </th>
              <th
                style={{
                  textAlign: "center",
                  padding: "0.75rem",
                  borderTop: "1px solid #E0E2E7",
                  borderBottom: "1px solid #E0E2E7",
                  fontSize: "0.75rem",
                  fontWeight: "500",
                  color: "#667085",
                  whiteSpace: "nowrap"
                }}
              >
                FINAL AMOUNT
              </th>
            </tr>
          </thead>
          <tbody>
            {items.length > 0 ? (
              items.map((item, index) => (
                <tr key={index} style={{ borderBottom: "1px solid #d1d5db" }}>
                  <td
                    style={{
                      textAlign: "left",
                      padding: "0.75rem",
                      borderTop: "1px solid #E0E2E7",
                      borderBottom: "1px solid #E0E2E7",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#333843"
                    }}
                  >
                    {index + 1}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      padding: "0.75rem",
                      borderTop: "1px solid #E0E2E7",
                      borderBottom: "1px solid #E0E2E7",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#333843"
                    }}
                  >
                    {item.itemName}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      padding: "0.75rem",
                      borderTop: "1px solid #E0E2E7",
                      borderBottom: "1px solid #E0E2E7",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#333843"
                    }}
                  >
                    {item.qty}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      padding: "0.75rem",
                      borderTop: "1px solid #E0E2E7",
                      borderBottom: "1px solid #E0E2E7",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#333843"
                    }}
                  >
                    {item?.strikeOffAmount && item.qty
                      ? (item.strikeOffAmount / item.qty).toFixed(2)
                      : item.price}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      padding: "0.75rem",
                      borderTop: "1px solid #E0E2E7",
                      borderBottom: "1px solid #E0E2E7",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#333843"
                    }}
                  >
                    ₹{item.strikeOffAmount?.toFixed(2) || item.amount}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      padding: "0.75rem",
                      borderTop: "1px solid #E0E2E7",
                      borderBottom: "1px solid #E0E2E7",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#333843"
                    }}
                  >
                    {item.strikeOffAmount && item.amount
                      ? (item.strikeOffAmount - item.amount).toFixed(2)
                      : 0}
                  </td>
                  <td
                    style={{
                      textAlign: "right",
                      padding: "0.75rem",
                      borderTop: "1px solid #E0E2E7",
                      borderBottom: "1px solid #E0E2E7",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#333843"
                    }}
                  >
                    ₹{item.amount?.toFixed(2) || "N/A"}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={5}
                  style={{
                    textAlign: "center",
                    padding: "1rem",
                    border: "1px solid #d1d5db"
                  }}
                >
                  No items available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    );

    // Summary Component
    const Summary = () => (
      <div className="invoice-summary">
        <div
          className="summary"
          style={{
            display: "flex",
            justifyContent: "flex-end"
          }}
        >
          <div style={{ width: "250px" }}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                padding: "0.5rem 0"
              }}
            >
              <span style={{ color: "#667085" }}>Total excluding tax:</span>
              <span style={{ fontWeight: "500", color: "#333843" }}>
                ₹{orderDetails.totalOrderAmount.toFixed(2)}
              </span>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                padding: "0.5rem 0"
              }}
            >
              <span style={{ color: "#667085" }}>Delivery Charges:</span>
              <span style={{ fontWeight: "500", color: "#333843" }}>
                ₹{orderDetails.deliveryCharges.toFixed(2)}
              </span>
            </div>
            {orderDetails.packagingCharges > 0 && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  padding: "0.5rem 0"
                }}
              >
                <span style={{ color: "#667085" }}>Packaging:</span>
                <span style={{ fontWeight: "500", color: "#333843" }}>
                  ₹{orderDetails.packagingCharges.toFixed(2)}
                </span>
              </div>
            )}
            {/* {orderDetails.discountAmount > 0 && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  padding: "0.5rem 0"
                }}
              >
                <span style={{ color: "#667085" }}>Discount:</span>
                <span>-₹{orderDetails.discountAmount.toFixed(2)}</span>
              </div>
            )} */}
            {orderDetails.totalTaxAmount > 0 && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  padding: "0.5rem 0"
                }}
              >
                <span style={{ color: "#667085" }}>
                  Total Tax <span style={{ fontSize: "12px" }}>(5% GST)</span>:
                </span>
                <span style={{ fontWeight: "500", color: "#333843" }}>
                  ₹{orderDetails.totalTaxAmount.toFixed(2)}
                </span>
              </div>
            )}
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                padding: "0.5rem 0",
                borderTop: "1px solid #E0E2E7",
                fontWeight: "bold",
                color: "#333843"
              }}
            >
              <span>Total Amount Paid:</span>
              <span>₹{orderDetails.totalAmount.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>
    );

    //Footer Component
    const Footer = () => (
      <div
        className="invoice-footer"
        style={{
          textAlign: "center",
          color: "#4b5563",
          fontSize: "0.875rem",
          paddingTop: "1rem"
        }}
      >
        <p>Thank you for your business!</p>
      </div>
    );

    return (
      <div
        ref={ref}
        className="bg-white p-4"
        style={{
          fontFamily: "Nunito, sans-serif",
          width: "100%",
          maxWidth: "800px",
          margin: "0 auto",
          boxSizing: "border-box"
        }}
      >
        {/* Top: Header Section */}
        <Header />

        {/* Main: Flex Column */}
        <div
          className="invoice-main"
          style={{
            display: "flex",
            flexDirection: "column",
            border: "1px solid #E0E2E7",
            borderRadius: "0.75rem",
            padding: "1rem"
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              gap: "1rem",
              marginBottom: "1rem"
            }}
          >
            <OrderDetails />
            <BillingDetails />
          </div>
          <ItemsTable />
          <Summary />
        </div>

        <Footer />
      </div>
    );
  }
);

InvoicePdf.displayName = "InvoicePdf";

export default InvoicePdf;
