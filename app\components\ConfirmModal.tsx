import React from "react";
import { Transition, TransitionChild } from "@headlessui/react";

interface ConfirmModalProps {
  isOpen: boolean;
  title: string;
  message: string;
  icon?: React.ReactNode;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  title,
  message,
  icon,
  onConfirm,
  onCancel,
  confirmText = "OK",
  cancelText = "Cancel"
}) => {
  return (
    <>
      <Transition show={isOpen}>
        <TransitionChild
          enter="ease-out duration-200"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/30 z-40" onClick={onCancel} />
        </TransitionChild>

        <TransitionChild
          enter="ease-out duration-200"
          enterFrom="opacity-0 translate-y-3"
          enterTo="opacity-100 translate-y-0"
          leave="ease-in duration-100"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 translate-y-3"
        >
          <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl shadow-lg w-full max-w-md">
              <div className="p-4 flex flex-row items-center gap-1.5">
                {icon && icon}
                <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
              </div>

              <div className="px-4">
                <p className="text-gray-600">{message}</p>
              </div>

              <div className="p-4 flex justify-end gap-3">
                <button
                  onClick={onCancel}
                  className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
                >
                  {cancelText}
                </button>
                <button
                  onClick={onConfirm}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md"
                >
                  {confirmText}
                </button>
              </div>
            </div>
          </div>
        </TransitionChild>
      </Transition>
    </>
  );
};

export default ConfirmModal;
