import React, { useState, useRef } from "react";
import { ItemCategoryDtos, AvailableItem } from "~/types";
import { X } from "lucide-react";

interface MenuFloatingButtonProps {
  categories: ItemCategoryDtos[];
  menuButtonPosition?: string;
  onSelectCategory: (categoryId: number) => void;
  scrollToCategory: (categoryId: number) => void;
  itemsByCategory?: { [key: number]: AvailableItem[] };
}

// Floating button component
interface FloatingButtonProps {
  onClick: () => void;
  variant: "menu" | "close";
  menuButtonPosition?: string;
}

const FloatingButton: React.FC<FloatingButtonProps> = ({
  onClick,
  variant,
  menuButtonPosition = "1rem"
}) => {
  return (
    <button
      onClick={onClick}
      className={`fixed right-4 z-[50] bg-black text-white rounded-lg px-4 py-2 shadow-lg flex items-center justify-center gap-2`}
      style={{ bottom: menuButtonPosition }}
      aria-label={variant === "menu" ? "Open menu" : "Close menu"}
    >
      {variant === "menu" ? (
        <>
          <img src="/food-menu-icon.svg" alt="menu" />
          <span className="">Menu</span>
        </>
      ) : (
        <>
          <X className="h-4 w-4" />
          <span className="">Close</span>
        </>
      )}
    </button>
  );
};

// Category item component
interface CategoryItemProps {
  category: ItemCategoryDtos;
  itemCount: number;
  onClick: () => void;
  isSelected: boolean;
}

const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  itemCount,
  onClick,
  isSelected
}) => {
  return (
    <button
      className={`w-full py-2 flex justify-between items-center cursor-pointer hover:bg-gray-50 text-left ${
        isSelected ? "text-primary" : "text-gray-800"
      }`}
      onClick={onClick}
    >
      <span className="text-sm">{category.name}</span>
      <span className="text-sm">{itemCount}</span>
    </button>
  );
};

// Category modal component
interface CategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: ItemCategoryDtos[];
  onSelectCategory: (categoryId: number) => void;
  itemsByCategory?: { [key: number]: AvailableItem[] };
  selectedCategoryId?: number;
  menuButtonPosition?: string;
}

const CategoryModal: React.FC<CategoryModalProps> = ({
  isOpen,
  onClose,
  categories,
  onSelectCategory,
  itemsByCategory,
  selectedCategoryId,
  menuButtonPosition
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  if (!isOpen) return null;

  // Filter out categories with zero items
  const categoriesWithItems = categories.filter(
    (category) => (itemsByCategory?.[category.id]?.length || 0) > 0
  );

  return (
    <>
      <div
        className={`fixed inset-0 bg-black bg-opacity-60 z-50 flex flex-col justify-end items-end pr-4`}
        style={{ paddingBottom: menuButtonPosition ? `calc(${menuButtonPosition} + 3rem)` : "1rem" }}
        role="dialog"
        aria-modal="true"
        aria-label="Category menu"
      >
        <button
          className="absolute inset-0 w-full h-full"
          aria-label="Close menu"
          onClick={onClose}
        />
        <div
          ref={modalRef}
          className={`bg-white rounded-lg w-72  ${
            menuButtonPosition ? "max-h-[65vh]" : "max-h-[75vh]"
          } overflow-y-auto p-4 shadow-xl relative z-10 no-scrollbar`}
        >
          <div className="">
            {categoriesWithItems.map((category) => (
              <CategoryItem
                key={category.id}
                category={category}
                itemCount={itemsByCategory?.[category.id]?.length || 0}
                onClick={() => onSelectCategory(category.id)}
                isSelected={category.id === selectedCategoryId}
              />
            ))}
          </div>
        </div>
        {/* Separate Close button at bottom right */}
        <FloatingButton
          onClick={onClose}
          variant="close"
          menuButtonPosition={menuButtonPosition}
        />
      </div>
    </>
  );
};

// Main component
const MenuFloatingButton: React.FC<MenuFloatingButtonProps> = ({
  categories,
  onSelectCategory,
  scrollToCategory,
  itemsByCategory,
  menuButtonPosition
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number>();

  // Set first category as default when component mounts
  React.useEffect(() => {
    if (categories.length > 0 && !selectedCategoryId) {
      const firstCategory = categories[0];
      setSelectedCategoryId(firstCategory.id);
      onSelectCategory(firstCategory.id);
    }
  }, [categories, selectedCategoryId]);

  const handleToggleMenu = () => {
    setIsOpen((prev) => !prev);
  };

  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    scrollToCategory(categoryId);
    onSelectCategory(categoryId);
    setIsOpen(false);
  };

  return (
    <>
      {!isOpen && (
        <FloatingButton
          onClick={handleToggleMenu}
          variant="menu"
          menuButtonPosition={menuButtonPosition}
        />
      )}

      <CategoryModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        categories={categories}
        onSelectCategory={handleCategorySelect}
        itemsByCategory={itemsByCategory}
        selectedCategoryId={selectedCategoryId}
        menuButtonPosition={menuButtonPosition}
      />
    </>
  );
};

export default MenuFloatingButton;
