import { BriefcaseBusiness, HomeIcon, MapPinIcon } from "lucide-react";
import React, { useState, useEffect } from "react";
import { AddressType } from "~/types/address.types";

interface AddressTypeSelectorProps {
  selectedType: AddressType;
  onChange: (type: AddressType) => void;
  variant?: "pills" | "buttons";
  className?: string;
  customLabel?: string;
  onCustomLabelChange?: (label: string) => void;
}

/**
 * A pure React component for selecting address types (Home, Office, Other)
 */
export const AddressTypeSelector: React.FC<AddressTypeSelectorProps> = ({
  selectedType,
  onChange,
  variant = "pills",
  className = "",
  customLabel = "",
  onCustomLabelChange = () => {}
}) => {
  // Map address types to display names
  const addressTypeLabels: Record<AddressType, string> = {
    home: "Home",
    work: "Work",
    other: "Other"
  };

  const getIcon = (type: string) => {
    switch (type) {
      case "home":
        return <HomeIcon className="w-4 h-4" />;
      case "work":
        return <BriefcaseBusiness className="w-4 h-4" />;
      default:
        return <MapPinIcon className="w-4 h-4" />;
    }
  };

  // Different styling based on variant
  const getButtonStyle = (type: string): string => {
    const isSelected = selectedType === type;

    if (variant === "pills") {
      return `px-3 py-1.5 rounded-full text-xs font-medium ${
        isSelected ? "bg-teal-600 text-white" : "bg-gray-100 text-gray-600"
      }`;
    }

    return `px-3 py-1.5 text-sm font-semibold rounded-xl inline-flex flex-row items-center gap-1 leading-tight transition-all ${
      isSelected
        ? "bg-primary-50 text-primary border border-primary"
        : "bg-white text-typography-700 border border-neutral-600"
    }`;
  };

  const [showCustomInput, setShowCustomInput] = useState(
    selectedType === "other"
  );
  const [inputValue, setInputValue] = useState(customLabel || "");
  // const [customLabelTouched, setCustomLabelTouched] = useState(false);

  // Update the showCustomInput state when selectedType changes
  useEffect(() => {
    setShowCustomInput(selectedType === "other");
  }, [selectedType]);

  // Update the parent component when inputValue changes
  useEffect(() => {
    if (showCustomInput) {
      onCustomLabelChange(inputValue);
    }
  }, [inputValue, showCustomInput, onCustomLabelChange]);

  return (
    <div className={`flex flex-col ${className}`}>
      <div
        className={`flex ${variant === "pills" ? "space-x-2" : "space-x-3"}`}
      >
        {Object.entries(addressTypeLabels).map(([type, label]) => (
          <button
            key={type}
            type="button"
            onClick={() => {
              // If switching from "other" to standard type, clear the custom label
              if (selectedType === "other" && type !== "other") {
                onCustomLabelChange("");
              }
              onChange(type as AddressType);
            }}
            className={getButtonStyle(type)}
            aria-pressed={selectedType === type}
          >
            {variant === "buttons" && getIcon(type)}
            {label}
          </button>
        ))}
      </div>

      {showCustomInput && variant === "buttons" && (
        <div className="mt-2 w-full">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
              // setCustomLabelTouched(true);
            }}
            placeholder="Enter custom address label"
            className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-teal-500 focus:border-teal-500"
            aria-label="Custom address label"
          />
          <p className="mt-1 ml-1 text-xs text-gray-500">
            Enter a custom label for this address (e.g., Mom&apos;s House, Shop,
            etc.)
          </p>
          {showCustomInput && !inputValue?.trim() && (
            <p className="mt-1 text-xs text-red-500">
              * Custom label is required
            </p>
          )}
        </div>
      )}
    </div>
  );
};
