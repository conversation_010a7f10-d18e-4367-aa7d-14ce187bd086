import { FC } from "react";

interface TimelineHeaderProps {
  sellerLogo?: string;
  sellerName: string;
  sellerAddress?: string;
}

const TimelineHeader: FC<TimelineHeaderProps> = ({
  sellerLogo,
  sellerName,
  sellerAddress
}) => {
  return (
    <div className="flex items-center gap-3">
      <div className="w-12 h-12 rounded-lg border border-neutral-100 aspect-square items-center justify-center">
        <img
          src={sellerLogo || "/mandi_active.png"}
          alt={""}
          className="w-full h-full object-stretch"
        />
      </div>
      <div className="w-full">
        <h3 className="text-md text-gray-900">{sellerName}</h3>
        {sellerAddress && (
          <div className="line-clamp-1 w-full text-xs text-gray-500">
            {sellerAddress}
          </div>
        )}
      </div>
    </div>
  );
};

export default TimelineHeader;
