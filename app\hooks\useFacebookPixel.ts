import { useEffect } from "react";

// Extend the Window interface to include fbq
declare global {
  interface Window {
    fbq?: (...args: unknown[]) => void;
  }
}

/**
 * Custom hook to initialize Facebook Pixel after page render
 * @param pixelId - Facebook Pixel ID
 * @param locationKey - Unique key for each page
 */
export const useFacebookPixel = (pixelId: string, locationKey?: string): void => {
  useEffect(() => {
    // Prevent initialization in non-browser environments
    if (typeof window === "undefined") return;

    // Check if pixelId is provided
    if (!pixelId) return;

    // Prevent duplicate initialization
    if (window.fbq) {
      window.fbq("track", "PageView");
      return;
    };

    // Create and append Facebook Pixel script
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = `
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '${pixelId}');
      fbq('track', 'PageView');
    `;

    document.head.appendChild(script);
  }, [pixelId, locationKey]);
};

