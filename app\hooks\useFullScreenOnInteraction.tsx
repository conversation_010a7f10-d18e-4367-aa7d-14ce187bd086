import { useEffect } from "react";
import { AppSource } from "~/types/app";

const useFullscreenOnInteraction = ({
  appSource
}: {
  appSource: AppSource;
}) => {
  useEffect(() => {
    if (appSource === "whatsappchat") {
      const enterFullscreen = () => {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch((err) => {
            console.log(`Error attempting fullscreen: ${err.message}`);
          });
        }
      };

      // List of interaction events to trigger fullscreen mode
      const interactionEvents = [
        "click",
        "touchstart",
        "keydown",
        "mousedown",
        "mouseup"
      ];

      // Add listeners for all interaction events
      interactionEvents.forEach((event) => {
        document.addEventListener(event, enterFullscreen);
      });

      return () => {
        // Clean up event listeners on unmount
        interactionEvents.forEach((event) => {
          document.removeEventListener(event, enterFullscreen);
        });
      };
    }
  }, [appSource]);
};

export default useFullscreenOnInteraction;
