import BottomSheet from "./BottmSheet";
import { AvailableItem, Cart } from "~/types";
import AddItemButton from "./chooseitem/AddItemButton";
import GetBadge from "./chooseitem/GetBadge";
import PrimaryButton from "./PrimaryButton";
import CustomImage from "./CustomImage";

interface ItemsVariantListProps {
  showVariants: boolean;
  setShowVariants: (show: boolean) => void;
  variants: AvailableItem[];
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  cart: Cart;
  displayPrices?: boolean;
}

export function ItemsVariantList({
  showVariants,
  setShowVariants,
  variants,
  cart,
  onAdd,
  onRemove,
  displayPrices
}: ItemsVariantListProps) {
  const handleOnClose = () => {
    setShowVariants(false);
  };

  return (
    <BottomSheet
      isOpen={showVariants}
      onClose={handleOnClose}
      transitionDuration={1000}
      className="bg-gray-100 p-4"
    >
      <div className="flex flex-col gap-4">
        <span className="font-semibold">{variants[0].itemName}</span>
        {variants.map((itemDetails) => (
          <VariantDetails
            key={itemDetails.sellerItemId}
            itemDetails={itemDetails}
            qty={cart[itemDetails.sellerItemId]?.qty || 0}
            onAdd={onAdd}
            onRemove={onRemove}
            displayPrices={displayPrices}
          />
        ))}
        <PrimaryButton onClick={handleOnClose}>Done</PrimaryButton>
      </div>
    </BottomSheet>
  );
}

interface VariantDetailsProps {
  itemDetails: AvailableItem;
  qty: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  displayPrices?: boolean;
}

function VariantDetails({
  itemDetails,
  qty,
  onAdd,
  onRemove,
  displayPrices
}: VariantDetailsProps) {
  return (
    <div className="relative flex items-center justify-between bg-white rounded-md p-2">
      {/* Show Badges */}
      {itemDetails.closed || itemDetails.soldout ? (
        <div className="absolute inset-0 bg-white bg-opacity-60 rounded-lg z-10">
          <div className="relative">
            <GetBadge itemDetails={itemDetails} />
          </div>
        </div>
      ) : (
        <GetBadge itemDetails={itemDetails} />
      )}
      <div className="flex items-center gap-2">
        <CustomImage
          src={itemDetails?.itemUrl}
          alt={""}
          className={`transition-all duration-300 ease-in-out rounded-md object-fill w-16 h-16`}
        />
        <span className="text-xs">
          {itemDetails.packaging ||
            `${itemDetails.unitWtFactor}/${itemDetails.unit}`}
        </span>
      </div>
      {displayPrices !== false && <div>
        <span className="self-center text-xs text-gray-500">
          ₹
          <span className="text-xs font-medium text-gray-700">
            {itemDetails.pricePerUnit}
          </span>{" "}
          /{itemDetails.unit}
        </span>
        {itemDetails.discPerc > 0 && (
          <span className="text-[10px] font-light text-gray-400 line-through ml-2">
            ₹ {itemDetails.strikeoffPrice} /{itemDetails.unit}
          </span>
        )}
      </div>}
      <AddItemButton
        qty={qty}
        onAdd={() => onAdd(itemDetails)}
        onRemove={() => onRemove(itemDetails)}
        isDisabled={itemDetails.closed || itemDetails.soldout}
        unit={itemDetails.unit}
      />
    </div>
  );
}
