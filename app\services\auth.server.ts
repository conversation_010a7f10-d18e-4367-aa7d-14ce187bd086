// app/services/auth.server.ts
import { apiRequest } from "~/utils/api";
import { DeviceInfo } from "~/types/deviceInfo";
import { NetworkConfig } from "~/types";
import { getDomainFromRequest } from "~/utils/domain";
import { ApiResponse } from "~/types/Api";

const API_BASE_URL = process.env.API_BASE_URL;

// Payload and Response Interfaces

interface RequestOtpPayload {
  app: string;
  mobileNumber: string;
  password: string;
  admin: boolean;
  deviceInfo: DeviceInfo;
}

interface VerifyOtpPayload {
  username: string;
  password: string;
}

interface VerifyOtpResponse {
  userId: number;
  userName: string;
  access_token?: string | null;
  refresh_token?: string | null;
  roles: string[];
}

interface RegisterUserPayload {
  fullName: string;
  mobileNumber: string;
  address: string;
  latitude: number;
  longitude: number;
  networkId: number;
}

interface RegisterUserResponse {
  userId: number;
  userName: string;
  businessName: string;
  mobileNumber: string;
  buyerId: number;
  minVersion: number;
  networkId: number;
}

export async function requestOtp(payload: RequestOtpPayload): Promise<void> {
  const url = `${API_BASE_URL}/login/otp/generate`;
  await apiRequest<void>(url, "POST", payload, {}, false);
}

export async function verifyOtp(
  mobileNumber: string,
  otp: string,
  source = "",
  request: Request
): Promise<ApiResponse<VerifyOtpResponse>> {
  let url = `${API_BASE_URL}/login`;
  if (source === "whatsAppLogin") {
    url = `${API_BASE_URL}/login?source=whatsAppLogin`;
  }
  if (source === "anonymousLogin") {
    url = `${API_BASE_URL}/login?source=anonymousLogin`;
  }
  const { domain } = getDomainFromRequest(request);
  const payload: VerifyOtpPayload = {
    username: JSON.stringify({
      mobileNumber: mobileNumber,
      appInfo: {
        app: "buyer_app",
        version: "1000",
        domain: domain
        // networkId: 6,
      }
    }),
    password: otp
  };

  const headers = {
    Accept: "application/json, text/plain, */*",
    Authorization: "Bearer null",
    "Content-Type": "application/json"
  };

  const response = await apiRequest<VerifyOtpResponse>(
    url,
    "POST",
    payload,
    headers
  );
  return response;
}

export function getDeviceInfo(): DeviceInfo {
  return {
    brand: "google",
    buildNumber: "1000",
    bundleId: "com.mandi_app",
    deviceId: "sunfish",
    model: "Pixel 4a"
  };
}

export async function registerUser(
  request: Request,
  payload: RegisterUserPayload,
  token: string
): Promise<ApiResponse<RegisterUserResponse>> {
  const { domain } = getDomainFromRequest(request);
  const url = API_BASE_URL + `/login/d/${domain}/buyer`;
  const headers = {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`
  };

  const response = await apiRequest<RegisterUserResponse>(
    url,
    "POST",
    payload,
    headers,
    true,
    request
  );
  return response;
}

export async function getNetworkConfig(
  request: Request
): Promise<ApiResponse<NetworkConfig>> {
  const { domain } = getDomainFromRequest(request);
  const url = API_BASE_URL + `/login/d/${domain}/theme`;

  try {
    const response = await apiRequest<NetworkConfig>(
      url,
      "GET",
      null,
      {},
      true, // Indicates server-side request,
      request
    );
    console.log("getNetworkConfig response: ", response);
    if (response) {
      console.log("getNetworkConfig response truee ");
      return response;
    } else {
      throw new Error("change language api failed");
    }
  } catch (error) {
    console.error("Error change language:", error);
    throw error;
  }
}
