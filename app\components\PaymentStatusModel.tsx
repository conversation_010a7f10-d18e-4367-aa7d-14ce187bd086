import React from "react";
import Button from "./Button";

interface PaymentStatusPopupProps {
  paymentStatus: "PENDING" | "SUCCESS" | "FAILED";
  onClose: () => void;
}

const PaymentStatusPopup: React.FC<PaymentStatusPopupProps> = ({
  paymentStatus,
  onClose
}) => {
  const handleOutsideClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (e.target === e.currentTarget) {
      onClose();
    }
  };
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Escape") {
      onClose();
    }
  };
  return (
    <div
      onClick={handleOutsideClick}
      onKeyDown={handleKeyDown} // Close on Escape key press
      tabIndex={0} // Make div focusable for accessibility
      role="button"
      className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50"
    >
      <div className="bg-white p-6 rounded-lg shadow-lg w-72 text-center">
        {paymentStatus === "PENDING" ? (
          <div>
            <div className="w-16 h-16 border-4 border-gray-200 border-t-4 border-t-teal-500 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-700">Processing payment...</p>
            <Button
              onClick={() => onClose()}
              className="mt-6 text-white text-sm bg-red-400 px-4 py-2 rounded-md hover:bg-red-500"
            >
              Close
            </Button>
          </div>
        ) : paymentStatus === "SUCCESS" ? (
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-16 w-16 text-teal-500 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 12l2 2 4-4M7 12l5 5L17 7"
              />
            </svg>
            <p className="text-teal-500 font-bold">Payment Successful!</p>
          </div>
        ) : (
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-16 w-16 text-red-500 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
            <p className="text-red-500 font-bold">Payment Failed!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentStatusPopup;
