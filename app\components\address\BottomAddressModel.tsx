import React from "react";
import Toast from "~/components/Toast";
import { AddressType } from "~/types/address.types";
import { AddressTypeSelector } from "./AddressTypeSelector";

interface LocationDetails {
  placeName: string;
  city: string;
  formattedAddress: string;
}

interface ActionData {
  success?: boolean;
  message?: string;
  errors?: {
    [key: string]: string;
  };
  error?: string;
}

export function BottomAddressModel({
  setSaveAddressModelClicked,
  setSearchFocused,
  saveAddressModelClicked,
  locationDetails,
  showErrorToast,
  setShowErrorToast,
  actionData,
  addressType,
  setAddressType,
  customAddressLabel,
  setCustomAddressLabel
}: {
  setSaveAddressModelClicked: (value: React.SetStateAction<boolean>) => void;
  setSearchFocused: (value: React.SetStateAction<boolean>) => void;
  saveAddressModelClicked: boolean;
  locationDetails: LocationDetails;
  showErrorToast?: boolean;
  setShowErrorToast?: (value: React.SetStateAction<boolean>) => void;
  actionData?: ActionData;
  addressType: AddressType;
  setAddressType: (value: AddressType) => void;
  customAddressLabel: string;
  setCustomAddressLabel: (value: string) => void;
}) {
  return (
    <div className="w-full flex flex-col p-4 bg-white shadow-md">
      {showErrorToast && actionData?.success === false && (
        <Toast
          type="error"
          message={actionData.error || "Something went wrong"}
          onClose={() => setShowErrorToast && setShowErrorToast(false)}
          position="bottom-center"
          duration={3000}
          width="full"
          className=""
        />
      )}

      <div className="flex items-center justify-between mb-3">
        {/* Address Type Selector */}
        <AddressTypeSelector
          selectedType={addressType}
          onChange={setAddressType}
          variant="pills"
          customLabel={customAddressLabel}
          onCustomLabelChange={setCustomAddressLabel}
        />
      </div>

      <div className="flex items-center justify-between">
        {/* location */}
        <div className="flex flex-row items-center mr-4">
          {/* Map Icon */}
          <img className="w-4 h-6 mr-2" src="map_location_icon.png" alt="" />
          {/* place name */}
          <div>
            <div className="text-teal-700 text-sm font-semibold overflow-hidden h-6">
              {locationDetails?.placeName}
            </div>
            {/* Address */}
            <div className="text-gray-500 text-[10px]">
              {locationDetails?.formattedAddress}
            </div>
          </div>
        </div>
        {/* Change button */}
        <button
          onClick={() => setSearchFocused((state) => !state)}
          className="text-teal-600 text-xs border border-teal-600 p-1 rounded-md hover:bg-teal-600 hover:text-white"
        >
          CHANGE
        </button>
      </div>
      <button
        onClick={() => setSaveAddressModelClicked(!saveAddressModelClicked)}
        className="w-full bg-teal-600 text-white text-lg font-regular py-2 mt-4 rounded-md shadow-md"
      >
        Confirm location
      </button>
    </div>
  );
}
