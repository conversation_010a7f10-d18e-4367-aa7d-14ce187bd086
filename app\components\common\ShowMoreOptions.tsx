import { ChevronDown, ChevronUp } from "lucide-react";
import React from "react";

interface ShowMoreOptionsProps {
  totalItems: number;
  visibleItems: number;
  onShowMore: () => void;
  onShowLess: () => void;
  showAll: boolean;
  moreText?: string;
  lessText?: string;
  className?: string;
}

const ShowMoreOptions: React.FC<ShowMoreOptionsProps> = ({
  totalItems,
  visibleItems,
  onShowMore,
  onShowLess,
  showAll,
  moreText = "more",
  lessText = "see less",
  className = ""
}) => {
  const remainingItems = totalItems - visibleItems;

  return (
    <>
      {totalItems > visibleItems && !showAll && (
        <div
          className={`text-sm text-primary cursor-pointer flex gap-1 items-center ${className}`}
          onClick={onShowMore}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              onShowMore();
            }
          }}
        >
          + {remainingItems} {moreText} <ChevronDown size={15} />
        </div>
      )}

      {totalItems > visibleItems && showAll && (
        <div
          className={`text-sm text-primary cursor-pointer flex gap-1 items-center ${className}`}
          onClick={onShowLess}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              onShowLess();
            }
          }}
        >
          {lessText} <ChevronUp size={15} />
        </div>
      )}
    </>
  );
};

export default ShowMoreOptions;
