// app/components/ItemRow.tsx

import React, { useRef, useState } from "react";
import { SellerOrderItem, AvailableItem } from "~/types";
import AddItemButtonV2 from "../chooseitem/AddItemButtonV2";
import { roundOff } from "@utils/roundOff";
import { ChevronRight } from "lucide-react";
import Button from "../Button";
import ItemCustomization, {
  ItemAddonOption
} from "../restaurant/ItemCustomization";
import { useCartStore } from "~/stores/cart.store";
import BottomSheet from "../BottmSheet";
import { DietaryImage } from "../common/DietaryImage";
import { formatCurrency } from "~/utils/format";
interface ItemRowProps {
  itemDetails: SellerOrderItem;
  onAdd?: () => void;
  onRemove?: () => void;
  quantity?: number;
  itemType?: "confirmed" | "new";
  amount?: number;
  onAddonsUpdate?: () => void;
  strikeOffAmt?: number;
}

const RestaurantItemRow: React.FC<ItemRowProps> = ({
  itemDetails,
  onAdd,
  onRemove,
  quantity = 0,
  amount = 0,
  itemType = "new",
  onAddonsUpdate,
  strikeOffAmt = 0
}) => {
  const { cart } = useCartStore();

  const showEdit =
    (itemType === "new" &&
      itemDetails.availableCartItem?.aogList &&
      itemDetails.availableCartItem.aogList?.length > 0) ||
    (itemDetails.availableCartItem?.itemVariationList &&
      itemDetails.availableCartItem.itemVariationList?.length > 0);

  const [showCustomization, setShowCustomization] = useState(false);

  const handleEdit = () => {
    setShowCustomization(true);
  };

  const handleCloseCustomization = () => {
    setShowCustomization(false);
  };

  // Create wrapper functions with the correct signatures
  const handleCustomizationAdd = (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    item: AvailableItem,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    selectedAddons?: Record<string, ItemAddonOption[]>
  ) => {
    if (onAdd) {
      onAdd();
    }
  };

  const handleCustomizationRemove = (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    item: AvailableItem
  ) => {
    if (onRemove) {
      onRemove();
    }
  };

  const bottomSheetRef = useRef<HTMLDivElement | null>(null);

  return (
    <div>
      {(!itemDetails.isSoldOut || itemDetails.quantity > 0) && (
        <div className="flex justify-between items-start gap-6 py-2.5">
          <div className="flex flex-row gap-2">
            <div className="pt-0.5">
              <DietaryImage dietary={itemDetails.availableCartItem.diet} className="min-w-4 min-h-4"/>
            </div>
            <div>
              <div className="flex flex-col gap-0.5">
                <h3
                  className={`text-sm font-medium overflow-hidden text-typography-900 line-clamp-2`}
                >
                  {itemDetails.itemName}
                </h3>
                {itemDetails.packaging && itemType !== "confirmed" && (
                  <div className="text-xs font-normal text-typography-200">
                    {itemDetails?.packaging}
                  </div>
                )}
                {/* <p className="text-xs text-typography-500 ">
                  ₹
                  <span className="font-semibold text-gray-600">
                    {itemDetails.pricePerUnit}
                  </span>
                </p> */}
              </div>
              {
                showEdit && (itemDetails.addOnItemDtos?.length || itemDetails.variationId) && (
                  <p className="text-xs font-normal text-typography-400 line-clamp-2">
                    {itemDetails.variationId ? itemDetails.availableCartItem.itemVariationList?.filter((v) => v.id === itemDetails.variationId)[0]?.name +
                      (itemDetails.addOnItemDtos?.length ? ", " : "") : ""}
                    {itemDetails.addOnItemDtos?.reduce((acc, item) => {
                      return acc + item.name + ", ";
                    }, "").slice(0, -2)}
                  </p>
                )
              }
              {showEdit && (
                <Button
                  className="flex flex-row items-center gap-1 text-[13px] font-semibold text-typography-400 mt-1 cursor-pointer"
                  onClick={handleEdit}
                >
                  <p>Edit</p>
                  <img src="/play.svg" alt="Right" className="w-2.5 h-2.5" />              
                </Button>
              )}
              
              <BottomSheet
                isOpen={showCustomization}
                onClose={handleCloseCustomization}
                className=" bg-gray-100"
                backdropClassName="z-[50]"
                sheetType="drawer"
                swipeIndicatorClassName="bg-gray-100 before:bg-gray-300"
                ref={bottomSheetRef}
              >
                <ItemCustomization
                  item={itemDetails.availableCartItem}
                  cart={cart}
                  onAdd={handleCloseCustomization}
                  onRemove={handleCustomizationRemove}
                  onAddonsUpdate={onAddonsUpdate}
                  parentRef={bottomSheetRef}
                />
              </BottomSheet>
              
              {itemDetails.pricePerUnit && (
                <div className="text-xs align-bottom font-light text-typography-400">
                  {itemType === "confirmed" ? (
                    <>
                      <span className="text-xs font-semibold text-primary">
                        {`${quantity} ${
                          itemDetails.unit === "unit" ? "" : itemDetails.unit
                        }`}
                        &nbsp;
                      </span>
                    </>
                  ) : null}
                </div>
              )}
            </div>
          </div>
          {
            <div className="flex flex-col items-end gap-2">
              {/* <p className="text-sm font-thin text-typography-800 pr-2"> */}

              {itemType === "new" && onAdd && onRemove && (
                <AddItemButtonV2
                  qty={quantity}
                  onAdd={onAdd}
                  onRemove={onRemove}
                  isDisabled={itemDetails.isSoldOut}
                  unit={itemDetails.unit}
                  btnConfig={{
                    showUnit: false,
                    iconSize: 16,
                    btnType: "secondary"
                  }}
                  className="h-8 text-xs w-[74px] bg-teal-50 rounded-[.25rem]"
                />
              )}
              <div className="flex flex-row items-baseline gap-1">
                {itemDetails?.strikeOffAmount &&
                  itemDetails?.strikeOffAmount > itemDetails?.amount && (
                    <p className="text-[10px] font-light text-typography-400 line-through">
                      {formatCurrency(itemDetails?.strikeOffAmount)}
                    </p>
                  )}
                <p
                  className={`text-sm font-semibold overflow-hidden text-typography-700 mr-1 ${
                    itemType === "confirmed"
                      ? "text-sm font-semibold text-typography-600"
                      : "text-xs font-semibold  text-typography-300"
                  }`}
                >
                  {formatCurrency(itemDetails?.amount)}
                </p>
              </div>
            </div>
          }
        </div>
      )}
      {itemDetails.isSoldOut && itemDetails.quantity === 0 && (
        <div className="relative flex justify-between items-center py-1 backdrop-blur-md">
          <div className="flex items-stretch gap-2">
            <div className="flex flex-col justify-between gap-1">
              <div className="flex flex-col gap-0">
                <h3 className="text-sm font-medium w-full overflow-hidden text-typography-700 line-clamp-1">
                  {itemDetails.itemName}
                </h3>
                {itemDetails.packaging && (
                  <div className="text-xs font-normal text-typography-200">
                    {itemDetails.packaging}
                  </div>
                )}
              </div>
              {itemDetails.quantity > 0 && (
                <p className="text-xs font-light text-typography-300">
                  {`${itemDetails.quantity} ${itemDetails.unit} `}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RestaurantItemRow;
