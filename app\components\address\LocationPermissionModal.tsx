import React from "react";
import PrimaryButton from "../PrimaryButton";

interface LocationPermissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenSettings: () => void;
}

/**
 * Modal that appears when location permissions are needed
 * Provides options to cancel or open device settings
 */
export const LocationPermissionModal: React.FC<
  LocationPermissionModalProps
> = ({ isOpen, onOpenSettings }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
        <h3 className="text-lg font-semibold mb-3">Location Access Required</h3>
        <p className="text-gray-600 mb-4">
          To use your current location, please allow location access when
          prompted by your browser.
        </p>
        <div className="flex items-center justify-center gap-3">
          <PrimaryButton type="button" onClick={onOpenSettings}>
            Allow Location
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};
