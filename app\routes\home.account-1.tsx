// app/routes/home.account.tsx

import React, { useEffect, useState } from "react";
import { Form, useSubmit, useNavigate, useLoaderData } from "@remix-run/react";
import { ActionFunction, LoaderFunction, redirect } from "@remix-run/node";
import { destroySession, getSession } from "~/utils/session.server";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken } from "~/types/user";
import { BackNavHeader } from "~/components/BackNavHeader";
import {
  MapPin,
  ChevronRight,
  CirclePower,
  Languages,
  Headset,
  UserCog
} from "lucide-react";
import Button from "~/components/Button";

interface LoaderData {
  businessName: string;
  mobileNumber: string;
  isBuyerOwner: boolean;
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  if (!access_token) {
    return redirect("/login");
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      return redirect("/login");
    }

    const isBuyerOwner = decoded.roles?.includes("buyer_app.WRITE");

    const { businessName, mobileNumber } = decoded.userDetails;

    if (!mobileNumber) {
      return redirect("/login");
    }

    return { businessName, mobileNumber, isBuyerOwner };
  } catch (error) {
    console.error("Error decoding access_token:", error);
    return redirect("/login");
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));

  return redirect("/login", {
    headers: {
      "Set-Cookie": await destroySession(session)
    }
  });
};

export default function Account() {
  const submit = useSubmit();
  const navigate = useNavigate();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [version, setVersion] = useState("");

  // Use loader data
  const { businessName, mobileNumber, isBuyerOwner } =
    useLoaderData<LoaderData>();

  const handleLogout = () => {
    // Clear local storage
    localStorage.clear();

    // Submit the form to trigger the action
    submit(null, { method: "post", action: "/home/<USER>" });
  };

  useEffect(() => {
    async function checkAppVersion() {
      try {
        if (
          typeof window !== undefined &&
          typeof window.getAppInfo !== undefined &&
          window.getAppInfo
        ) {
          const appInfo = (await window.getAppInfo()) as AppInfo;
          console.log("getAppInfo", appInfo);
          setVersion(appInfo?.versionName || "");
        }
      } catch (error) {
        console.error("Error fetching app info:", error);
      }
    }

    checkAppVersion();
  }, []);

  return (
    <div className="h-screen">
      <BackNavHeader
        buttonText="Settings"
        handleBack={() => navigate("/home")}
      />

      <div className="flex flex-col items-center bg-gray-100 px-2">
        {/* User Info */}
        <SettingCardLayout
          //   title="User Info"
          //   icon={<User size={24} />}
          onClick={() => {}}
        >
          <div className="flex flex-row justify-between">
            <div>
              <p className="text-md text-gray-500">
                <span className="">Name:</span> {businessName}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                <span className="">Ph:</span> {mobileNumber}
              </p>
            </div>
            <Button
              onClick={() => navigate("/help")}
              className="h-10 flex text-md items-center px-3 border text-teal-600 border-teal-600 rounded-3xl"
            >
              <Headset size={20} className="mr-2" />
              <span className="text-sm">HELP</span>
            </Button>
          </div>
        </SettingCardLayout>

        {/* User management Section */}
        {isBuyerOwner && (
          <SettingCardLayout
            title="User Management"
            description="Add or active/deactivate users from here"
            icon={<UserCog size={24} />}
            onClick={() => navigate("/usermanagement")}
          />
        )}

        {/* Language Section */}
        <SettingCardLayout
          title="Change Language"
          description="You can update your preferred language anytime"
          icon={<Languages size={24} />}
          onClick={() => navigate("/language")}
        />

        {/* Address Section */}
        <SettingCardLayout
          title="Address"
          description="Update your delivery address"
          icon={<MapPin size={24} />}
          onClick={() =>
            navigate(
              "/select-address?flowType=address-list&returnTo=/home/<USER>",
              {
                state: {
                  from: "/home/<USER>",
                  returnTo: "/home/<USER>",
                  flowType: "address-list"
                }
              }
            )
          }
        />

        <SettingCardLayout
          title="Logout"
          description="Sign out from this device"
          icon={<CirclePower size={24} />}
          onClick={() => setShowConfirmDialog(true)}
        />

        {/* Logout Confirmation Dialog */}
        {showConfirmDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-8">
            <div className="bg-white p-6 rounded-lg shadow-xl flex flex-col items-center">
              <h2 className="text-lg font-medium mb-4">Confirm Logout</h2>
              <p className="mb-6 text-md">Are you sure you want to logout?</p>
              <div className="flex justify-end space-x-4">
                <button
                  className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
                  onClick={() => setShowConfirmDialog(false)}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600"
                  onClick={() => {
                    setShowConfirmDialog(false);
                    handleLogout();
                  }}
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      <div className="w-20 h-15 mb-20 mx-auto mt-5 ">
        <NetworkAsset assetName="footer" />
        <p className="text-center text-md mt-1">{version}</p>
      </div>
    </div>
  );
}

function SettingCardLayout({
  title,
  description,
  icon,
  onClick,
  children
}: {
  title?: string;
  description?: string;
  icon?: any;
  onClick: () => void;
  children?: React.ReactNode;
}) {
  return (
    <div
      className="w-full max-w-md p-4 cursor-pointer transition border-t border-grey-500"
      onClick={onClick}
      tabIndex={0}
      onKeyPress={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick();
        }
      }}
      role="button"
      aria-label="Update your delivery address"
    >
      <div className="flex flex-col">
        <div className="flex flex-row justify-between items-center">
          <div className="flex flex-row items-center">
            <div className="mr-3 text-teal-500">{icon}</div>
            <div className="">
              {title ? <h2 className="text-sm font-medium">{title}</h2> : null}
              {description ? (
                <p className="text-xs font-light text-gray-500">
                  {description}
                </p>
              ) : null}
            </div>
          </div>
          {title ? (
            <div className="text-gray-500">
              <ChevronRight size={24} />
            </div>
          ) : null}
        </div>
        {children}
      </div>
    </div>
  );
}

import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { NetworkAsset } from "~/components/NetworkAssests";
import { AppInfo } from "~/types";
export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(-1)} />;
}
