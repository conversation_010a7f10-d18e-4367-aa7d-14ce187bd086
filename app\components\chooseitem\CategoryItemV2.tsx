import React from "react";

import { ItemCategoryDtos } from "~/types";
import Button from "../Button";
interface CategoryItemProps {
  category: ItemCategoryDtos;
  isSelected: boolean;
  onSelect: () => void;
}
export const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  isSelected,
  onSelect
}) => {
  return (
    <Button
      className={`relative flex items-center w-full h-24 my-2 transition-all duration-1000 ease-in-out`}
      onClick={onSelect}
    >
      <button className=" w-full flex text-center flex-col items-center">
        <div
          className={`flex items-center justify-center w-12 h-12 border-2 rounded-full transition-all duration-300 ease-in-out ${
            isSelected ? "bg-teal-100" : "bg-gray-200"
          } relative overflow-hidden ${
            isSelected ? " border-teal-500" : "border-gray-300"
          }`}
        >
          <img src={category.picture} alt="" className={`absolute h-12`} />
        </div>
        <p
          className={`text-center w-full mt-1 text-[.7rem] font-medium rounded-full transition-all duration-300 ${
            isSelected ? "text-teal-500" : "text-gray-600"
          }`}
        >
          {category.name}
        </p>
      </button>
      {/* <span
        className={`absolute right-0 bottom-0 w-1 rounded-md bg-teal-500 transition-all duration-300 ease-in-out ${
          isSelected ? "h-full" : "h-0"
        }`}
      ></span> */}
    </Button>
  );
};

export default CategoryItem;
