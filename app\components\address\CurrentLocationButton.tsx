import React from "react";
import { MapPin } from "lucide-react";

interface CurrentLocationButtonProps {
  onClick: () => void;
  isLoading: boolean;
  className?: string;
}

/**
 * Button component that allows users to request their current location
 * Shows a loading spinner when fetching location
 */
export const CurrentLocationButton: React.FC<CurrentLocationButtonProps> = ({
  onClick,
  isLoading,
  className = ""
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`bg-white p-3 rounded-full shadow-md flex items-center justify-center ${className}`}
      aria-label="Use current location"
      disabled={isLoading}
    >
      {isLoading ? (
        <div className="w-5 h-5 border-2 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
      ) : (
        <MapPin size={20} className="text-teal-600" />
      )}
    </button>
  );
};
