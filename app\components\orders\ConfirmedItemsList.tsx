import React, { useState } from "react";
import { SellerOrderItem } from "~/types";
import ItemRow from "@components/ItemRow";
import { roundOff } from "@utils/roundOff";
import { Check } from "lucide-react";
import { useCartStore } from "~/stores/cart.store";
import RestaurantItemRow from "../cart/RestaurantItemRow";
interface ConfirmedItemsListProps {
  items: SellerOrderItem[];
  displayPrices?: boolean;
}

export const ConfirmedItemsList: React.FC<ConfirmedItemsListProps> = ({
  items,
  displayPrices
}) => {
  const { cartClientType } = useCartStore();
  const [isExpanded, setIsExpanded] = useState(false);

  const confirmedItems = items.filter(
    (item) => item.availableCartItem && item.availableCartItem.orderedQty > 0
  );
  //   const confirmedItems = items;

  if (confirmedItems.length === 0) return null;

  const totalConfirmedAmount = confirmedItems.reduce((total, item) => {
    return total + item.availableCartItem.orderedAmount;
  }, 0);

  return (
    <div className="bg-primary-50 rounded-xl shadow border border-primary-200 transition-transform">
      <button
        className={`w-full ${
          isExpanded ? "pt-3 px-3" : "px-2 py-1"
        }  flex items-center justify-between`}
        onClick={() => setIsExpanded(!isExpanded)}
        aria-expanded={isExpanded}
        aria-controls="confirmed-items-content"
      >
        {isExpanded ? (
          <div className="flex flex-col gap-2 w-full">
            <div className="flex w-full justify-between ">
              <span className="text-xs font-normal text-typography-300 tracking-wide">
                {" "}
                CONFIRMED ITEMS
              </span>
              <span className="text-xs font-semibold text-primary tracking-wide">
                {confirmedItems.length}{" "}
                {confirmedItems.length > 1 ? "ITEMS" : "ITEM"}
              </span>
            </div>
            <div className="border-b border-primary-100"></div>
          </div>
        ) : (
          <div className="flex items-center w-full gap-3 ">
            <div className="text-white bg-primary rounded-full p-1">
              <Check size={16} />
            </div>
            <div className="flex flex-col w-full gap-0">
              <div className="flex gap-1 items-start">
                <span className="text-md font-semibold text-primary">
                  {confirmedItems.length}{" "}
                  {confirmedItems.length > 1 ? "items" : "item"}
                </span>{" "}
                <span className="text-md font-normal text-typography-300">
                  {" "}
                  already confirmed
                </span>
              </div>
              {displayPrices !== false && <div className="flex gap-1 items-start">
                <span className="text-md font-normal text-typography-300">
                  Total:{" "}
                </span>
                <span className="text-md font-semibold text-primary">
                  ₹ {roundOff(totalConfirmedAmount, true)}
                </span>
              </div>}
            </div>
            <svg
              className={`w-5 h-5 text-primary transition-transform ${
                isExpanded ? "rotate-180" : ""
              }`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        )}
      </button>

      {isExpanded && (
        <div id="confirmed-items-content" className="p-3">
          <div className="flex flex-col gap-3 ">
            {confirmedItems.map((item, index) => (
              <React.Fragment key={item.sellerItemId}>
                {cartClientType === "retailer" ? (
                  <ItemRow
                    itemDetails={item}
                    quantity={item.availableCartItem.orderedQty}
                    itemType="confirmed"
                    amount={item.availableCartItem.orderedAmount}
                    displayPrices={displayPrices}
                  />
                ) : (
                  <RestaurantItemRow
                    itemDetails={item}
                    quantity={item.availableCartItem.orderedQty}
                    itemType="confirmed"
                    amount={item.availableCartItem.orderedAmount}
                  />
                )}
              </React.Fragment>
            ))}
            <div className="flex flex-col gap-2">
              <div className="border-b border-primary-100"></div>
              <button
                className="w-full flex items-center justify-between"
                onClick={() => setIsExpanded(!isExpanded)}
                aria-expanded={isExpanded}
                aria-controls="confirmed-items-content"
              >
                <div className="flex w-full justify-between items-start">
                  <div className="flex gap-2">
                    {displayPrices !== false && (
                      <>
                        <span className="text-xs font-thin text-typography-300 tracking-wide">
                          TOTAL:{" "}
                        </span>
                        <span className="text-xs font-semibold text-primary tracking-wide">
                          ₹ {roundOff(totalConfirmedAmount, true)}
                        </span>
                      </>
                    )}
                  </div>
                  <svg
                    className={`w-4 h-4 text-primary transition-transform ${
                      isExpanded ? "rotate-180" : ""
                    }`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
