import React, { useState, useEffect, useRef } from "react";

interface PullToRefreshWrapperProps {
  onRefresh: () => void;
  children: React.ReactNode;
}

export default function PullToRefreshWrapper({
  onRefresh,
  children
}: PullToRefreshWrapperProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const touchStartYRef = useRef<number>(0);
  const touchCurrentYRef = useRef<number>(0);
  const isTouchingRef = useRef<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const isScrollable = (element: HTMLElement) => {
      const style = window.getComputedStyle(element);
      return (
        ["auto", "scroll"].includes(style.overflowY) &&
        element.scrollHeight > element.clientHeight
      );
    };

    const shouldIgnoreTouch = (target: EventTarget | null) => {
      let element = target as HTMLElement | null;
      while (element && element !== container) {
        if (
          isScrollable(element) ||
          element.dataset.preventPullToRefresh === "true"
        ) {
          return true;
        }
        element = element.parentElement;
      }
      return false;
    };

    const handleTouchStart = (e: TouchEvent) => {
      if (container.scrollTop === 0 && !shouldIgnoreTouch(e.target)) {
        isTouchingRef.current = true;
        touchStartYRef.current = e.touches[0].clientY;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isTouchingRef.current) return;

      touchCurrentYRef.current = e.touches[0].clientY;
      const pullDistance = touchCurrentYRef.current - touchStartYRef.current;

      if (pullDistance > 100 && !isRefreshing) {
        setIsRefreshing(true);
        isTouchingRef.current = false;

        onRefresh();

        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      }
    };

    const handleTouchEnd = () => {
      isTouchingRef.current = false;
      touchStartYRef.current = 0;
      touchCurrentYRef.current = 0;
    };

    container.addEventListener("touchstart", handleTouchStart, {
      passive: true
    });
    container.addEventListener("touchmove", handleTouchMove, {
      passive: true
    });
    container.addEventListener("touchend", handleTouchEnd);

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isRefreshing, onRefresh]);

  return (
    <div className="relative h-full overflow-auto" ref={containerRef}>
      {isRefreshing && (
        <div className="absolute top-0 left-0 w-full flex items-center justify-center bg-white py-2 z-50">
          {/* <span className="text-sm text-gray-600">Refreshing...</span> */}
        </div>
      )}
      {children}
    </div>
  );
}
