import React, { useState, useEffect, useRef } from "react";
import Button from "~/components/Button";
import BottomSheet from "./BottmSheet";

interface NotePopupProps {
  visible: boolean;
  initialNote: string;
  onClose: () => void;
  onSave: (note: string) => void;
  onClear?: () => void;
  maxLength?: number;
  cartKey?: string;
}

const NotePopup: React.FC<NotePopupProps> = ({
  visible,
  initialNote,
  onClose,
  onSave,
  onClear,
  maxLength = 150,
  cartKey
}) => {
  const [note, setNote] = useState(initialNote);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Focus textarea and position cursor at the end when visible
  useEffect(() => {
    if (visible && textareaRef.current) {
      textareaRef.current.focus();

      // Position cursor at the end of the text
      const length = textareaRef.current.value.length;
      textareaRef.current.setSelectionRange(length, length);
    }
  }, [visible]);

  // Reset note when initialNote changes
  useEffect(() => {
    setNote(initialNote);
  }, [initialNote]);

  // if (!visible) return null;

  const handleSave = () => {
    onSave(note);
    onClose();
  };

  const handleClear = () => {
    if (note === "") {
      onClose();
      return;
    }

    setNote("");
    if (onClear) {
      onClear();
    }
    if (cartKey) {
      localStorage.removeItem(`orderNote_${cartKey}`);
    }

    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  return (
    <BottomSheet
      isOpen={visible}
      onClose={onClose}
      backdropClassName="overflow-x-hidden"
      className="overflow-x-hidden"
      sheetType="drawer"
    >
      <div className="">
        <div className="bg-neutral-50 w-full rounded-2xl p-4">
          <h2 className="text-lg font-medium text-typography-800 mb-4">
            Add a note for the restaurant
          </h2>

          <div className="bg-white rounded-lg p-2 mb-4">
            <textarea
              ref={textareaRef}
              className="bg-gray-100 rounded-lg w-full h-32 p-2 text-typography-700 text-sm focus:outline-none resize-none"
              placeholder="e.g. Note for the entire order"
              value={note}
              onChange={(e) => setNote(e.target.value.slice(0, maxLength))}
              maxLength={maxLength}
            />
            <div className="flex justify-end">
              <span className="text-xs text-typography-500">
                {maxLength - note.length}
              </span>
            </div>
          </div>

          <p className="text-xs text-typography-600 mb-4">
            We will try our best to follow your requests. However, refunds or cancellations in this regard wont't be possible.
          </p>

          <div className="flex items-center justify-between gap-4">
            <Button
              className="flex-1 py-3 bg-white border border-neutral-300 rounded-xl text-primary font-medium"
              onClick={handleClear}
            >
              Clear
            </Button>
            <Button
              className="flex-1 py-3 bg-primary text-white rounded-xl font-medium"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        </div>
      </div>
    </BottomSheet>
  );
};

export default NotePopup;
