import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { CouponDTO } from "~/types/coupon.types";

export const CouponSuccessModal = ({
  couponData,
  open,
  onClose
}: {
  couponData: CouponDTO;
  open: boolean;
  onClose: () => void;
}) => {
  if (!open) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4"
      onClick={onClose}
      onKeyDown={(e) => {
        if (e.key === "Escape") {
          onClose();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label="Close coupon success modal"
    >
      <div
        className="bg-white rounded-lg w-full max-w-[80vw] overflow-hidden relative"
        onClick={(e) => e.stopPropagation()}
        onKeyDown={(e) => {
          if (e.key === "Escape") {
            onClose();
          }
        }}
        tabIndex={0}
        role="button"
        aria-label="Close coupon success modal"
      >
        <div className="flex flex-col items-center px-6 py-3 text-center gap-2">
          <DotLottieReact
            src="https://lottie.host/620648dc-991d-4fb4-8109-cae58c6ce5ed/Evd5o9rjUY.lottie"
            loop
            autoplay
            speed={2}
          />
          <p className="text-typography-500 text-xs">
            &lsquo;{couponData.code}&rsquo; applied
          </p>

          <h2 className="text-md font-bold text-typography-800">
            You saved ₹{couponData?.discountValue.toFixed(2)}
          </h2>

          <p className="text-typography-500 text-xs">with this coupon code.</p>

          <button
            className="text-teal-500 font-medium z-10 -my-2  px-6 pt-4 pb-2"
            onClick={onClose}
          >
            Woohoo! Thanks
          </button>
        </div>
        <div className="absolute inset-0">
          <DotLottieReact
            src="https://lottie.host/41dff85f-f65a-4e37-9f95-96722cc8c865/78ctiXySbm.lottie"
            loop
            autoplay
            speed={2}
          />
        </div>
      </div>
    </div>
  );
};
