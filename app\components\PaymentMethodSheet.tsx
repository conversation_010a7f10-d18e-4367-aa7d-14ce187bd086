import React, { useState } from "react";
import BottomSheet from "./BottmSheet";
import { ChevronDown, ChevronUp, CreditCard } from "lucide-react";
import { cn } from "~/utils/cn";

// Define the payment method types
export type PaymentMethod =
  | "google_pay"
  | "phone_pe"
  | "cod"
  | "card"
  | "netbanking"
  | string;
export type PaymentCategory = "online" | "offline";

interface PaymentMethodOption {
  id: PaymentMethod;
  name: string;
  logo: string | React.ReactNode;
  isDefault?: boolean;
  category: PaymentCategory;
}

interface PaymentMethodSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectPaymentMethod: (
    method: PaymentMethod,
    category: PaymentCategory
  ) => void;
  selectedMethod?: PaymentMethod;
  amount?: number;
}

const PaymentMethodSheet: React.FC<PaymentMethodSheetProps> = ({
  isOpen,
  onClose,
  onSelectPaymentMethod,
  selectedMethod = "cod",
  amount = 0
}) => {
  const [showOtherOptions, setShowOtherOptions] = useState(false);

  // UPI payment methods
  const primaryPaymentMethods: PaymentMethodOption[] = [
    {
      id: "google_pay",
      name: "Google Pay",
      logo: "/gpay-logo.png",
      category: "online"
    },
    {
      id: "phone_pe",
      name: "Phone Pe",
      logo: "/phonepe-logo.png",
      category: "online"
    }
  ];

  // Other payment methods
  const otherPaymentMethods: PaymentMethodOption[] = [
    {
      id: "cod",
      name: "Cash on Delivery",
      logo: "/cash_icon.svg",
      isDefault: true,
      category: "offline"
    },
    {
      id: "card",
      name: "Credit / Debit Card",
      logo: <CreditCard size={24} className="text-gray-600" />,
      category: "online"
    }
    // Add more payment methods as needed
  ];

  const handlePaymentMethodSelect = (
    method: PaymentMethod,
    category: PaymentCategory
  ) => {
    onSelectPaymentMethod(method, category);
    onClose();
  };

  const toggleOtherOptions = () => {
    setShowOtherOptions(!showOtherOptions);
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <BottomSheet isOpen={isOpen} onClose={onClose} className="px-4 pb-8">
      <div className="pt-4 pb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Select Payment Method
        </h2>

        {amount > 0 && (
          <p className="text-sm text-gray-500 mb-5">
            Amount to pay:{" "}
            <span className="font-semibold">{formatAmount(amount)}</span>
          </p>
        )}

        {/* Primary payment methods - UPI options */}
        <div className="space-y-4">
          {primaryPaymentMethods.map((method) => (
            <button
              key={method.id}
              className={cn(
                "w-full flex items-center p-4 border rounded-lg",
                selectedMethod === method.id
                  ? "border-primary bg-primary-50"
                  : "border-gray-200"
              )}
              onClick={() =>
                handlePaymentMethodSelect(method.id, method.category)
              }
            >
              <div className="w-10 h-10 p-1 mr-3 flex-shrink-0 flex items-center justify-center">
                {typeof method.logo === "string" ? (
                  <img
                    src={method.logo}
                    alt={method.name}
                    className="max-w-full max-h-full"
                  />
                ) : (
                  method.logo
                )}
              </div>
              <span className="flex-grow text-left text-gray-800">
                {method.name}
              </span>
            </button>
          ))}
        </div>

        {/* Other payment options section */}
        <div className="mt-6 border-t border-gray-200 pt-4">
          <button
            className="w-full flex items-center justify-between py-2"
            onClick={toggleOtherOptions}
            type="button"
          >
            <span className="text-gray-800 font-medium">
              Use other payment method
            </span>
            {showOtherOptions ? (
              <ChevronUp size={20} />
            ) : (
              <ChevronDown size={20} />
            )}
          </button>

          {showOtherOptions && (
            <div className="mt-4 space-y-4">
              {otherPaymentMethods.map((method) => (
                <button
                  key={method.id}
                  className={cn(
                    "w-full flex items-center p-4 border rounded-lg",
                    selectedMethod === method.id
                      ? "border-primary bg-primary-50"
                      : "border-gray-200"
                  )}
                  onClick={() =>
                    handlePaymentMethodSelect(method.id, method.category)
                  }
                >
                  <div className="w-8 h-8 mr-3 flex-shrink-0 flex items-center justify-center">
                    {typeof method.logo === "string" ? (
                      <img
                        src={method.logo}
                        alt={method.name}
                        className="max-w-full max-h-full"
                      />
                    ) : (
                      method.logo
                    )}
                  </div>
                  <span className="flex-grow text-left text-gray-800">
                    {method.name}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </BottomSheet>
  );
};

export default PaymentMethodSheet;
