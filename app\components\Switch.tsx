import React from "react";

interface SwitchProps {
  isOn: boolean;
  handleToggle: () => void;
}

const Switch: React.FC<SwitchProps> = ({ isOn, handleToggle }) => {
  return (
    <div
      className={`relative inline-block w-8 h-3 
        ${isOn ? "bg-teal-100" : "bg-gray-300"} 
        rounded-full cursor-pointer transition-colors duration-200 flex items-center justify-center`}
      onClick={handleToggle}
      onKeyDown={() => {}}
      role="button"
      tabIndex={0}
    >
      <div
        className={`absolute left-0 right-0 h-5 w-5  ${
          isOn ? "bg-teal-500" : "bg-gray-200"
        }  rounded-full shadow-lg 
          transform transition-transform duration-200 
          ${isOn ? "translate-x-4" : ""}`}
      ></div>
    </div>
  );
};

export default Switch;
