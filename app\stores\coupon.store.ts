import { create } from "zustand";
import { CouponDTO } from "~/types/coupon.types";

interface CouponState {
  coupons: CouponDTO[];
  selectedCoupon: CouponDTO | null;
  isApplied: boolean;
  showCouponDetails: boolean;
  showSuccessModal: boolean;
  searchText: string;
  error?: string;
}

interface CouponStore extends CouponState {
  selectCoupon: (couponId: number) => void;
  applyCoupon: () => void;
  removeCoupon: () => void;
  openCouponDetails: (couponId: number) => void;
  hideCouponDetails: () => void;
  hideSuccessModal: () => void;
  setSearchText: (text: string) => void;
  initializeCoupons: (coupons: CouponDTO[]) => void;
}

const initialState: CouponState = {
  coupons: [],
  selectedCoupon: null,
  isApplied: false,
  showCouponDetails: false,
  showSuccessModal: false,
  searchText: ""
};

export const useCouponStore = create<CouponStore>((set) => ({
  ...initialState,

  initializeCoupons: (coupons: CouponDTO[]) => {
    set({ coupons });
  },

  selectCoupon: (couponId: number) => {
    set((state) => {
      const coupons = state.coupons.map((coupon) => ({
        ...coupon,
        isSelected: coupon.id === couponId
      }));

      const selectedCoupon =
        coupons.find((coupon) => coupon.id === couponId) || null;

      return {
        ...state,
        coupons,
        selectedCoupon
      };
    });
  },

  applyCoupon: () => {
    set((state) => {
      if (!state.selectedCoupon) return state;

      return {
        ...state,
        isApplied: true,
        showSuccessModal: true
      };
    });
  },

  removeCoupon: () => {
    set((state) => {
      const coupons = state.coupons.map((coupon) => ({
        ...coupon,
        isSelected: false
      }));

      return {
        ...state,
        coupons,
        selectedCoupon: null,
        isApplied: false
      };
    });
  },

  openCouponDetails: (couponId: number) => {
    set((state) => {
      const selectedCoupon =
        state.coupons.find((coupon) => coupon.id === couponId) || null;

      return {
        ...state,
        selectedCoupon,
        showCouponDetails: true
      };
    });
  },

  hideCouponDetails: () => {
    set((state) => ({
      ...state,
      showCouponDetails: false
    }));
  },

  hideSuccessModal: () => {
    set((state) => ({
      ...state,
      showSuccessModal: false
    }));
  },

  setSearchText: (text: string) => {
    set((state) => ({
      ...state,
      searchText: text
    }));
  }
}));
