import { create } from "zustand";
import {
  Cart,
  AvailableItem,
  SellerOrderItem,
  AogList,
  AddonItem,
  CartItem,
  PrecheckOrderResponse
} from "~/types";
import { removeAllCarts } from "~/utils/localStorage";

interface CartStore {
  cart: Cart;
  cartKey: string | null;
  orderNote: string;
  showNoteFeature: boolean;
  cartOrderItemsMap: Map<number, SellerOrderItem>;
  cartClientType: "restaurant" | "retailer";
  totalAmount: number;
  itemCount: number;
  precheckResponse: PrecheckOrderResponse | null;
  setCart: (cart: Cart, cartKey: string) => void;
  addItem: (
    item: AvailableItem,
    cartKey: string,
    selectedAogs?: AogList[],
    variationId?: number
  ) => void;
  removeItem: (
    item: AvailableItem,
    cartKey: string,
    variationId?: number
  ) => void;
  syncCart: (availableItems: AvailableItem[], cartKey: string) => void;
  clearCart: (cartKey: string | "all") => void;
  setOrderNote: (note: string, cartKey: string) => void;
  setShowNoteFeature: (show: boolean) => void;
  setCartOrderItemsMap: (
    cartOrderItemsMap: Map<number, SellerOrderItem>
  ) => void;
  setCartClientType: (cartClientType: "restaurant" | "retailer") => void;
  getCartItem: (sellerItemId: number) => CartItem | undefined;
  setPrecheckResponse: (precheckResponse: PrecheckOrderResponse) => void;
}

// Helper function to calculate cart totals
const calculateCartTotals = (cart: Cart) => {
  let totalAmount = 0;
  let itemCount = 0;

  Object.values(cart).forEach((item) => {
    totalAmount += item.amount;
    itemCount += 1; // TODO: ask darshan if this is correct
  });

  return { totalAmount, itemCount };
};

export const useCartStore = create<CartStore>((set, get) => ({
  cart: {},
  cartKey: null,
  orderNote: "",
  showNoteFeature: false,
  cartOrderItemsMap: new Map(),
  cartClientType: "retailer",
  totalAmount: 0,
  itemCount: 0,
  precheckResponse: null,
  setCart: (cart: Cart, cartKey: string) => {
    const { totalAmount, itemCount } = calculateCartTotals(cart);

    set({ cart, totalAmount, itemCount });
    // Sync with localStorage
    if (typeof window !== "undefined" && cartKey) {
      localStorage.setItem(`cart_${cartKey}`, JSON.stringify(cart));
    }
  },

  getCartItem: (sellerItemId: number) => {
    const currentState = get();
    return currentState.cart[sellerItemId];
  },

  addItem: (
    item: AvailableItem,
    cartKey: string,
    selectedAogs?: AogList[],
    variationId?: number
  ) => {
    const currentState = get();

    // If cartKey changed, we need to load the correct cart first
    if (currentState.cartKey !== cartKey) {
      const storedCart =
        typeof window !== "undefined"
          ? localStorage.getItem(`cart_${cartKey}`)
          : null;
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          currentState.cart = parsedCart;
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    const cart = currentState.cart || {};
    const incomingVariation = item.itemVariationList?.find(
      (v) => v.id === variationId
    );
    const hasVariationUpdated =
      variationId &&
      cart[item.sellerItemId]?.variationId &&
      incomingVariation &&
      incomingVariation.id !== cart[item.sellerItemId]?.variationId;

    // Ensure we have valid cart state
    if (!cart || typeof cart !== "object") {
      console.warn("Cart is not properly initialized");
      return;
    }

    const currentQty = cart[item.sellerItemId]?.qty || 0;
    let incrementOrderQty = item.incrementOrderQty || 1;

    if (hasVariationUpdated) {
      incrementOrderQty = 0;
    }

    if (currentQty + incrementOrderQty > item.maxAvailableQty) return;

    const newQty = currentQty + incrementOrderQty;

    // Calculate base amount for the main item
    let baseAmount = newQty * item.pricePerUnit;

    // Calculate variation price if it exists
    let variationPrice = 0;

    if (incomingVariation) {
      variationPrice = incomingVariation.price;
    }

    if (variationPrice > 0) {
      baseAmount = variationPrice * newQty;
    }

    // Create a flattened list of all addon items
    const flatAddons: AddonItem[] = [];

    // Calculate add-on items total price if any are selected
    let addonsTotalPrice = 0;
    if (selectedAogs && selectedAogs.length > 0) {
      selectedAogs.forEach((aog) => {
        aog.addOnItemList.forEach((addonItem) => {
          if (addonItem.qty > 0) {
            addonsTotalPrice += addonItem.price * addonItem.qty;
            // Add to flattened list
            flatAddons.push(addonItem);
          }
        });
      });
    }

    // Total amount includes both base item and add-ons
    const newAmount = baseAmount + addonsTotalPrice * newQty;

    // Create new cart object to ensure immutability
    const updatedCart = {
      ...cart,
      [item.sellerItemId]: {
        itemId: item.sellerItemId,
        qty: newQty,
        amount: newAmount,
        cartKey,
        aogList: selectedAogs || cart[item.sellerItemId]?.aogList,
        flatAddons:
          flatAddons.length > 0
            ? flatAddons
            : cart[item.sellerItemId]?.flatAddons,
        variationId: variationId || cart[item.sellerItemId]?.variationId
      }
    };

    // Calculate new total amount and item count
    const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

    // Update state with cart, cartKey, totalAmount, and itemCount
    set({ cart: updatedCart, cartKey, totalAmount, itemCount });

    // Sync with localStorage
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
      } catch (error) {
        console.error("Error syncing cart with localStorage:", error);
      }
    }
  },

  removeItem: (item: AvailableItem, cartKey: string, variationId?: number) => {
    const currentState = get();

    // If cartKey changed, we need to load the correct cart first
    if (currentState.cartKey !== cartKey) {
      const storedCart =
        typeof window !== "undefined"
          ? localStorage.getItem(`cart_${cartKey}`)
          : null;
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          currentState.cart = parsedCart;
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    const cart = currentState.cart || {};
    const currentQty = cart[item.sellerItemId]?.qty || 0;
    const orderedQty = item.orderedQty || 0;
    const incrementOrderQty = item.incrementOrderQty || 1;

    if (currentQty <= orderedQty) return;

    const newQty = currentQty - incrementOrderQty;

    // Filter out add-on items with qty = 0 and empty add-on groups
    let existingAogList = cart[item.sellerItemId]?.aogList;

    // Create a new flattened list from filtered addons
    const flatAddons: AddonItem[] = [];

    if (existingAogList && existingAogList.length > 0) {
      existingAogList = existingAogList
        .map((aog) => ({
          ...aog,
          addOnItemList: aog.addOnItemList.filter(
            (addonItem) => addonItem.qty > 0
          )
        }))
        .filter((aog) => aog.addOnItemList.length > 0);

      // Rebuild flat addons list from filtered aogList
      existingAogList.forEach((aog) => {
        aog.addOnItemList.forEach((addon) => {
          if (addon.qty > 0) {
            flatAddons.push(addon);
          }
        });
      });
    }

    // Calculate base amount for the main item
    let baseAmount = newQty * item.pricePerUnit;

    if (variationId) {
      const variation = item.itemVariationList?.find(
        (v) => v.id === variationId
      );
      if (variation) {
        baseAmount = variation.price * newQty;
      }
    }

    // Calculate add-on items total price if any exist
    let addonsTotalPrice = 0;
    if (existingAogList && existingAogList.length > 0) {
      existingAogList.forEach((aog) => {
        aog.addOnItemList.forEach((addonItem) => {
          if (addonItem.qty > 0) {
            addonsTotalPrice += addonItem.price * addonItem.qty;
          }
        });
      });
    }

    // Total amount includes both base item and add-ons
    const newAmount = baseAmount + addonsTotalPrice * newQty;

    const updatedCart = { ...cart };
    if (newQty === 0) {
      delete updatedCart[item.sellerItemId];
    } else {
      updatedCart[item.sellerItemId] = {
        itemId: item.sellerItemId,
        qty: newQty,
        amount: newAmount,
        cartKey,
        aogList:
          existingAogList && existingAogList.length > 0
            ? existingAogList
            : undefined,
        flatAddons: flatAddons.length > 0 ? flatAddons : undefined,
        variationId: variationId || cart[item.sellerItemId]?.variationId
      };
    }

    // Calculate new total amount and item count
    const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

    // Update state with cart, cartKey, totalAmount, and itemCount
    set({ cart: updatedCart, cartKey, totalAmount, itemCount });

    if (typeof window !== "undefined") {
      localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
    }
  },

  syncCart: (availableItems: AvailableItem[], cartKey: string) => {
    let initialCart: Cart = {};

    // First, sync ordered items
    availableItems?.forEach((item) => {
      if (item.orderedQty > 0) {
        // Get add-on groups from the available item if they exist
        const aogList = item.aogList || [];

        // Create a flattened list of all addon items
        const flatAddons: AddonItem[] = [];

        // Calculate base amount for the main item
        const baseAmount = item.orderedQty * item.pricePerUnit;

        // Calculate add-on items total price
        let addonsTotalPrice = 0;
        if (aogList.length > 0) {
          aogList.forEach((aog) => {
            aog.addOnItemList.forEach((addonItem) => {
              if (addonItem.qty > 0) {
                addonsTotalPrice += addonItem.price * addonItem.qty;
                // Add to flattened list
                flatAddons.push(addonItem);
              }
            });
          });
        }

        // Total amount includes both base item and add-ons
        const totalAmount = baseAmount + addonsTotalPrice * item.orderedQty;

        initialCart[item.sellerItemId] = {
          itemId: item.sellerItemId,
          qty: item.orderedQty,
          amount: totalAmount,
          cartKey,
          aogList: aogList.length > 0 ? aogList : undefined,
          flatAddons: flatAddons.length > 0 ? flatAddons : undefined
        };
      }
    });

    // Then, sync with localStorage
    if (typeof window !== "undefined") {
      const storedCart = localStorage.getItem(`cart_${cartKey}`);
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          if (parsedCart) {
            initialCart = { ...initialCart, ...parsedCart };
          }
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    // Calculate total amount and item count
    const { totalAmount, itemCount } = calculateCartTotals(initialCart);

    // Update state with cart, cartKey, totalAmount, and itemCount
    set({ cart: initialCart, cartKey, totalAmount, itemCount });

    if (typeof window !== "undefined") {
      localStorage.setItem(`cart_${cartKey}`, JSON.stringify(initialCart));
    }
  },

  clearCart: (cartKey: string | "all") => {
    set({
      cart: {},
      cartKey: null,
      orderNote: "",
      totalAmount: 0,
      itemCount: 0
    });
    if (typeof window !== "undefined") {
      localStorage.removeItem(`cart_${cartKey}`);
      localStorage.removeItem(`orderNote_${cartKey}`);
    }
    if (cartKey === "all") {
      removeAllCarts();
    }
  },

  setOrderNote: (note: string, cartKey: string) => {
    set({ orderNote: note });
    // Sync with localStorage
    if (typeof window !== "undefined" && cartKey) {
      if (note) {
        localStorage.setItem(`orderNote_${cartKey}`, note);
      } else {
        localStorage.removeItem(`orderNote_${cartKey}`);
      }
    }
  },

  setShowNoteFeature: (show: boolean) => {
    set({ showNoteFeature: show });
  },

  setCartOrderItemsMap: (cartOrderItemsMap: Map<number, SellerOrderItem>) => {
    set({ cartOrderItemsMap });
  },

  setCartClientType: (cartClientType: "restaurant" | "retailer") => {
    set({ cartClientType });
  },

  setPrecheckResponse: (precheckResponse: PrecheckOrderResponse) => {
    set({ precheckResponse });
  }
}));
