import React, { useState } from 'react';

const AddToHomeScreenBanner = () => {
    const [showBanner, setShowBanner] = useState(true);

    const handleDismiss = () => {
        setShowBanner(false);
    };

    if (!showBanner) return null;

    return (
        <div className="fixed bottom-16 left-0 right-0 bg-blue-500 text-white p-2 flex justify-between items-center z-40">
            <span className="text-sm">Add this app to your home screen!</span>
            <div>
                <button
                    onClick={() => alert("To add to home screen:\n\n• On iOS: Tap the share icon and choose 'Add to Home Screen'\n\n• On Android: Tap the menu icon and choose 'Add to Home screen'")}
                    className="bg-white text-blue-500 px-2 py-1 rounded text-xs mr-2"
                >
                    How to Add
                </button>
                <button
                    onClick={handleDismiss}
                    className="bg-blue-600 text-white px-2 py-1 rounded text-xs"
                >
                    Dismiss
                </button>
            </div>
        </div>
    );
};

export default AddToHomeScreenBanner;
