import { useAppConfigStore } from "~/stores/appConfig.store";
import { useEffect, useRef, useState, useCallback } from "react";
import { getItem, setItem } from "~/utils/localStorage";
import { useWhatsAppCTA } from "~/stores/appConfig.store";
// import PrimaryButton from "./PrimaryButton";

export const handleWhatsappClick = (mobileNumber: string, text?: string) => {
  const countryCode = mobileNumber.includes("+91");
  window.location.replace(
    `https://wa.me/${countryCode ? "" : "+91"}${mobileNumber?.trim()}${
      text ? `?text=${encodeURIComponent(text)}` : ""
    }`
  );
};

interface Position {
  x: number;
  y: number;
}

// Debounce function to limit how often a function can be called
const debounce = <F extends (...args: Parameters<F>) => ReturnType<F>>(
  func: F,
  delay: number
): ((...args: Parameters<F>) => void) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return (...args: Parameters<F>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
      timeoutId = null;
    }, delay);
  };
};

const WhatsappCTA = ({ mobileNumber }: { mobileNumber?: string }) => {
  const { networkConfig } = useAppConfigStore((state) => state);
  const showWhatsappCTA = useWhatsAppCTA();
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const dragRef = useRef<HTMLDivElement>(null);
  const initialPositionRef = useRef<Position>({ x: 0, y: 0 });
  const dragStartPositionRef = useRef<Position>({ x: 0, y: 0 });
  const lastUpdateTimeRef = useRef<number>(0);

  // Debounced save function to reduce localStorage operations
  const debouncedSave = useCallback(
    debounce((newPosition: Position) => {
      setItem<Position>("whatsappButtonPosition", newPosition);
    }, 300),
    []
  );

  // Load saved position from localStorage on mount
  useEffect(() => {
    const savedPosition = getItem<Position>("whatsappButtonPosition");
    if (savedPosition) {
      setPosition(savedPosition);
    }
  }, []);

  // Save position to localStorage when it changes (debounced)
  useEffect(() => {
    if (!isDragging && (position.x !== 0 || position.y !== 0)) {
      debouncedSave(position);
    }
  }, [position, isDragging, debouncedSave]);

  const handleDragStart = useCallback(
    (clientX: number, clientY: number) => {
      if (!dragRef.current) return;

      setIsDragging(true);
      initialPositionRef.current = { ...position };
      dragStartPositionRef.current = { x: clientX, y: clientY };

      // Add a class to the body to prevent scrolling while dragging
      document.body.classList.add("no-scroll");

      if (dragRef.current) {
        // Set will-change to hint the browser about the transformation
        dragRef.current.style.willChange = "transform";
      }
    },
    [position]
  );

  const handleDragMove = useCallback(
    (clientX: number, clientY: number) => {
      if (!isDragging || !dragRef.current) return;

      // Use requestAnimationFrame for smoother animations
      requestAnimationFrame(() => {
        const now = Date.now();
        // Throttle updates to 60fps (approximately 16ms between frames)
        if (now - lastUpdateTimeRef.current < 16) {
          return;
        }
        lastUpdateTimeRef.current = now;

        const deltaX = clientX - dragStartPositionRef.current.x;
        const deltaY = clientY - dragStartPositionRef.current.y;

        const newPosition = {
          x: initialPositionRef.current.x + deltaX,
          y: initialPositionRef.current.y + deltaY
        };

        // Ensure button stays within viewport bounds
        if (dragRef.current) {
          const button = dragRef.current;
          const buttonRect = button.getBoundingClientRect();
          const viewportWidth = window.innerWidth;
          const viewportHeight = window.innerHeight;

          // Constrain x position
          if (newPosition.x < -viewportWidth / 2 + buttonRect.width / 2) {
            newPosition.x = -viewportWidth / 2 + buttonRect.width / 2;
          } else if (newPosition.x > viewportWidth / 2 - buttonRect.width / 2) {
            newPosition.x = viewportWidth / 2 - buttonRect.width / 2;
          }

          // Constrain y position
          if (newPosition.y < -viewportHeight / 2 + buttonRect.height / 2) {
            newPosition.y = -viewportHeight / 2 + buttonRect.height / 2;
          } else if (
            newPosition.y >
            viewportHeight / 2 - buttonRect.height / 2
          ) {
            newPosition.y = viewportHeight / 2 - buttonRect.height / 2;
          }
        }

        setPosition(newPosition);
      });
    },
    [isDragging]
  );

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    document.body.classList.remove("no-scroll");

    if (dragRef.current) {
      // Reset will-change to its default value
      dragRef.current.style.willChange = "auto";
    }
  }, []);

  // Mouse event handlers
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault(); // Prevent default behavior
      handleDragStart(e.clientX, e.clientY);
    },
    [handleDragStart]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      e.preventDefault(); // Prevent default behavior
      handleDragMove(e.clientX, e.clientY);
    },
    [handleDragMove]
  );

  const handleMouseUp = useCallback(
    (e: MouseEvent) => {
      e.preventDefault(); // Prevent default behavior
      handleDragEnd();
    },
    [handleDragEnd]
  );

  // Touch event handlers with passive: false for better mobile performance
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      // Don't prevent default here as it would break clicking the button
      if (e.touches.length === 1) {
        const touch = e.touches[0];
        handleDragStart(touch.clientX, touch.clientY);
      }
    },
    [handleDragStart]
  );

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (isDragging) {
        e.preventDefault(); // Only prevent default if we're actually dragging
      }

      if (e.touches.length === 1) {
        const touch = e.touches[0];
        handleDragMove(touch.clientX, touch.clientY);
      }
    },
    [handleDragMove, isDragging]
  );

  const handleTouchEnd = useCallback(() => {
    handleDragEnd();
  }, [handleDragEnd]);

  // Add and remove global event listeners
  useEffect(() => {
    if (isDragging) {
      window.addEventListener("mousemove", handleMouseMove, { passive: false });
      window.addEventListener("mouseup", handleMouseUp);
      window.addEventListener("touchmove", handleTouchMove, { passive: false });
      window.addEventListener("touchend", handleTouchEnd);
    }

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("touchmove", handleTouchMove);
      window.removeEventListener("touchend", handleTouchEnd);
    };
  }, [
    isDragging,
    handleMouseMove,
    handleMouseUp,
    handleTouchMove,
    handleTouchEnd
  ]);

  // Add global CSS once for no-scroll class
  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      .no-scroll {
        overflow: hidden;
        touch-action: none;
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (style.parentNode === document.head) {
        document.head.removeChild(style);
      }
    };
  }, []);

  const positionStyle = {
    transform:
      position.x !== 0 || position.y !== 0
        ? `translate3d(${position.x}px, ${position.y}px, 0)`
        : "none",
    transition: isDragging ? "none" : "opacity 0.2s ease, transform 0.1s ease",
    willChange: isDragging ? "transform" : "auto"
  };

  // If the CTA should not be shown based on our rules, return null
  if (!showWhatsappCTA) {
    return null;
  }

  return (
    <div
      ref={dragRef}
      className={`flex z-50 fixed bottom-[25%] right-[5%] opacity-85 items-center justify-center w-12 h-12 bg-teal-500 text-white rounded-full shadow-lg cursor-move transition-transform ${
        isDragging ? "opacity-90" : "hover:scale-105 hover:opacity-100"
      } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2`}
      style={positionStyle}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      role="button"
      tabIndex={0}
      aria-label="Drag WhatsApp button"
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
        }
      }}
    >
      <button
        onClick={() =>
          handleWhatsappClick(
            networkConfig?.wabMobileNumber || mobileNumber || ""
          )
        }
        className="w-full h-full flex items-center justify-center"
        aria-label="Chat on WhatsApp"
      >
        <img
          src="/whatsapp_icon.svg"
          alt="WhatsApp"
          className="w-5 h-5 text-white"
        />
      </button>
    </div>
  );
};

export default WhatsappCTA;
