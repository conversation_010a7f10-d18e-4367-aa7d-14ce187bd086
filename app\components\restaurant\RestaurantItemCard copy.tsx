import React from "react";
import { AvailableItem, Cart } from "~/types";
import CustomImage from "../CustomImage";

interface RestaurantItemCardProps {
  item: AvailableItem;
  cart: Cart;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing?: boolean;
  badgeType?: "bestseller" | "spicy" | "vegetarian" | "non-vegetarian" | "none";
  showRating?: boolean;
  rating?: number;
  amount?: number;
}

const RestaurantItemCard: React.FC<RestaurantItemCardProps> = ({
  item,
  cart,
  onAdd,
  onRemove,
  approxPricing = false,
  badgeType = "none",
  showRating = false,
  rating = 0,
  amount = 0
}) => {
  const qty = cart[item.sellerItemId]?.qty || 0;

  // Format price to remove decimal if it's a whole number
  const formatPrice = (price: number) =>
    price % 1 === 0 ? price.toFixed(0) : price.toFixed(2);

  // Get badge based on type
  const renderBadge = () => {
    if (badgeType === "none") return null;

    let badgeClass =
      "inline-flex items-center px-2 py-1 text-xs font-medium rounded-md";
    let icon = null;
    let text = "";

    switch (badgeType) {
      case "bestseller":
        badgeClass += " bg-teal-100 text-teal-800";
        text = "Bestseller";
        break;
      case "spicy":
        badgeClass += " bg-orange-100 text-orange-800";
        icon = <span className="mr-1">🌶️</span>;
        break;
      case "vegetarian":
        badgeClass += " bg-red-100 text-red-800";
        break;
      case "non-vegetarian":
        badgeClass += " bg-red-100 text-red-800";
        break;
      default:
        return null;
    }

    return (
      <div className={badgeClass}>
        {icon}
        {text}
      </div>
    );
  };

  // Render star rating
  const renderRating = () => {
    if (!showRating) return null;

    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? "text-yellow-400" : "text-gray-300"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col gap-2 relative bg-white rounded-md shadow-md p-3 w-full">
      {/* Badge for categories */}
      <div className="absolute top-3 left-3">{renderBadge()}</div>

      <div className="flex flex-row items-start w-full gap-2">
        <div className="flex flex-grow flex-col justify-between pl-2 w-full">
          {/* Product name and details */}
          <div className="flex flex-grow flex-col items-start text-start w-full">
            <h2 className="text-sm font-semibold text-typography-900 line-clamp-2 mt-6">
              {item.itemName}
            </h2>
            <p className="text-xs text-typography-300 mt-1">
              {item.itemRegionalLanguageName}
            </p>
            <p className="text-sm text-typography-500 mt-1">{item.packaging}</p>

            {/* Description */}
            <p className="text-xs text-gray-600 mt-2">
              A steamed egg, spicy Habanero sauce, on toasted buns, a protein
              packed delight! .
            </p>

            {/* Rating stars if applicable */}
            {renderRating()}
          </div>

          {/* Price and Add button */}
          <div className="w-full self-end align-bottom mt-2">
            <div className="flex flex-col w-full">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <span className="text-base font-medium text-typography-900">
                    ₹{formatPrice(item.pricePerUnit)}
                  </span>
                  {approxPricing && (
                    <span className="text-xs text-gray-400 ml-1">(approx)</span>
                  )}
                </div>

                <button
                  onClick={() => onAdd(item)}
                  disabled={item.soldout || item.closed}
                  className="bg-teal-100 text-teal-800 hover:bg-teal-200 font-medium py-1 px-5 rounded-full text-sm"
                >
                  ADD
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product image */}
        <div className="relative">
          <CustomImage
            src={item.itemUrl}
            alt={item.itemName}
            className="w-24 h-24 object-cover rounded-md"
          />
          {(item.soldout || item.closed) && (
            <div className="absolute inset-0 bg-white bg-opacity-60 rounded-lg flex items-center justify-center">
              <span className="text-sm font-medium text-red-600">
                {item.soldout ? "Sold Out" : "Not Available"}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RestaurantItemCard;
