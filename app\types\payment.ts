import { Order, PrecheckOrderResponse } from "~/types";

export interface PaymentInitiateRequest {
  amount: number;
  orderGroupId?: number;
  note?: string;
}

export interface PaymentStatusRequest {
  refId: number;
}

export interface PaymentResponse {
  paymentUrl?: string;
  refId?: number;
  error?: string;
  errors?: {
    [key: string]: string;
  };
  message?: string;
  success?: boolean;
  paymentStatus?: "SUCCESS" | "FAILED" | "PENDING" | "SUCCESS_NOT_CNF";
}

export interface PaymentState {
  isOpen: boolean;
  isLoading: boolean;
  status: "idle" | "processing" | "success" | "failed" | "success_not_cnf";
  message: string;
  refId?: number;
  isProcessing: boolean;
}

export interface UsePaymentOptions {
  onToast?: (message: string, type: "success" | "error") => void;
}

// Helper functions to convert from different types to PaymentInitiateRequest
export const isValidOrderForPayment = (order: unknown): order is Order => {
  return (
    order !== null &&
    typeof order === "object" &&
    "balanceTobePaid" in order &&
    typeof (order as Order).balanceTobePaid === "number" &&
    "id" in order &&
    typeof (order as Order).id === "number" &&
    "id" in order &&
    typeof (order as Order).id === "number"
  );
};

export const orderToPaymentRequest = (order: Order): PaymentInitiateRequest => {
  if (!isValidOrderForPayment(order)) {
    console.error("Invalid order for payment:", order);
    throw new Error("Invalid order for payment: Missing required properties");
  }

  return {
    amount: order.balanceTobePaid,
    orderGroupId: order.id,
    note: `Initiating payment for order ${order.id}`
  };
};

export const isValidPrecheckResponseForPayment = (
  precheck: unknown
): precheck is PrecheckOrderResponse => {
  return (
    precheck !== null &&
    typeof precheck === "object" &&
    "totalAmount" in precheck &&
    typeof (precheck as PrecheckOrderResponse).totalAmount === "number" &&
    "preconfirmUid" in precheck &&
    typeof (precheck as PrecheckOrderResponse).preconfirmUid === "string"
  );
};

export const precheckResponseToPaymentRequest = (
  precheck: PrecheckOrderResponse
): PaymentInitiateRequest => {
  if (!isValidPrecheckResponseForPayment(precheck)) {
    console.error("Invalid precheck response for payment:", precheck);
    throw new Error(
      "Invalid precheck response for payment: Missing required properties"
    );
  }

  return {
    amount: precheck.balancePayableAmount,
    note: `Initiating payment for preconfirmUid ${precheck.preconfirmUid}`
  };
};
