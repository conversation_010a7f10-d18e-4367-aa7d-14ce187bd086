import AddItemButton from "./AddItemButton";
import React, { useState } from "react";
import { AvailableItem, Cart } from "~/types";
import Button from "../Button";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules";
import GetBadge from "./GetBadge";
import { cn } from "~/utils/cn";
// import styles from "~/styles/swiper.css?url";
// import { LinksFunction } from "@remix-run/node";

// export const links: LinksFunction = () => {
//   return [{ rel: "stylesheet", href: styles }];
// };

interface ItemDetailsProps {
  items: AvailableItem[];
  cart: Cart;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  displayPrices?: boolean;
  setShowItemDetails: (value: boolean) => void;
}

const ItemDetails: React.FC<ItemDetailsProps> = ({
  items,
  cart,
  onAdd,
  onRemove,
  approxPricing,
  displayPrices
}) => {
  const [selectedUnit, setSelectedUnit] = useState(items[0]);

  const itemDetails = items?.[0];

  return (
    <div className="flex flex-col rounded-t-lg bg-gray-100 text-lg">
      <div className="rounded-md mb-2 w-full">
        <ItemInfo
          items={items}
          approxPricing={approxPricing}
          displayPrices={displayPrices}
          selectedUnit={selectedUnit}
          setSelectedUnit={setSelectedUnit}
          cart={cart} // Pass cart to ItemInfo
        />
      </div>
      <div className="flex w-full justify-between bg-white py-2 px-2 rounded-md">
        <div className="flex flex-col justify-center">
          {displayPrices !== false && <>
            <span className="text-xs">
              {selectedUnit.unitWtFactor} {selectedUnit.unit}
            </span>
            <span className="font-bold text-xs text-gray-800">{`₹ ${selectedUnit.pricePerUnit}`}</span>
          </>
          }
        </div>
        <AddItemButton
          qty={cart[selectedUnit.sellerItemId]?.qty || 0}
          onAdd={() => onAdd(selectedUnit)}
          onRemove={() => onRemove(selectedUnit)}
          isDisabled={itemDetails.closed || itemDetails.soldout}
          unit={itemDetails.unit}
        />
      </div>
    </div>
  );
};

// Component for displaying item image, name, and badges
const ItemInfo: React.FC<{
  items: AvailableItem[];
  selectedUnit: AvailableItem;
  setSelectedUnit: (item: AvailableItem) => void;
  approxPricing: boolean;
  displayPrices?: boolean;
  cart: Cart; // Add cart to props
}> = ({ items, approxPricing, displayPrices, selectedUnit, setSelectedUnit, cart }) => {
  const itemDetails = items?.[0];
  const imageUrls = itemDetails?.itemUrl?.split(",") || [];
  if (imageUrls.length === 1) {
    imageUrls.push(imageUrls[0]);
  }

  return (
    <div className="flex flex-col w-full h-full gap-2">
      <div className=" w-full flex overflow-hidden  bg-white rounded-md px-2">
        <Swiper
          pagination={{
            clickable: true
            //   bulletClass: "custom-bullet",
            //   bulletActiveClass: "custom-bullet-active"
          }}
          className="mySwiper"
          modules={[Autoplay, Pagination]}
          // autoplay={{ delay: 3000, disableOnInteraction: false }}
          loop={true}
          spaceBetween={10}
          slidesPerView={1}
        >
          {imageUrls.map((image, index) => (
            <SwiperSlide key={index}>
              <img
                className="rounded-lg w-full max-h-96 mb-12 object-contain"
                src={image}
                alt={`Slide ${index + 1}`}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      <div className="rounded-md bg-white p-2">
        <div className="flex-1">
          <h2 className="text-md font-bold text-gray-800 ">
            {itemDetails.itemName}
          </h2>
          <p className="text-xs text-gray-500 mt-1">
            {itemDetails.itemRegionalLanguageName}
          </p>
        </div>
        {displayPrices !== false && <SelectUnit
          items={items}
          approxPricing={approxPricing}
          selectedUnit={selectedUnit}
          setSelectedUnit={setSelectedUnit}
          cart={cart} // Pass cart to SelectUnit
        />}
      </div>
    </div>
  );
};

const SelectUnit: React.FC<{
  items: AvailableItem[];
  approxPricing: boolean;
  selectedUnit: AvailableItem;
  setSelectedUnit: (item: AvailableItem) => void;
  cart: Cart; // Add cart to props
}> = ({ items, approxPricing, selectedUnit, setSelectedUnit, cart }) => {
  return (
    <>
      <span className="font-semibold text-sm ">Select Unit</span>
      <div className="flex w-full my-2 gap-4">
        {items.map((item) => (
          <UnitDetails
            key={item.sellerItemId}
            item={item}
            approxPricing={approxPricing}
            selectedUnit={selectedUnit}
            setSelectedUnit={setSelectedUnit}
            cart={cart} // Pass cart to UnitDetails
          />
        ))}
      </div>
    </>
  );
};

interface UnitDetailsProps {
  item: AvailableItem;
  approxPricing: boolean;
  selectedUnit: AvailableItem;
  setSelectedUnit: (item: AvailableItem) => void;
  cart: Cart; // Add cart to props
}

const UnitDetails: React.FC<UnitDetailsProps> = ({
  item,
  approxPricing,
  selectedUnit,
  setSelectedUnit,
  cart // Destructure cart from props
}) => {
  const itemQty = cart[item.sellerItemId]?.qty || 0; // Get item quantity from cart

  return (
    <div className="relative">
      {/* Show Badges */}
      <GetBadge
        className="w-full rounded-md text-start text-[.6rem] leading-3 py-1 -top-2 z-10"
        itemDetails={item}
      />
      {itemQty > 0 && (
        <span className="absolute -top-3 -right-3 bg-primary text-white text-xs font-semibold rounded-full w-6 h-6 flex items-center justify-center z-10 shadow-lg border-2 border-gray-100">
          {itemQty}
        </span>
      )}
      <Button
        disabled={item.soldout || item.closed}
        key={item.sellerItemId}
        onClick={() => setSelectedUnit(item)}
        className={cn(
          "flex flex-col py-4 px-2 min-w-28 border  rounded-lg items-start justify-center shadow-md",
          selectedUnit.sellerItemId === item.sellerItemId
            ? "border-primary bg-primary-50 "
            : "border-gary-400"
        )}
      >
        <span className="text-[.6rem] leading-3 text-gray-800 font-semibold mb-1">
          {item.packaging}
        </span>
        <div className="flex items-center">
          <span className="text-xs text-gray-500">
            <span className="text-xs font-semibold text-gray-800">
              ₹ {item.pricePerUnit}
            </span>
            /{item.unit}
          </span>
          {item.discPerc > 0 && (
            <span className="text-[.62rem] leading-3 font-light text-gray-400 line-through ml-2">
              ₹ {item.strikeoffPrice}/{item.unit}
            </span>
          )}
        </div>
        {approxPricing && (
          <span className="text-[.62rem] leading-3 text-gray-400 ml-1">
            (approx)
          </span>
        )}
      </Button>
    </div>
  );
};

export default ItemDetails;
