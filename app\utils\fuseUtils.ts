import type { AvailableItem } from "~/types";
import type Fuse from "fuse.js";

// Interface for search options
export interface FuseSearchOptions {
  threshold?: number;
  maxResults?: number;
  searchFields?: {
    name: string;
    weight: number;
  }[];
}

// Cache to store Fuse instances for reuse
const fuseInstanceCache = new Map<string, Promise<Fuse<unknown>>>();

/**
 * Dynamically import Fuse.js to avoid loading it unnecessarily
 * This uses code splitting to reduce the initial bundle size
 */
async function loadFuse(): Promise<typeof Fuse> {
  // Dynamic import of Fuse.js
  const module = await import("fuse.js");
  return module.default;
}

/**
 * Generate a cache key for a Fuse instance based on items and options
 */
function createFuseInstanceKey(
  items: unknown[],
  options?: FuseSearchOptions
): string {
  return `fuse-${items.length}-${options?.threshold || 0.6}-${
    options?.maxResults || 50
  }`;
}

/**
 * Create or retrieve a cached Fuse instance
 */
export async function getFuseInstance<T>(
  items: T[],
  options?: FuseSearchOptions
): Promise<Fuse<T>> {
  const cacheKey = createFuseInstanceKey(items, options);

  // Return cached instance if available
  if (fuseInstanceCache.has(cacheKey)) {
    // Cast is needed here because we're storing generic instances with unknown type
    return fuseInstanceCache.get(cacheKey) as Promise<Fuse<T>>;
  }

  // Create a new instance
  const fusePromise = createFuseInstance(items, options);
  fuseInstanceCache.set(cacheKey, fusePromise as Promise<Fuse<unknown>>);

  // Clean up cache if it gets too large
  if (fuseInstanceCache.size > 10) {
    const oldestKey = fuseInstanceCache.keys().next().value;
    fuseInstanceCache.delete(oldestKey);
  }

  return fusePromise;
}

/**
 * Create a new Fuse instance with the provided items and options
 */
async function createFuseInstance<T>(
  items: T[],
  options?: FuseSearchOptions
): Promise<Fuse<T>> {
  const Fuse = await loadFuse();

  const searchFields = options?.searchFields || [
    { name: "itemName", weight: 3 },
    { name: "packaging", weight: 1 },
    { name: "description", weight: 1 }
  ];

  const fuseOptions = {
    keys: searchFields,
    includeScore: true,
    shouldSort: true,
    tokenize: true,
    matchAllTokens: false,
    threshold: options?.threshold || 0.6,
    location: 0,
    distance: 200,
    minMatchCharLength: 1,
    useExtendedSearch: true,
    ignoreLocation: true
  };

  return new Fuse(items, fuseOptions);
}

/**
 * Perform a fuzzy search using Fuse.js
 */
export async function performFuzzySearch<T>(
  items: T[],
  searchText: string,
  options?: FuseSearchOptions
): Promise<T[]> {
  // Don't load Fuse.js if there's no search text or no items
  if (!searchText || !items.length) {
    return items;
  }

  try {
    const fuse = await getFuseInstance(items, options);
    // Fuse.js requires a string, ensure we always pass one
    const query = typeof searchText === "string" ? searchText : "";
    const results = fuse.search(query);

    // Map results and limit by maxResults option
    return results
      .map((result) => result.item)
      .slice(0, options?.maxResults || 50);
  } catch (error) {
    console.error("Error performing fuzzy search:", error);
    // Fallback to returning all items if fuzzy search fails
    return items;
  }
}

/**
 * Helper function specifically for searching available items
 */
export async function searchAvailableItems(
  items: AvailableItem[],
  searchText: string,
  options?: FuseSearchOptions
): Promise<AvailableItem[]> {
  return performFuzzySearch(items, searchText, options);
}
