export interface ApplyCouponPayload {
  preconfirmUid: string;
  couponId: number;
  active: boolean;
}

export interface CouponDTO {
  id: number;
  code: string;
  title: string;
  description: string;
  discountValue: number;
  minOrderValue: number;
  maxDiscountValue: number;
  isActive: boolean;
  type?: "FLAT" | "PERCENTAGE" | "BOGO";
  expiryDate?: string;
  isSelected?: boolean;
  icon?: string;
  terms?: string[];
}

export interface AppliedCouponDTO {
  success: boolean;
  message: string;
  coupon: CouponDTO;
}

export interface CouponState {
  coupons: CouponDTO[];
  selectedCoupon: CouponDTO | null;
  isApplied: boolean;
  showCouponDetails: boolean;
  showSuccessModal: boolean;
  searchText: string;
  error?: string;
}

export interface CouponContextType {
  state: CouponState;
  selectCoupon: (couponId: number) => void;
  applyCoupon: () => void;
  removeCoupon: () => void;
  showCouponDetails: (couponId: number) => void;
  hideCouponDetails: () => void;
  hideSuccessModal: () => void;
  setSearchText: (text: string) => void;
}
