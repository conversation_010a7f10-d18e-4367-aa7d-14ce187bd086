import React from "react";
import { ItemCategoryDtos } from "~/types";
import CategoryItem from "./CategoryItem";
import CategoryItemV2 from "./CategoryItemV2";
import CategoryItemL2 from "./CategoryItemL2";

interface CategoryListProps {
  categoriesL3: ItemCategoryDtos[];
  categoriesL2: ItemCategoryDtos[];
  selectedCategoryId: number | undefined;
  onSelectCategory: (categoryId: number) => void;
}
export const CategoryListL2: React.FC<CategoryListProps> = ({
  categoriesL3,
  categoriesL2,
  selectedCategoryId,
  onSelectCategory
}) => {
  return (
    <div className="overflow-y-scroll flex flex-col w-full px-4 h-full">
      {categoriesL3?.length ? (
        categoriesL3?.map((l3, i) => (
          <div className="" key={i}>
            <div className="my-4">
              <span className="text-sm text-gray-700 font-semibold mt-4">
                {l3.name}
              </span>
            </div>
            <div className="grid gap-4 grid-cols-[repeat(auto-fit,minmax(5rem,max-content))]">
              {categoriesL2
                ?.filter((l2) => l2.parentCategories?.includes(l3.id))
                .map((category) => (
                  <CategoryItemL2
                    key={category.id}
                    category={category}
                    isSelected={selectedCategoryId === category.id}
                    onSelect={() => onSelectCategory(category.id)}
                  />
                ))}
            </div>
          </div>
        ))
      ) : (
        <div className="">
          <div className="my-4">
            <span className="text-sm text-gray-700 font-semibold">
              {"All Categories"}
            </span>
          </div>
          <div className="grid gap-4 grid-cols-[repeat(auto-fit,minmax(5rem,max-content))]">
            {categoriesL2?.map((category) => (
              <CategoryItemL2
                key={category.id}
                category={category}
                isSelected={selectedCategoryId === category.id}
                onSelect={() => onSelectCategory(category.id)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
