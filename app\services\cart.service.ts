import { apiRequest, getApiUrl } from "@utils/api";
import { PrecheckOrderResponse } from "~/types";
import { getDomainFromRequest } from "@utils/domain";
import { ApiResponse } from "~/types/Api";
import { ApplyCouponPayload } from "~/types/coupon.types";

const MOCK_API_BASE_URL =
  "https://6bfe032c-8f37-477f-bb19-bf17621c9d3e.mock.pstmn.io";

export async function applyCouponAPI(
  payload: ApplyCouponPayload,
  request: Request
): Promise<ApiResponse<PrecheckOrderResponse>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params = {};
  const url = getApiUrl(
    "/precheckorder/coupon",
    undefined,
    params,
    hasSubdomain ? domain : undefined
  );

  // const url = `https://6bfe032c-8f37-477f-bb19-bf17621c9d3e.mock.pstmn.io/buyer/d/restaurant.mnetlive.com/precheckorder/coupon`;

  try {
    const response = await apiRequest<PrecheckOrderResponse>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );

    return response;
  } catch (error) {
    console.error("Error confirming order:", error);
    throw error;
  }
}
