import { useNavigate } from "@remix-run/react";
import Button from "./Button";
import { ReactElement } from "react";
import { ArrowLeft } from "lucide-react";
import { cn } from "~/utils/cn";
export function BackNavHeader({
  children,
  backButton = true,
  buttonText,
  handleBack,
  rightText,
  rightTextStyle,
  className,
  pageName,
  rightButton
}: {
  children?: ReactElement;
  backButton?: boolean;
  buttonText?: string;
  handleBack?: () => void;
  rightText?: string | ReactElement;
  rightTextStyle?: string;
  className?: string;
  pageName?: string;
  rightButton?: ReactElement;
}) {
  const navigate = useNavigate();

  const handleBackNav = () => {
    navigate(-1);
  };

  return (
    <>
      {pageName === "SRP" ? (
        <div className={`shadow-md p-2 min-h-14 ${className}`}>
          <div className="flex items-center justify-between p-1 h-full">
            <div className="flex flex-row items-center justify-center">
              {backButton && (
                <Button
                  type="button"
                  onClick={handleBack ?? handleBackNav}
                  className="mr-2"
                >
                  <ArrowLeft size={24} />
                </Button>
              )}
              <h1 className="text-lg">{buttonText}</h1>
            </div>

            <div>
              <span className={`${rightTextStyle}`}>{rightText}</span>
            </div>
          </div>
          {children}
        </div>
      ) : (
        <div
          className={cn(
            `shadow-md p-2 bg-white min-h-14`,
            className ? className : ""
          )}
        >
          <div className="flex items-center justify-between p-1 h-full">
            <div className="flex flex-row items-center justify-center">
              {backButton && (
                <Button
                  type="button"
                  onClick={handleBack ?? handleBackNav}
                  className="mr-2 text-teal-500"
                >
                  {/* <ArrowLeft size={24} /> */}
                  <img src="/Back.svg" alt="Back" className="w-6 h-6" />
                </Button>
              )}
              <h1 className="text-lg font-semibold text-typography-700">{buttonText}</h1>
            </div>

            <div>
              <span className={`${rightTextStyle}`}>{rightText}</span>
              {rightButton}
            </div>
          </div>
          {children}
        </div>
      )}
    </>
  );
}

