import { json, LoaderFunction } from "@remix-run/node";
import { useState } from "react";
import { Form, useLoaderData } from "@remix-run/react";
import RazorpayCheckout from "~/components/RazorpayCheckout";
import type { PaymentRequest, RazorpayResponse } from "~/types/payment.types";
import { BackNavHeader } from "~/components/BackNavHeader";
import { getSession } from "~/utils/session.server";
import { requireAuth } from "~/utils/clientReponse";
import Toast from "~/components/Toast";

// Loader to ensure authenticated user
export const loader: LoaderFunction = async ({ request }) => {
  // Check if user is authenticated
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token");

  const auth = await requireAuth(request, "", false);
  if (auth?.authRequired) {
    return json(auth);
  }

  if (!access_token) {
    return json({ isAuthenticated: false });
  }

  return json({ isAuthenticated: true });
};

export default function TestPayment() {
  const { isAuthenticated } = useLoaderData<{ isAuthenticated: boolean }>();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    amount: ""
  });
  const [paymentRequest, setPaymentRequest] = useState<PaymentRequest | null>(
    null
  );
  const [paymentResult, setPaymentResult] = useState<{
    status: "success" | "failure";
    message: string;
    paymentId?: string;
  } | null>(null);
  const [showToast, setShowToast] = useState(false);

  // Handle form changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (
      !formData.name ||
      !formData.email ||
      !formData.phone ||
      !formData.amount
    ) {
      setPaymentResult({
        status: "failure",
        message: "Please fill in all fields"
      });
      setShowToast(true);
      return;
    }

    // Create payment request
    const request: PaymentRequest = {
      id: `order_${Date.now()}`,
      amount: Number(formData.amount),
      currency: "INR",
      receipt: `receipt_${Date.now()}`,
      customerName: formData.name,
      customerEmail: formData.email,
      customerContact: formData.phone,
      description: "Test Payment",
      notes: {
        source: "test-payment"
      }
    };

    setPaymentRequest(request);
    setPaymentResult(null);
  };

  // Handle successful payment
  const handlePaymentSuccess = (response: RazorpayResponse) => {
    setPaymentResult({
      status: "success",
      message: "Payment successful!",
      paymentId: response.razorpay_payment_id
    });
    setShowToast(true);
    setPaymentRequest(null); // Reset payment request
  };

  // Handle payment failure
  const handlePaymentFailure = (error: Error) => {
    setPaymentResult({
      status: "failure",
      message: error.message || "Payment failed"
    });
    setShowToast(true);
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-yellow-100 text-yellow-800 p-4 rounded mb-4">
          You need to be logged in to access this page.
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-[#F2F4F9]">
      <BackNavHeader
        buttonText="Test Payment"
        handleBack={() => window.history.back()}
      />

      <div className="container max-w-md mx-auto p-6 bg-white rounded-lg shadow-md mt-4">
        <h2 className="text-xl font-semibold mb-6 text-center">
          Razorpay Test Payment
        </h2>

        {!paymentRequest ? (
          <Form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Full Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary bg-white"
                placeholder="John Doe"
                required
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary bg-white"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary bg-white"
                placeholder="9999999999"
                required
              />
            </div>

            <div className="mb-6">
              <label
                htmlFor="amount"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Amount (₹)
              </label>
              <input
                type="number"
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary bg-white"
                placeholder="100"
                min="1"
                required
              />
            </div>

            <button
              type="submit"
              className="w-full p-3 bg-primary text-white rounded-lg hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary"
            >
              Proceed to Payment
            </button>
          </Form>
        ) : (
          <div className="text-center">
            <p className="mb-4 text-gray-700">
              You&apos;re about to pay{" "}
              <span className="font-bold">₹{paymentRequest.amount}</span>
            </p>
            <RazorpayCheckout
              paymentRequest={paymentRequest}
              onSuccess={handlePaymentSuccess}
              onFailure={handlePaymentFailure}
              onClose={() => setPaymentRequest(null)}
              buttonText="Pay Now"
              buttonClassName="w-full p-3 bg-primary text-white rounded-lg hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary"
              companyName="BRIH Solutions"
            />
            <button
              onClick={() => setPaymentRequest(null)}
              className="w-full p-3 mt-2 border border-gray-300 rounded-lg hover:bg-gray-100"
            >
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* Toast for displaying payment results */}
      {paymentResult && showToast && (
        <Toast
          message={
            paymentResult.status === "success"
              ? `${paymentResult.message} Payment ID: ${paymentResult.paymentId}`
              : paymentResult.message
          }
          type={paymentResult.status === "success" ? "success" : "error"}
          duration={5000}
          onClose={() => setShowToast(false)}
          position="bottom-center"
          showIcon
          width="full"
          autoClose={true}
        />
      )}
    </div>
  );
}
