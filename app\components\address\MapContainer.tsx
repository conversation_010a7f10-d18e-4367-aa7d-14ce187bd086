import React, { useEffect } from "react";
import MapPicker from "~/components/MapPicker";
// import { CurrentLocationButton } from "./CurrentLocationButton";
import { GoogleMapsWrapper } from "./GoogleMapsWrapper";

interface MapContainerProps {
  latitude?: string | number;
  longitude?: string | number;
  onLocationSelect: (lat: string, lng: string, address: string) => void;
  onCurrentLocationRequest: () => void;
  isLoadingCurrentLocation: boolean;
  searchFocused?: boolean;
  googleMapsApiKey: string;
}

/**
 * A container component that wraps the MapPicker and adds a CurrentLocationButton
 */
export const MapContainer: React.FC<MapContainerProps> = ({
  latitude,
  longitude,
  onLocationSelect,
  onCurrentLocationRequest,
  isLoadingCurrentLocation,
  searchFocused = false,
  googleMapsApiKey
}) => {
  // Convert latitude and longitude to string if they exist
  const lat = latitude !== undefined ? String(latitude) : "";
  const lng = longitude !== undefined ? String(longitude) : "";

  // Monitor latitude/longitude changes for debugging
  useEffect(() => {
    if (lat && lng) {
      console.log("MapContainer received coordinates:", { lat, lng });
    }
  }, [lat, lng]);

  const handleLocationSelect = (lat: string, lng: string, address: string) => {
    console.log("MapContainer: location selected", { lat, lng, address });
    onLocationSelect(lat, lng, address);
  };

  const handleCurrentLocationClick = () => {
    console.log("MapContainer: current location button clicked");
    onCurrentLocationRequest();
  };

  return (
    <div className="flex-1 relative h-full">
      <GoogleMapsWrapper googleMapsApiKey={googleMapsApiKey}>
        <MapPicker
          googleMapsApiKey={googleMapsApiKey}
          latitude={lat}
          longitude={lng}
          onLocationSelect={handleLocationSelect}
          onCurrentLocationClick={handleCurrentLocationClick}
          isLoadingCurrentLocationParent={isLoadingCurrentLocation}
          searchFocused={searchFocused}
        />

        {/* Current Location Button */}
        {/* <div className="absolute bottom-24 right-4 z-10">
          <CurrentLocationButton
            onClick={handleCurrentLocationClick}
            isLoading={isLoadingCurrentLocation}
          />
        </div> */}
      </GoogleMapsWrapper>
    </div>
  );
};
