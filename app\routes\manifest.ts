import type { LoaderFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getNetworkConfig } from "~/services/auth.server";

export const loader: LoaderFunction = async ({ request }) => {
  // 1. Fetch your network/theme config
  const apiRes = await getNetworkConfig(request);
  const cfg = apiRes.data;

  // 2. Build your PWA manifest
  const manifest = {
    //name: cfg.businessName ?? "mNet Buyer",
    name: cfg.pwaAppName ?? cfg.domain.replace(/\..+$/, "") ?? "mNet Buyer",
    short_name: cfg.pwaAppShortName ?? cfg.pwaAppName ?? cfg.domain.replace(/\..+$/, ""),    // e.g. “restaurant”
    description: cfg.pwaAppDiscription ?? "A web application from your neighbourhood businesses",
    start_url: cfg.ondcDomain==="RET11"? "/home/<USER>": cfg.defaultStartPage === "chooseitems" ? "/chooseitems?utm_source=pwa_app" : "/home/<USER>",
    scope: "/", 
    display: "standalone",
    background_color: "#ffffff",                   // you could pull these from cfg too
    theme_color: "#000000",
    icons: [
      {
        src: cfg.pwaAppIcon,
        sizes: cfg.pwaAppIconSize ?? "1024x1024",
        type: cfg.pwaAppIconType ?? "image/png",
        purpose:"any"
      } 
      //,
      // {
      //   src: cfg.pwaAppIcon,
      //   sizes: "1024x1024",
      //   type: "image/png"
      // }
    ],
    screenshots: [
    // {
    //   "src": "/assets/screenshots/feature-wide.png",
    //   "sizes": "1440x720",
    //   "type": "image/png",
    //   "form_factor": "wide"          // desktop/tablet preview
    // },
    {
      "src": cfg.ondcDomain==="RET11"?"/assets/screenshots/feature-mobile.png":"/assets/screenshots/feature-mobile-non-ret.png",
      "sizes": "720x1440",
      "type": "image/png",
      "form_factor": "wide" // mobile preview
    },
    {
      "src": cfg.ondcDomain==="RET11"?"/assets/screenshots/feature-mobile.png" : "/assets/screenshots/feature-mobile-non-ret.png",
      "sizes": "720x1440",
      "type": "image/png",
      "form_factor": "narrow" // mobile preview
    }
  ]
  };

  // 3. Return it as application/manifest+json
  return new Response(JSON.stringify(manifest), {
    status: 200,
    headers: {
      "Content-Type": "application/manifest+json",
      // optionally cache for an hour; tweak as needed
      "Cache-Control": "public, max-age=5"
    }
  });
};