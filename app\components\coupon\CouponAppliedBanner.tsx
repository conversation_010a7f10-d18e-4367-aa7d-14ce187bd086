import { Link } from "@remix-run/react";
import { useCouponStore } from "~/stores/coupon.store";

interface CouponAppliedBannerProps {
  className?: string;
}

export const CouponAppliedBanner = ({
  className = ""
}: CouponAppliedBannerProps) => {
  const { selectedCoupon, isApplied, removeCoupon } = useCouponStore();

  if (!isApplied || !selectedCoupon) return null;

  return (
    <div className={`bg-blue-50 p-4 rounded-md ${className}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <div className="w-8 h-8 mr-3 flex-shrink-0">
            <div className="w-full h-full bg-teal-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">₹</span>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-700">Coupon selected by you</p>
            <p className="text-sm font-medium text-gray-900">
              {selectedCoupon.type === "BOGO"
                ? "Buy 1 Get 1 Free"
                : `Save ₹${selectedCoupon.amount} with this '${selectedCoupon.code}'`}
            </p>
          </div>
        </div>
        <button
          onClick={removeCoupon}
          className="text-gray-400 hover:text-gray-600"
          aria-label="Remove coupon"
        >
          <svg
            className="w-5 h-5"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            ></path>
          </svg>
        </button>
      </div>
      <Link
        to="/coupons"
        className="mt-2 text-xs text-teal-600 underline inline-block"
      >
        View all coupons
      </Link>
    </div>
  );
};
