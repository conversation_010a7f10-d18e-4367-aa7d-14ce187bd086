import { useEffect, useState } from "react";
import { NetworkConfig } from "~/types";
import { getItem } from "~/utils/localStorage";
export function NetworkAsset({
  assetName
}: {
  assetName: "footer" | "businessLogo" | "banner" | "loginBanner";
}) {
  const [image, setImage] = useState("");
  const networkConfig = getItem<NetworkConfig>("networkConfig");
  // TODO: move to util
  useEffect(() => {
    if (assetName === "footer") {
      networkConfig?.footerAppIcon && setImage(networkConfig?.footerAppIcon);
    } else if (assetName === "businessLogo" || assetName === "loginBanner") {
      networkConfig?.businessLogo && setImage(networkConfig?.businessLogo);
    } else if (assetName === "banner") {
      networkConfig?.homePageBanner && setImage(networkConfig?.homePageBanner);
    }
  }, [networkConfig, assetName]);

  if (assetName === "banner") {
    return (
      <div className="flex justify-center items-center h-full w-full">
        <img
          src={image}
          alt=""
          className="h-44 w-44 rounded-full shadow-lg object-fit animate-bounce"
        />
      </div>
    );
  }

  if (assetName === "loginBanner") {
    return (
      <div className="flex justify-center items-start h-full w-full mt-[20vh]">
        <div className="flex-1 overflow-hidden flex flex-col items-center justify-center p-4">
          <h1 className=" text-gray-700 text-3xl mb-16">Welcome to</h1>
          <div className="h-64 w-auto mb-6">
            <NetworkAsset assetName="businessLogo" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {image === "" ? (
        <div className="flex justify-center items-center h-full w-full bg-gray-200 rounded-lg">
          <div className="w-64 h-4 bg-gray-300 animate-pulse rounded-lg m-4"></div>
        </div>
      ) : (
        <div className="flex justify-center h-full w-full">
          <img src={image} alt="" className=" h-full w-full" />
        </div>
      )}
    </>
  );
}
