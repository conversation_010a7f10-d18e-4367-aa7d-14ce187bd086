import React from "react";

interface DeliveryTimeDisplayProps {
  time: string;
}

const DeliveryTimeDisplay: React.FC<DeliveryTimeDisplayProps> = ({ time }) => {
  return (
    <div className="px-3 py-2 bg-[#F7FFFD]">
      <div className="flex items-center justify-start gap-2">
        <img src="/lightning-bolt-con.svg" alt="lightning-bolt-con" />
        <>
          <p className="text-sm font-medium text-primary">Delivery in</p>
          <p className="text-sm font-bold text-primary">{time}</p>
        </>
      </div>
    </div>
  );
};

export default DeliveryTimeDisplay;
