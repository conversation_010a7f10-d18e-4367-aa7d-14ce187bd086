import SearchItems from "./SearchItems";
import { ImageViewToggle } from "./ImageViewToggle";
import { ImageViewType } from "~/types";
import { useState } from "react";
import { cn } from "~/utils/cn";

interface SearchHeaderProps {
  isScrolled: boolean;
  categoryType: string | null;
  imageViewType: ImageViewType;
  searchStr: string;
  itemOptionsData?: {
    deliveryDate: string | null;
    sellerId: string | null;
  };
  setSearchPage: (page: "L1" | "L2") => void;
  onSearchSelect: (searchStr: string) => void;
  onSearchClear: () => void;
  onViewChange: (view: ImageViewType) => void;
}

export const SearchWithCardView = ({
  categoryType,
  imageViewType,
  searchStr,
  setSearchPage,
  onSearchSelect,
  onSearchClear,
  onViewChange
}: SearchHeaderProps) => {
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  return (
    <div className={`px-3 pt-1 pb-3 flex flex-row justify-between gap-2 `}>
      <div
        className={cn(
          "transition-all duration-300 ease-in-out",
          isSearchFocused ? "flex-grow" : "w-full"
        )}
      >
        <SearchItems
          className="w-full"
          searchValue={searchStr}
          onClick={() => {
            if (categoryType === "L1") {
              setSearchPage("L1");
            } else {
              setSearchPage("L2");
            }
          }}
          onSelect={onSearchSelect}
          onClear={onSearchClear}
          onFocus={() => setIsSearchFocused(true)}
          onBlur={() => setIsSearchFocused(false)}
        />
      </div>
      <div
        className={`transition-all duration-500 ease-in-out flex items-center ${
          isSearchFocused ? "translate-x-80 w-0" : "translate-x-0"
        }`}
      >
        {categoryType === "L1" || categoryType === "L0" ? (
          <ImageViewToggle
            imageViewType={imageViewType}
            onViewChange={onViewChange}
          />
        ) : null}
      </div>
    </div>
  );
};

export default SearchWithCardView;
