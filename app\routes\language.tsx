// app/routes/home.account.tsx

import React, { useState } from "react";
import { useLoader<PERSON><PERSON>, useF<PERSON>cher, json } from "@remix-run/react";
import { ActionFunction, LoaderFunction, redirect } from "@remix-run/node";
import { getSession } from "~/utils/session.server";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken } from "~/types/user";
import { BackNavHeader } from "~/components/BackNavHeader";
import { Check } from "lucide-react";
import { getLanguage, updateLanguage } from "~/services/buyer.service";
import { User } from "~/types";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { useRequireAuth } from "~/hooks/useRequireAuth";
const languages: Language[] = [
  { title: "हिन्दी", name: "hindi", code: "HI" },
  { title: "English", name: "english", code: "E<PERSON>" },
  { title: "मराठी", name: "marathi", code: "MA" },
  { title: "ગુજરાતી", name: "gujarati", code: "G<PERSON>" },
  { title: "తెలుగు", name: "telugu", code: "TE" },
  { title: "தமிழ்", name: "tamil", code: "TA" },
  { title: "ಕನ್ನಡ", name: "kannada", code: "KN" },
  { title: "മലയാളം", name: "malayalam", code: "ML" },
  { title: "বাংলা", name: "bangla", code: "BG" }
];

interface LoaderData {
  language: Language;
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, language: {} });
  }

  if (!access_token || !user) {
    return redirect("/home/<USER>");
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;
    if (!decoded || !decoded.userDetails) {
      return redirect("/home");
    }

    const response = await getLanguage(request);
    const currentLanguageName = response.data;

    let language: Language | undefined = undefined;

    if (currentLanguageName) {
      language = languages.find((l) => l.name === currentLanguageName);
    } else {
      language = languages[1];
    }

    return createClientResponse(request, { language }, response);
  } catch (error: unknown) {
    console.error("Error decoding access_token:", error);
    return redirect("/home");
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, success: false, error: "Authentication required" });
  }

  if (!access_token || !user) {
    return redirect("/login");
  }
  const formData = await request.formData();
  const languageName = formData.get("language") as string;

  const response = await updateLanguage(languageName, request);

  return createClientResponse(request, {}, response, "/home/<USER>");
};

export default function Language() {
  const fetcher = useFetcher();
  const loader = useLoaderData<LoaderData>();
  const selectedLang = loader.language as Language;

  const [language, setLanguage] = useState<Language>(selectedLang);
  const [loading, setLoading] = useState<boolean>(false);

  const selectLanguage = (lan: Language) => {
    setLanguage(lan);
  };

  const handleSaveLanguage = (selectedLanguage: Language) => {
    setLoading(true);
    const formData = new FormData();
    formData.append("language", selectedLanguage.name);
    fetcher.submit(formData, { method: "POST" });
  };

  return (
    <div className="h-screen">
      <BackNavHeader />
      <div className="bg-gray-100 px-2">
        <LanguageSelector
          languages={languages}
          selectedLanguage={language}
          onSelectLanguage={selectLanguage}
          onSaveLanguage={handleSaveLanguage}
          loading={loading}
        />
      </div>
    </div>
  );
}

interface Language {
  title: string;
  name: string;
  code: string;
}

interface LanguageSelectorProps {
  languages: Language[];
  selectedLanguage: Language;
  onSelectLanguage: (language: Language) => void;
  onSaveLanguage: (language: Language) => void;
  loading: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  languages,
  selectedLanguage,
  onSelectLanguage,
  onSaveLanguage
}) => {
  useRequireAuth();

  const groupedLanguages = languages?.reduce((rows, language, index) => {
    if (index % 2 === 0) rows.push([language]);
    else rows[rows.length - 1].push(language);
    return rows;
  }, [] as Language[][]);

  return (
    <div className="p-4 w-full flex flex-col">
      <h3 className="text-xs font-medium text-gray-900 mb-4">
        Select your preferred language
      </h3>
      <div className="grow">
        {groupedLanguages.map((languagePair) => (
          <div className="flex justify-between mb-4" key={languagePair[0].name}>
            {languagePair.map((language) => (
              <LanguageCard
                key={language.name}
                language={language}
                onSelect={() => onSelectLanguage(language)}
                selected={selectedLanguage.name === language.name}
              />
            ))}
          </div>
        ))}
      </div>
      <button
        onClick={() => onSaveLanguage(selectedLanguage)}
        className="bg-teal-500 text-white font-medium py-2 px-4 rounded mt-6 w-full h-12 self-end"
      >
        Set Language
      </button>
      {/* {loading && <PageLoading />} */}
    </div>
  );
};

// Component for each language card
const LanguageCard: React.FC<{
  language: Language;
  onSelect: () => void;
  selected: boolean;
}> = ({ language, onSelect, selected }) => (
  <div
    tabIndex={0}
    role="button"
    onKeyDown={() => {}}
    onClick={onSelect}
    className={`cursor-pointer p-2 rounded-lg shadow-md w-40 h-20 ${
      selected ? "bg-teal-50 border border-teal-500" : "bg-white"
    }`}
  >
    <div className="flex flex-row mb-6 w-full justify-between">
      <div className="flex flex-col">
        <span className={"text-md font-normal text-gray-700"}>
          {language.title}
        </span>
        <span className={"text-xs font-light text-gray-500"}>
          {language.name}
        </span>
      </div>
      <div className="p-1">
        {selected ? (
          <div className="w-4 h-4 flex items-center justify-center rounded-full text-white bg-teal-500">
            <Check size={14} />
          </div>
        ) : (
          <div className="w-4 h-4 rounded-full border border-gray-300"></div>
        )}
      </div>
    </div>
  </div>
);
