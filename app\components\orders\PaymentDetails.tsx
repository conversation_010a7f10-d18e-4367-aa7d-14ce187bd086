import { FC } from "react";
import BillRow from "./BillRow";

interface PaymentDetailsProps {
  codAmount: number;
  onlineAmount: number;
  delayPaymentPendingAmount: number;
}

const PaymentDetails: FC<PaymentDetailsProps> = ({
  codAmount,
  onlineAmount,
  delayPaymentPendingAmount
}) => {
  if (codAmount + onlineAmount === 0 && delayPaymentPendingAmount === 0) {
    return null;
  }

  return (
    <>
      {codAmount + onlineAmount > 0 && (
        <BillRow label="Paid" value="" className="border-t border-gray-300">
          <div className="flex gap-4">
            <div className="flex flex-col items-center">
              <span className="text-xs text-gray-900">CoD</span>
              <span className="text-xs text-blue-500">₹ {codAmount}</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs text-gray-900">Direct / Online</span>
              <span className="text-xs text-blue-500">₹ {onlineAmount}</span>
            </div>
          </div>
        </BillRow>
      )}

      {delayPaymentPendingAmount > 0 && (
        <BillRow
          label="Balance to be paid"
          value={delayPaymentPendingAmount}
          valueColor="text-red-600"
          className="border-t border-gray-400"
        />
      )}
    </>
  );
};

export default PaymentDetails;
