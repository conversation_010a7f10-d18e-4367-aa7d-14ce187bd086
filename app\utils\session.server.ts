// app/utils/session.server.ts

import { createCookieSessionStorage } from "@remix-run/node";

const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) {
  throw new Error("SESSION_SECRET must be set");
}

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "__session",
    secure: process.env.NODE_ENV !== "production" ? false : true, // Disable Secure flag for non-HTTPS
    secrets: [sessionSecret],
    sameSite: process.env.NODE_ENV !== "production" ? "lax" : "none", // Keep SameSite as 'lax' for broader compatibility
    path: "/",
    maxAge: 60 * 60 * 24 * 7, // 7 days
    httpOnly: true,
    domain: process.env.NODE_ENV === "production" ? ".mnetlive.com" : undefined
  }
});

export const { getSession, commitSession, destroySession } = sessionStorage;
