import React, { useEffect, useRef, useState } from "react";
import { Search, X, Mic } from "lucide-react";
import SpeechRecognition, {
  useSpeechRecognition
} from "react-speech-recognition";

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onClear: () => void;
  placeholder?: string;
  className?: string;
  isLoading?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  onClear,
  placeholder = "Search for dishes...",
  className = "",
  isLoading = false
}) => {
  const {
    transcript,
    listening,
    browserSupportsSpeechRecognition,
    resetTranscript
  } = useSpeechRecognition();

  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isFocused, setIsFocused] = useState(false);

  const startListening = () => {
    resetTranscript();
    SpeechRecognition.startListening({ continuous: true });
  };

  const stopListening = () => {
    SpeechRecognition.stopListening();
    resetTranscript();
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }
  };

  // Sync transcript with parent value and reset on silence
  useEffect(() => {
    if (transcript) {
      onChange(transcript);

      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
      }

      // Stop listening after 3 seconds of silence
      silenceTimeoutRef.current = setTimeout(() => {
        stopListening();
      }, 3000);
    }
  }, [transcript, onChange]);

  // if (!browserSupportsSpeechRecognition) {
  //   return <p>Voice search is not supported in this browser.</p>;
  // }

  const handleClear = () => {
    stopListening();
    onClear();
  };

  return (
    <div className={`relative w-full ${className} bg-primary px-3 py-2`}>
      <div className="relative flex items-center w-full">
        <div
          className={`absolute left-3 top-1/2 transform -translate-y-1/2 text-primary h-5 w-5 transition-opacity duration-200 ${
            isLoading ? "opacity-0" : "opacity-100"
          }`}
        >
          <Search className="h-5 w-5" />
        </div>

        {isLoading && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary h-5 w-5">
            <div className="h-5 w-5 rounded-full border-2 border-t-primary border-r-primary border-b-transparent border-l-transparent animate-spin"></div>
          </div>
        )}

        {/* Input */}
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          enterKeyHint="search"
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              e.currentTarget.blur();
            }
          }}
          className={`w-full pl-10 pr-10 py-2 border ${
            isFocused ? "border-primary" : "border-gray-300"
          } rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white transition-all duration-200`}
        />

        {/* Vertical Divider */}
        {browserSupportsSpeechRecognition && (
          <div className="absolute right-12 top-1/2 -translate-y-1/2 h-5 border-l border-gray-300" />
        )}

        {/* Clear Button */}
        {value && (
          <button
            onClick={handleClear}
            className={`absolute ${
              browserSupportsSpeechRecognition ? "right-16" : "right-4"
            } top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600`}
            aria-label="Clear search"
          >
            <X className="h-5 w-5" />
          </button>
        )}

        {/* Mic Button */}
        {browserSupportsSpeechRecognition && (
          <button
            onClick={listening ? stopListening : startListening}
            className={`absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors duration-300 
            ${
              listening ? "text-red-500 animate-pulse" : "text-gray-400"
            } hover:text-primary-600`}
            aria-label={listening ? "Stop listening" : "Start voice search"}
          >
            <Mic className="h-5 w-5" />
          </button>
        )}
      </div>
    </div>
  );
};

export default SearchBar;
