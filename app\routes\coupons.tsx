import { json } from "@remix-run/node";
import type { ActionFunction, LoaderFunction } from "@remix-run/node";
import { CouponDTO } from "~/types/coupon.types";

const couponsMockData: CouponDTO[] = [
  {
    id: 1,
    code: "WELCOME125",
    title: "Flat ₹125 OFF",
    description: "Save ₹125 with this code",
    discountValue: 125,
    minOrderValue: 500,
    maxDiscountValue: 125,
    isActive: true,
    type: "FLAT",
    terms: [
      "Minimum order value of ₹500",
      "Valid only for first-time users",
      "Cannot be combined with other offers",
      "Valid until 31st December 2023"
    ]
  },
  {
    id: 2,
    code: "BOGO",
    title: "Buy 1 Get 1 Free",
    description: "Add items from Deal of the day menu to claim this offer",
    discountValue: 0,
    minOrderValue: 0,
    maxDiscountValue: 0,
    isActive: true,
    type: "BOGO",
    terms: [
      "Add a minimum of 2 items from the Deal of the Day menu to avail this offer",
      "The lower priced item will be FREE",
      "This coupon cannot be clubbed with other coupons",
      "No coupon code required"
    ]
  },
  {
    id: 3,
    code: "SUMMER50",
    title: "Flat ₹50 OFF",
    description: "Save ₹50 on your order",
    discountValue: 50,
    minOrderValue: 300,
    maxDiscountValue: 50,
    isActive: true,
    type: "FLAT",
    terms: [
      "Minimum order value of ₹300",
      "Valid until 30th June 2023",
      "Cannot be combined with other offers"
    ]
  },
  {
    id: 4,
    code: "SAVE10",
    title: "10% OFF",
    description: "Save 10% on your order",
    discountValue: 10,
    minOrderValue: 200,
    maxDiscountValue: 100,
    isActive: true,
    type: "PERCENTAGE",
    terms: [
      "Maximum discount of ₹100",
      "Minimum order value of ₹200",
      "Valid until 31st December 2023"
    ]
  }
];

interface LoaderData {
  coupons: CouponDTO[];
}

interface ActionData {
  success?: boolean;
  error?: string;
  appliedCoupon?: CouponDTO;
}

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const couponId = formData.get("couponId")
    ? Number(formData.get("couponId"))
    : null;

  if (!couponId) {
    return json<ActionData>({
      success: false,
      error: "No coupon selected"
    });
  }

  // In a real app, you would validate the coupon against the current cart
  // and user information from the database
  const selectedCoupon = couponsMockData.find(
    (coupon) => coupon.id === couponId
  );

  if (!selectedCoupon) {
    return json<ActionData>({
      success: false,
      error: "Invalid coupon"
    });
  }

  return json<ActionData>({
    success: true,
    appliedCoupon: selectedCoupon
  });
};

export const loader: LoaderFunction = async () => {
  // In a real app, you would fetch coupons from an API or database
  const coupons: CouponDTO[] = couponsMockData;

  return json<LoaderData>({ coupons });
};

export default function CouponsPage() {
  // const { coupons } = useLoaderData<LoaderData>();
  // const { initializeCoupons } = useCouponStore();

  // useEffect(() => {
  //   // Initialize coupons in the store
  //   initializeCoupons(coupons);
  //   // Scroll to top on mount
  //   window.scrollTo(0, 0);
  // }, [coupons, initializeCoupons]);

  return (
    <div>
      <div>Coupons</div>
    </div>
  );
}
