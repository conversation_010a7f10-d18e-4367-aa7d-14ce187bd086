import React from "react";

interface PendingCardProps {
  buyerHomeData: {
    pendingPaymentsCount: number;
    pendingPaymentsAmount: number;
  };
  onPress: (tabName: string) => void;
}

const PendingCard: React.FC<PendingCardProps> = ({
  buyerHomeData,
  onPress,
}) => {
  return (
    <div className="w-full max-w-sm mx-auto my-4">
      <button
        className="w-full bg-red-50 border border-red-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
        onClick={() => onPress("pending")}
      >
        <div className="p-6 flex flex-col items-start text-left">
          <div className="flex items-center w-full mb-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-red-500 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <h3 className="text-sm font-semibold text-gray-800 flex-grow">
              Pending Payments
            </h3>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
          <p className="text-sm font-medium text-red-600 mt-2">
            {buyerHomeData.pendingPaymentsCount} Orders
          </p>
          <p className="text-sm font-semibold text-red-700 mt-1">
            ₹ {buyerHomeData.pendingPaymentsAmount.toLocaleString("en-IN")}
          </p>
        </div>
      </button>
    </div>
  );
};

export default PendingCard;
