import React, { useEffect, useState } from "react";

interface GoogleMapsWrapperProps {
  googleMapsApiKey: string;
  children: React.ReactNode;
  onMapsLoaded?: () => void;
}

/**
 * A component that loads the Google Maps API and ensures it's ready
 * before rendering children that depend on it
 */
export const GoogleMapsWrapper: React.FC<GoogleMapsWrapperProps> = ({
  googleMapsApiKey,
  children,
  onMapsLoaded
}) => {
  const [mapsLoaded, setMapsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);

  useEffect(() => {
    // Check if Google Maps is already loaded
    if (window.google?.maps) {
      console.log("Google Maps already loaded");
      setMapsLoaded(true);
      onMapsLoaded?.();
      return;
    }

    // Load Google Maps script
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log("Google Maps loaded successfully");
      setMapsLoaded(true);
      onMapsLoaded?.();
    };

    script.onerror = (error) => {
      console.error("Error loading Google Maps:", error);
      setLoadError("Failed to load Google Maps. Please try again later.");
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script tag when component unmounts
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [googleMapsApiKey, onMapsLoaded]);

  if (loadError) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100">
        <div className="text-center p-4">
          <p className="text-red-500 mb-2">{loadError}</p>
          <button
            className="bg-blue-500 text-white px-4 py-2 rounded"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!mapsLoaded) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="w-12 h-12 border-4 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return <>{children}</>;
};
