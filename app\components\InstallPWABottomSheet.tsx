// InstallPWABottomSheet.tsx
import { useAppConfigStore } from "~/stores/appConfig.store";
import BottomSheet from "./BottmSheet";
import InstallPWAiOSInstruction from "./InstallPWAiOSInstruction";
import { useInstallPWA } from "~/hooks/useInstallPWA";

interface InstallPWABottomSheetProps {
  themeColor?: string;
  title?: string;
  subtitle?: string;
}

export default function InstallPWABottomSheet({
  themeColor = "Primary",
  title = "Get The App",
  subtitle = "For Better Experience"
}: InstallPWABottomSheetProps) {
  const DISMISS_KEY = "installPWABottomSheetDismissedAt";
  const DISMISS_DURATION = 48 * 60 * 60 * 1000; // 48Hr in ms

  const { networkConfig } = useAppConfigStore();

  const {
    visible,
    isFirefox,
    isSafari,
    showInstructionSheet,
    setShowInstructionSheet,
    handleInstall,
    handleDismiss
  } = useInstallPWA({
    dismissKey: DISMISS_KEY,
    dismissDuration: DISMISS_DURATION
  });

  if (networkConfig?.enableInstallPwa !== true) {
    return null;
  }
  if (!visible) return null;

  return (
    <>
      <BottomSheet
        isOpen={true}
        onClose={handleDismiss}
        className="w-full p-0 bg-white"
        showSwipeIndicator={false}
        sheetType="drawer"
        showCloseButton={false}
      >
        {/* — your Chrome or manual CTA UI */}
        <div className="flex flex-col gap-0">
          <img src="/install_bg.svg" className="w-full object-cover" />
          <div className="relative flex flex-col gap-3 p-3 pt-8 items-center border-t-4 border-white">
            <img
              src={networkConfig?.footerAppIcon}
              alt="App Icon"
              className="absolute -top-14 w-20 aspect-square rounded-lg outline outline-white outline-[0.375rem]"
            />
            <div className="pt-2 text-sm font-normal text-typography-400">
              Get the {networkConfig?.domain.split(".")[0]} app for
            </div>
            <div className="flex flex-col gap-1 items-center text-center pt-0 p-2">
              <div className="text-xl font-semibold text-typography-800">
                Better Experience
              </div>
              <div className="flex gap-2 text-base font-normal text-typography-300 tracking-wider items-center">
                LIVE TRACKING{" "}
                <span className="block w-1 h-1 rounded-full bg-gray-400"></span>{" "}
                EXCLUSIVE OFFERS
              </div>
            </div>
            <div className="w-full flex flex-col items-center gap-2 p-2">
              <button
                onClick={handleInstall}
                className="p-3 bg-primary font-semibold text-white w-[90%] rounded-lg"
              >
                Download the app now
              </button>
              <button
                onClick={handleDismiss}
                className="p-2 pb-3 font-semibold bg-white text-primary w-[90%] rounded-lg"
              >
                Continue on web
              </button>
            </div>
          </div>
        </div>
      </BottomSheet>

      {/* Fallback sheet for in‑depth instructions */}
      <BottomSheet
        isOpen={showInstructionSheet}
        onClose={() => setShowInstructionSheet(false)}
        className="w-full p-2 bg-neutral-100"
        sheetType="drawer"
      >
        <InstallPWAiOSInstruction
          browser={isFirefox ? "firefox" : isSafari ? "safari" : "browser"}
          platform={isSafari ? "iOS" : "desktop"}
        />
      </BottomSheet>
    </>
  );
}
