import { useFetcher, useNavigate } from "@remix-run/react";
import { useEffect, useState } from "react";
import { ActionData } from "~/types";
import Button from "../Button";
import { Check, ChevronRight } from "lucide-react";
import { BackNavHeader } from "../BackNavHeader";
import { useAppConfigStore } from "~/stores/appConfig.store";
interface IIssueType {
  id: number;
  name: string;
  icon: string;
}
const issueTypes: IIssueType[] = [
  { id: 1, name: "Quality Issue", icon: "quality.svg" },
  { id: 2, name: "Delivery Issue", icon: "delivery.svg" },
  { id: 3, name: "Driver / Sales Behaviour Issue", icon: "driver.svg" },
  { id: 4, name: "Request Call Back", icon: "call_back.svg" },
  { id: 5, name: "Others", icon: "others.svg" }
];

export default function CreateSupportTicket({
  setCreateTicket,
  orderId = ""
}: {
  setCreateTicket: (value: boolean) => void;
  orderId?: string;
}) {
  const fetcher = useFetcher<ActionData<undefined>>();
  const navigate = useNavigate();
  const { appDomain } = useAppConfigStore((state) => state);
  const [issueType, setIssueType] = useState<IIssueType>({
    id: 4,
    name: "Request Call Back",
    icon: "call_back.svg"
  });
  const [showSelectIssue, setShowSelectIssue] = useState(
    orderId ? false : true
  );
  const [issueDescription, setIssueDescription] = useState("");
  const [requestCallback, setRequestCallback] = useState(true);

  const handleRequestTicket = () => {
    const formData = new FormData();
    formData.append("issueType", issueType.name);
    formData.append("description", issueDescription);
    formData.append("requestCallback", requestCallback ? "1" : "0");
    if (orderId) {
      formData.append("orderGroupId", orderId);
    }
    fetcher.submit(formData, { method: "POST" });
  };

  const handleIssueTypeChange = (issueType: IIssueType) => {
    setIssueType(issueType);
    setShowSelectIssue(false);
  };

  const handleRequestCallback = () => {
    // making non clickable
    setRequestCallback(requestCallback);
  };

  useEffect(() => {
    if (fetcher.data?.success === true) {
      setCreateTicket(false);
      navigate("/help");
    }
  }, [fetcher.data?.success, navigate, setCreateTicket]);

  // Set "Request Call Back" as the selected issue type when opening with action=create
  useEffect(() => {
    if (orderId || appDomain === "RET11") {
      setIssueType({
        id: 4,
        name: "Request Call Back",
        icon: "call_back.svg"
      });
      setShowSelectIssue(false);
    }
  }, [orderId, appDomain]);

  return (
    <div
      className={`${showSelectIssue ? " bg-white" : "bg-gray-100"} flex-grow`}
    >
      <BackNavHeader
        buttonText="Create Support Ticket"
        handleBack={() => setCreateTicket(false)}
      />
      {showSelectIssue ? (
        <SelectIssueType handleIssueTypeChange={handleIssueTypeChange} />
      ) : (
        <div className="flex flex-col">
          <div className="self-start flex flex-row px-2 py-6 bg-white w-full">
            <img className="mr-2" src={issueType.icon} alt="" />
            <span className="text-md text-teal-600 ">{issueType.name}</span>
          </div>
          <div className="flex-grow flex flex-col bg-white px-4 py-6 mt-2">
            <span className="font-medium mb-2">Describe your issue</span>
            <textarea
              value={issueDescription}
              onChange={(e) => setIssueDescription(e.target.value)}
              className="bg-white border rounded-md h-28 p-2 text-sm font-light outline-none focus-within:border-teal-600 "
              placeholder="Enter Complete Details"
            ></textarea>
            {orderId && (
              <div className="text-xs text-gray-400 mt-1">
                Order #{orderId} will be linked to this ticket
              </div>
            )}
          </div>
          <div className="flex-grow flex justify-between items-center bg-white px-4 py-6 mt-2">
            <div className="flex flex-col">
              <span className="font-medium mb-2">Request Call Back?</span>
              <span className="text-xs text-gray-500 font-light mb-2 max-w-60">
                You will receive a call from our our agents after looking into
                the issue
              </span>
            </div>
            <Button
              onClick={handleRequestCallback}
              className={`rounded-md border flex items-center justify-center border-teal-600 h-10 w-10 ${
                requestCallback ? "bg-teal-600" : "bg-white"
              }`}
              disabled={true}
            >
              {requestCallback && <Check color="white" />}
            </Button>
          </div>
          <div className="flex justify-center w-full">
            <Button
              onClick={handleRequestTicket}
              className="text-white font-medium text-sm  px-8 py-3 bg-teal-600 rounded-lg mt-8 w-full mx-6"
            >
              {fetcher.state === "submitting"
                ? "Submitting..."
                : "REQUEST CALLBACK"}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

function IssueTypeCard({
  issueType,
  onClick
}: {
  issueType: IIssueType;
  onClick: (issueType: IIssueType) => void;
}) {
  return (
    <Button
      onClick={() => onClick(issueType)}
      className="flex flex-row justify-between px-4 py-4 border-b"
    >
      <div className="flex">
        <img className="mr-3" src={issueType.icon} alt="" />
        <span className="font-medium text-gray-700">{issueType.name}</span>
      </div>
      <ChevronRight className="text-gray-400" />
    </Button>
  );
}

function SelectIssueType({
  handleIssueTypeChange
}: {
  handleIssueTypeChange: (issueType: IIssueType) => void;
}) {
  return (
    <div className="flex flex-col">
      <span className="font-light text-sm text-gray-900  px-4 pt-8 pb-2 bg-gray-100 ">
        SELECT A TYPE OF ISSUE{" "}
      </span>
      {issueTypes.map((issueType) => (
        <IssueTypeCard
          onClick={handleIssueTypeChange}
          key={issueType.id}
          issueType={issueType}
        />
      ))}
    </div>
  );
}
