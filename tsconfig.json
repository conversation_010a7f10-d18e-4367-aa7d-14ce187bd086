{
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/.server/**/*.ts",
    "**/.server/**/*.tsx",
    "**/.client/**/*.ts",
    "**/.client/**/*.tsx"
  ],
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": ["@remix-run/node", "vite/client"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"],
      "@components/*": ["./app/components/*"],
      "@styles/*": ["./app/styles/*"],
      "@utils/*": ["./app/utils/*"],
      "@routes/*": ["./app/routes/*"],
      "@types/*": ["./app/types/*"],
      "@services/*": ["./app/services/*"],
      "@stores/*": ["./app/stores/*"]
    },
    "sourceMap": true, // Enable source maps
    // Vite takes care of building everything, not tsc.
    "noEmit": true
  }
}
