# Anonymous User Flow Documentation

## Overview

The Anonymous User Flow feature allows users to browse and interact with certain parts of the application without requiring full authentication. This feature is specifically designed for B2C networks with the "RET11" ONDC domain, providing a seamless user experience while maintaining security for protected actions.

## Key Features

- **Automatic Anonymous Login**: Users are automatically logged in with default credentials for specific network configurations
- **Protected Action Detection**: System identifies when anonymous users attempt to access protected features
- **Seamless Login Modal**: Bottom sheet login modal appears when authentication is required
- **Session Management**: Server-side session management with anonymous user state tracking
- **Path-based Access Control**: Different paths have different access levels for anonymous users

## Architecture Components

### 1. Session Management (`app/utils/session.server.ts`)

The session management system uses Remix's `createCookieSessionStorage` with the following configuration:

```typescript
export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "__session",
    secure: process.env.NODE_ENV !== "production" ? false : true,
    secrets: [sessionSecret],
    sameSite: process.env.NODE_ENV !== "production" ? "lax" : "none",
    path: "/",
    maxAge: 60 * 60 * 24 * 7, // 7 days
    httpOnly: true,
    domain: process.env.NODE_ENV === "production" ? ".mnetlive.com" : undefined
  }
});
```

**Key Functions:**
- `getSession(cookieHeader)`: Retrieves session data
- `commitSession(session)`: Saves session data to cookie
- `destroySession(session)`: Clears session data

### 2. Anonymous Authentication (`app/utils/auth.server.ts`)

#### Auto-Login Function

The `handleAppAutoLogin` function manages both WhatsApp auto-login and anonymous login:

```typescript
export async function handleAppAutoLogin(request: Request, networkConfig: NetworkConfig) {
  // Check for WhatsApp login parameters
  let token = url.searchParams.get("token");
  let mobileNumber = url.searchParams.get("mobileNumber");
  
  // Anonymous login for specific network configurations
  if (
    !isWaAutoLogin &&
    !session.get("access_token") &&
    networkConfig.networkType === "B2C" &&
    networkConfig.ondcDomain === "RET11"
  ) {
    mobileNumber = "0000000000";
    token = "112233";
    source = "anonymousLogin";
  }
}
```

**Anonymous Login Criteria:**
- No existing WhatsApp auto-login parameters
- No existing access token in session
- Network type is "B2C"
- ONDC domain is "RET11"

**Default Anonymous Credentials:**
- Mobile Number: "0000000000"
- Token: "112233"

### 3. User Type Definition (`app/types/index.ts`)

The User interface includes an `isAnonymous` flag:

```typescript
export interface User {
  userId: number;
  userName: string;
  businessName: string;
  buyerId: number;
  buyerFromBC?: boolean;
  mobileNumber?: string;
  isAnonymous?: boolean; // Flag to identify anonymous users
}
```

### 4. Anonymous User Detection Hook (`app/hooks/useAnonymousCheck.ts`)

This hook provides client-side anonymous user detection and authentication triggering:

```typescript
export function useAnonymousCheck() {
  const rootData = useRouteLoaderData<RootLoaderData>("root");
  const { openLogin } = useLoginStore();

  const isAnonymous = rootData?.isAnonymous || false;

  const requireRealAuth = (): boolean => {
    if (isAnonymous) {
      openLogin();
      return true;
    }
    return false;
  };

  return { isAnonymous, requireRealAuth };
}
```

**Usage:**
- `isAnonymous`: Boolean indicating if current user is anonymous
- `requireRealAuth()`: Function to check and trigger login if user is anonymous

### 5. Path-based Access Control (`app/utils/clientReponse.ts`)

The system defines specific paths that allow anonymous access:

```typescript
const anonymousAllowedPaths = ['/', '/home', '/home/<USER>', '/r/cart'];

// Allow anonymous users on specific paths
if (access_token && user?.isAnonymous && isAnonymousAllowedPath) {
  return {
    authRequired: false,
    forceLogin: false
  };
}

// Require authentication for anonymous users on protected paths
if (access_token && user?.isAnonymous && !isAnonymousAllowedPath) {
  return {
    authRequired: true,
    message: "Authentication required for this action",
    forceLogin: true
  };
}
```

**Anonymous Allowed Paths:**
- `/` - Home page
- `/home` - Main home page
- `/home/<USER>/Shop page
- `/r/cart` - Cart page

## Implementation Flow

### 1. Initial Page Load

1. **Root Loader** (`app/root.tsx`):
   - Fetches network configuration
   - Calls `handleAppAutoLogin` for auto-authentication
   - Sets `isAnonymous` flag in loader data
   - Passes anonymous state to client

2. **Anonymous Auto-Login Process**:
   - Checks if user meets anonymous login criteria
   - Uses default credentials (0000000000/112233)
   - Calls `verifyOtp` service with `anonymousLogin` source
   - Creates user session with `isAnonymous: true`
   - Redirects to clean URL

### 2. Client-Side Anonymous Detection

1. **useAnonymousCheck Hook**:
   - Reads `isAnonymous` from root loader data
   - Provides `requireRealAuth` function for components
   - Integrates with login store for modal management

2. **Component Integration**:
   ```typescript
   const { isAnonymous, requireRealAuth } = useAnonymousCheck();
   
   const handleProtectedAction = () => {
     if (requireRealAuth()) {
       return; // Login modal will be shown
     }
     // Proceed with protected action
   };
   ```

### 3. Login Modal System

#### Login Store (`app/stores/login.store.ts`)

Global state management for login modal:

```typescript
interface LoginState {
  isLoginOpen: boolean;
  redirectPath: string | null;
  openLogin: () => void;
  closeLogin: () => void;
  setRedirectPath: (path: string | null) => void;
}
```

#### Login Bottom Sheet (`app/components/LoginBottomSheet.tsx`)

Modal component for user authentication:

**Features:**
- Two-step authentication (phone number → OTP)
- Phone number validation (10 digits)
- OTP validation (6 digits)
- Automatic redirect after successful login
- Cart synchronization after login
- Error handling and display

**Login Flow:**
1. User enters phone number
2. System sends OTP via `requestOtp` API
3. User enters OTP
4. System verifies OTP via `verifyOtp` API
5. Session is updated with real user data
6. Cart synchronization occurs
7. User is redirected to original page

### 4. Protected Action Handling

#### Server-Side Protection

Routes use `requireAuth` function to check authentication:

```typescript
export const loader: LoaderFunction = async ({ request }) => {
  const auth = await requireAuth(request);
  if (auth && auth.authRequired) {
    return json(auth);
  }
  // Continue with protected logic
};
```

#### Client-Side Protection

Components use hooks to check authentication requirements:

```typescript
// Method 1: useRequireAuth hook
const { authRequired } = useRequireAuth();

// Method 2: useAnonymousCheck hook
const { isAnonymous, requireRealAuth } = useAnonymousCheck();
```

## Usage Examples

### 1. Protecting a Component Action

```typescript
import { useAnonymousCheck } from "~/hooks/useAnonymousCheck";

function PayNowButton() {
  const { requireRealAuth } = useAnonymousCheck();

  const handlePayment = () => {
    if (requireRealAuth()) {
      return; // Login modal will be shown
    }
    // Proceed with payment
    processPayment();
  };

  return <button onClick={handlePayment}>Pay Now</button>;
}
```

### 2. Route-Level Protection

```typescript
// In route loader
export const loader: LoaderFunction = async ({ request }) => {
  return createClientResponse(request, data, response);
};

// In component
export default function ProtectedPage() {
  const { authRequired } = useRequireAuth();
  
  if (authRequired) {
    return <LoadingScreen />; // Login modal will be shown
  }
  
  return <PageContent />;
}
```

### 3. Conditional UI Rendering

```typescript
function UserProfile() {
  const { isAnonymous } = useAnonymousCheck();
  
  return (
    <div>
      {isAnonymous ? (
        <GuestUserMessage />
      ) : (
        <UserProfileDetails />
      )}
    </div>
  );
}
```

## Configuration

### Network Configuration Requirements

For anonymous login to be enabled:

```typescript
{
  "networkType": "B2C",
  "ondcDomain": "RET11"
}
```

### Environment Variables

```env
SESSION_SECRET=your-session-secret
NODE_ENV=production|development
```

## Security Considerations

1. **Limited Access**: Anonymous users can only access specific paths
2. **Session Expiry**: Sessions expire after 7 days
3. **Secure Cookies**: Production uses secure, httpOnly cookies
4. **Token Validation**: All tokens are validated before use
5. **Protected Actions**: Critical actions require real authentication

## Benefits

1. **Improved UX**: Users can browse without immediate login requirement
2. **Reduced Friction**: Lower barrier to entry for new users
3. **Seamless Transition**: Smooth upgrade from anonymous to authenticated state
4. **Maintained Security**: Protected actions still require real authentication
5. **Network Specific**: Feature can be enabled per network configuration

## Troubleshooting

### Common Issues

1. **Anonymous login not working**: Check network configuration (B2C + RET11)
2. **Login modal not appearing**: Verify `useAnonymousCheck` hook usage
3. **Session not persisting**: Check cookie configuration and domain settings
4. **Redirect issues**: Ensure proper redirect path handling in login flow

### Debug Steps

1. Check browser cookies for `__session`
2. Verify network configuration in loader data
3. Check console for authentication errors
4. Validate token expiry and format
5. Confirm route protection implementation
