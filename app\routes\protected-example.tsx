import { json, LoaderFunction, ActionFunction } from "@remix-run/node";
import { useLoaderData, Form, useActionData } from "@remix-run/react";
import { requireAuth, withAuth } from "~/utils/auth-bridge.server";

interface LoaderData {
  message: string;
}

interface ActionData {
  success: boolean;
  message: string;
}

// Loader example with auth bridge
export const loader: LoaderFunction = async ({ request }) => {
  // Check if user is authenticated using the auth bridge
  const authResult = await requireAuth(request);

  // If authResult is not null, return it (will trigger login modal on client)
  if (authResult) {
    return authResult;
  }

  // User is authenticated, proceed with the route
  return json<LoaderData>({
    message: "This is protected content that requires authentication"
  });
};

// Action example with auth bridge
export const action: ActionFunction = async ({ request }) => {
  // Use withAuth helper for the action
  return withAuth(
    request,
    async (req) => {
      // This function only runs if user is authenticated
      const formData = await req.formData();
      const action = formData.get("action") as string;

      // Simulate some action processing
      if (action === "force-login") {
        // For demonstration, we'll use this to show a "force login" scenario
        // In a real app, this might be used for sensitive operations
        // that require re-authentication
        return withAuth(
          request,
          async () => {
            return json<ActionData>({
              success: true,
              message:
                "Completed sensitive action after forced re-authentication"
            });
          },
          true
        ); // true = force login
      }

      return json<ActionData>({
        success: true,
        message: `Action "${action}" processed successfully!`
      });
    },
    false // false = don't force login by default
  );
};

export default function ProtectedExample() {
  const loaderData = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4">Protected Route Example</h1>
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Loader Data:</h2>
        <p>{loaderData.message}</p>
      </div>

      {actionData && (
        <div className="mb-6 p-3 bg-green-100 border border-green-300 rounded">
          <h2 className="text-lg font-semibold mb-2">Action Result:</h2>
          <p>{actionData.message}</p>
        </div>
      )}

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Regular Action:</h2>
        <Form method="post" className="space-y-4">
          <input type="hidden" name="action" value="regular" />
          <button
            type="submit"
            className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600"
          >
            Submit Regular Action
          </button>
          <p className="text-sm text-gray-600">
            This action requires authentication but doesn't force re-login
          </p>
        </Form>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Force Login Action:</h2>
        <Form method="post" className="space-y-4">
          <input type="hidden" name="action" value="force-login" />
          <button
            type="submit"
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Submit Sensitive Action
          </button>
          <p className="text-sm text-gray-600">
            This action forces you to re-authenticate even if already logged in
          </p>
        </Form>
      </div>

      <p className="mt-4 text-sm text-gray-600">
        This page demonstrates the auth bridge - if you are not logged in, the
        login bottom sheet will appear instead of redirecting to a login page.
        The form will automatically resubmit after successful login.
      </p>
    </div>
  );
}
