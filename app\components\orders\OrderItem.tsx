import { FC } from "react";
import { OrderItem as OrderItemType } from "~/types";
import CustomImage from "../CustomImage";
import { formatCurrency } from "~/utils/format";
import SupplierBadge from "../common/SupplierBadge";

interface OrderItemProps {
  item: OrderItemType;
  displayPrices?: boolean;
}

const OrderItem: FC<OrderItemProps> = ({ item,displayPrices }) => {
  return (
    <div className="py-4 first:pt-0 last:pb-0 flex flex-col text-xs">
      <div className="flex justify-center items-center space-x-3">
        <div className="w-14 h-14 bg-gray-50 rounded-lg overflow-hidden border border-neutral-100">
          <CustomImage
            src={item.itemUrl}
            alt={item.itemName}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex justify-between">
            <div className="min-w-0">
              <h4 className="font-medium text-gray-900 truncate">
                {item.itemName}
              </h4>
              <p className=" text-gray-500 mt-0.5">1 {item.unit}</p>
            </div>
          </div>
          { displayPrices !== false && <div className="flex justify-between">
            <div className="flex items-center mt-1">
              <span className=" text-teal-600">{item.qty}</span>
              <span className=" text-gray-500 mx-1">×</span>
              <span className=" text-gray-500">
                {formatCurrency(item.price)}/{item.unit}
              </span>
            </div>
            <div className="flex gap-1 items-center">
              {item.strikeOffAmount && item.strikeOffAmount > item.amount && (
                <p className="text-xs text-gray-500 line-through">
                  {formatCurrency(item.strikeOffAmount)}
                </p>
              )}
              <p className="font-medium text-gray-900 flex-shrink-0">
                {formatCurrency(item.amount)}
              </p>
            </div>
          </div>}
          {/* {item.supplier !== "" ? (
            <div className="mt-1">
              <SupplierBadge supplier={item.supplier ?? ""} />
            </div>
          ) : null} */}
        </div>
      </div>

      {(item.cancelledQty > 0 || item.returnedQty > 0) && (
        <div className="flex flex-row justify-between items-center text-xs mt-2 py-2 rounded-md   bg-gradient-to-r from-red-50 to-orange-50">
          {item.cancelledQty > 0 && (
            <div className="flex items-center px-2">
              <span className=" text-red-600">
                Cancelled qty : {item.cancelledQty} {item.unit}
              </span>
            </div>
          )}
          {item.returnedQty > 0 && (
            <div className="flex items-center px-2">
              <span className="text-orange-600">
                Returned qty : {item.returnedQty} {item.unit}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OrderItem;
