// app/components/PaymentOption.tsx

import React from 'react';

interface PaymentOptionProps {
    onPress: () => void;
    disabled: boolean;
    selected: boolean;
    label: string;
    discount?: number;
}

const PaymentOption: React.FC<PaymentOptionProps> = ({ onPress, disabled, selected, label, discount }) => {
    return (
        <button
            className={`flex items-center p-2 rounded ${disabled && !selected ? 'cursor-not-allowed' : 'cursor-pointer'}`}
            onClick={onPress}
            disabled={disabled}
        >
            {selected ? (
                <img src="/lang_check.png" alt="Selected" className="w-4 h-4 mr-2" />
            ) : (
                <div
                    className={`w-3 h-3 rounded-full border ${disabled ? 'border-gray-300' : 'border-gray-500'} mr-2`}
                />
            )}
            <span className={`text-sm ${disabled && !selected ? 'text-gray-400' : 'text-gray-800'}`}>
                {label}
                {discount && <span className="text-purple-500">{` (${discount}% Discount)`}</span>}
            </span>
        </button>
    );
};

export default PaymentOption;
