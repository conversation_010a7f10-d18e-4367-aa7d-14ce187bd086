import React from "react";
import { Home, MapPin, Trash2, Pencil, BriefcaseBusiness } from "lucide-react";
import { cn } from "~/utils/cn";
import { AddressType } from "~/types/address.types";

export interface AddressCardProps {
  id: number;
  type?: AddressType;
  address: string;
  isSelected?: boolean;
  onEdit?: (id: number) => void;
  onDelete?: (id: number) => void;
  className?: string;
  buyerInServiceArea?: boolean;
  showDeliveryStatus?: boolean;
  showTrashIcon?: boolean;
}

const getAddressIcon = (type?: string) => {
  switch (type?.toLowerCase()) {
    case "home":
      return <Home className="w-5 h-5" />;
    case "work":
      return <BriefcaseBusiness className="w-5 h-5" />;
    default:
      return <MapPin className="w-5 h-5" />;
  }
};

const AddressCard: React.FC<AddressCardProps> = ({
  id,
  type = "other",
  address = "",
  isSelected = false,
  onEdit,
  onDelete,
  className,
  buyerInServiceArea = true,
  showDeliveryStatus = false,
  showTrashIcon
}) => {
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(id);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(id);
    }
  };

  return (
    <div
      className={cn(
        "py-2 px-3 mx-2 rounded-lg border transition-colors duration-200 shadow-md bg-white",
        isSelected ? ["bg-primary-50", "border-primary-100"] : "",
        showDeliveryStatus && buyerInServiceArea
          ? "hover:border-primary-200"
          : "",
        // buyerInServiceArea ? "" : "bg-gray-100",
        className
      )}
      role="button"
      aria-pressed={isSelected}
      tabIndex={0}
    >
      <div className="flex flex-col items-start gap-2">
        {showDeliveryStatus &&
          (buyerInServiceArea ? (
            <p className="text-sm text-blue-600 mt-1">DELIVERS TO</p>
          ) : (
            <p className="text-sm text-red-600 mt-1">DOES NOT DELIVER TO</p>
          ))}
        <div
          className={cn(
            "flex flex-row gap-2",
            buyerInServiceArea ? "text-typography-800" : "text-typography-200"
          )}
        >
          <div className="mt-0.5 flex-shrink-0">{getAddressIcon(type)}</div>
          <div className="flex flex-col gap-1 items-start">
            <h3 className={cn("font-medium capitalize")}>
              {type || "Address"}
            </h3>
            <p
              className={cn(
                "text-xs text-start text-typography-600 font-medium break-words",
                buyerInServiceArea
                  ? "text-typography-600"
                  : "text-typography-200"
              )}
            >
              {address}
            </p>
            <div className="pt-2 pb-1 flex flex-row gap-3.5">
              {onEdit && (
                <div
                  onClick={handleEdit}
                  className="text-primary hover:text-primary-600 p-1 rounded-full border border-primary"
                  aria-label="Edit address"
                >
                  <Pencil className="w-3.5 h-3.5" />
                </div>
              )}
              {onDelete && showTrashIcon && (
                <div
                  onClick={handleDelete}
                  className="text-red-500 hover:text-red-600 p-1 rounded-full border border-red-500"
                  aria-label="Delete address"
                >
                  <Trash2 className="w-3.5 h-3.5" />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressCard;
