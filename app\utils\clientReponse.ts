import { json, redirect } from "@remix-run/node";
import { ApiResponse } from "~/types/Api";
import { commitSession, destroySession, getSession } from "./session.server";
import { getAppSource } from "./loader";
import { parseJWT } from "./token-utils";
import { DecodedToken } from "~/types/user";
import { User } from "~/types";

// TODO: handle session with new headers also
export async function createClientResponse<TClientData, T>(
  request: Request,
  data: TClientData,
  apiResponse: ApiResponse<T>,
  redirectUrl?: string
) {
  let session = await getSession(request?.headers.get("Cookie"));

  if (apiResponse.statusCode === 401) {
    const appSource = getAppSource(request);
    const url = new URL(request.url);
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appSource, appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/login?redirectTo=${url.pathname}`, { headers });

    // return redirect("/login", {
    //   headers: {
    //     "Set-Cookie": await destroySession(session)
    //   }
    // });
  }

  const { headers } = apiResponse;
  console.log(
    "Setting new headers: ",
    headers?.has("Set-cookie") ? headers.get("Set-cookie") : null
  );

  if (redirectUrl?.length) {
    return redirect(
      redirectUrl,
      headers?.has("Set-Cookie")
        ? {
            headers: {
              "Set-Cookie": headers.get("Set-Cookie")!
            }
          }
        : undefined
    );
  }

  return json<TClientData>(
    data,
    headers?.has("Set-Cookie")
      ? {
          headers: {
            "Set-Cookie": headers.get("Set-Cookie")!
          }
        }
      : undefined
  );
}

export async function requireAuth(
  request: Request,
  redirectTo?: string,
  forceLogin?: boolean
) {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;
  const url = new URL(request.url);
  const appSource = getAppSource(request);
  let decoded = undefined;

  // Define paths that don't require authentication
  const publicPaths = ['/landing_page', '/free-item'];

  // Check if current path is in the public paths list
  if (publicPaths.includes(url.pathname)) {
    return {
      authRequired: false,
      forceLogin: false
    };
  }

  // Define paths that allow anonymous users
  const anonymousAllowedPaths = ['/', '/home', '/home/<USER>', '/r/cart'];

  // Check if current path allows anonymous users and user is anonymous
  const isAnonymousAllowedPath = anonymousAllowedPaths.includes(url.pathname);

  if (access_token && user?.isAnonymous && isAnonymousAllowedPath) {
    return {
      authRequired: false,
      forceLogin: false
    };
  }

  if (access_token && user?.isAnonymous && !isAnonymousAllowedPath) {
    return {
      authRequired: true,
      message: "Authentication required for this action",
      forceLogin: true
    };
  }

  if (forceLogin) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appSource, appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return {
      authRequired: true,
      message: "Authentication required",
      forceLogin: forceLogin || false
    };
  }

  // If user is already authenticated and not forcing login, continue
  if (access_token && !forceLogin) {
    return {
      authRequired: false,
      forceLogin: forceLogin || false
    };
  }

  try {
    if (!access_token || !user) {
      const headers = new Headers();
      headers.append("Set-Cookie", await destroySession(session));
      session = await getSession();
      session.set("appConfig", { appSource, appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return {
        authRequired: true,
        message: "Authentication required",
        forceLogin: forceLogin || false
      };
    }

    decoded = parseJWT(access_token) as DecodedToken;
    if (!decoded || !decoded.userDetails) {
      const headers = new Headers();
      session = await getSession();
      session.set("appConfig", { appSource, appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return {
        authRequired: true,
        message: "Authentication required",
        forceLogin: forceLogin || false
      };
    }
  } catch (error) {
    console.error("Error while checking authentication:", error);
    throw {
      authRequired: true,
      message: "Authentication required",
      forceLogin: forceLogin || false
    };
  }

  // If redirectTo is provided, store it in the session
  if (redirectTo) {
    session.set("redirectPath", redirectTo);
  } else {
    // Use the current URL as the redirect path
    const url = new URL(request.url);
    session.set("redirectPath", url.pathname + url.search);
  }

  // Set a special flag in the session to trigger the login modal
  session.set("requireAuth", true);

  // Return JSON with authentication required flag and set the cookie
  return {
    authRequired: true,
    message: "Authentication required",
    forceLogin: forceLogin || false
  };
}
