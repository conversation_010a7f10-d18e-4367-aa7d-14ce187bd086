import React, { useEffect, useState, useRef } from "react";
import { Search } from "lucide-react";
import { cn } from "~/utils/cn";

export interface DropdownOption {
  value: string;
  label: string;
}

interface SearchableDropdownProps {
  value: string;
  options: DropdownOption[];
  onClick?: () => void;
  onSelect: (value: string) => void;
  onClear: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onInputChange?: (value: string) => void;
  className?: string;
  placeholder?: string;
}

export const SearchableDropdown: React.FC<SearchableDropdownProps> = ({
  value,
  options,
  className,
  onSelect,
  onClick,
  onClear,
  onFocus,
  onBlur,
  onInputChange,
  placeholder = "Search..."
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onInputChange?.(newValue);
    setIsOpen(true);
    setHighlightedIndex(-1);
  };

  const handleOptionClick = (option: DropdownOption) => {
    setInputValue(option.label);
    onSelect(option.value);
    setIsOpen(false);
  };

  const handleClear = () => {
    setInputValue("");
    onClear();
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && inputValue?.length < 2) {
      onClear();
      return;
    }

    if (!isOpen && e.key === "ArrowDown") {
      setIsOpen(true);
      return;
    }

    if (isOpen) {
      switch (e.key) {
        case "ArrowDown":
          setHighlightedIndex((prev) =>
            prev < options.length - 1 ? prev + 1 : prev
          );
          e.preventDefault();
          break;
        case "ArrowUp":
          setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : prev));
          e.preventDefault();
          break;
        case "Enter":
          if (highlightedIndex >= 0 && highlightedIndex < options.length) {
            handleOptionClick(options[highlightedIndex]);
          }
          e.preventDefault();
          break;
        case "Escape":
          setIsOpen(false);
          break;
      }
    }
  };

  return (
    <div
      ref={dropdownRef}
      className={cn("w-full relative", className)}
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick?.();
        }
      }}
      role="combobox"
      aria-expanded={isOpen}
      aria-haspopup="listbox"
      tabIndex={0}
    >
      <div className="relative">
        <Search
          size={20}
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none"
        />
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => {
            onFocus?.();
            setIsOpen(true);
          }}
          onBlur={onBlur}
          onKeyDown={handleKeyDown}
          className="w-full px-10 py-2 rounded-full bg-white border border-white hover:border-gray-300 focus:border-gray-300 focus:outline-none transition-colors"
          placeholder={placeholder}
          aria-autocomplete="list"
        />
        {inputValue && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleClear();
            }}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            aria-label="Clear search"
          >
            ×
          </button>
        )}
      </div>

      {isOpen && options.length > 0 && (
        <div
          className="absolute z-50 w-full mt-1 bg-white rounded-lg shadow-lg border border-gray-100 max-h-60 overflow-auto"
          role="listbox"
        >
          <div className="p-1">
            {options.map((option, index) => (
              <div
                key={option.value}
                onClick={() => handleOptionClick(option)}
                onMouseEnter={() => setHighlightedIndex(index)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    handleOptionClick(option);
                  }
                }}
                role="option"
                aria-selected={highlightedIndex === index}
                tabIndex={0}
                className={cn(
                  "px-3 py-2 rounded-md cursor-pointer",
                  "hover:bg-gray-100",
                  highlightedIndex === index && "bg-gray-100",
                  "transition-colors duration-100"
                )}
              >
                {option.label}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
