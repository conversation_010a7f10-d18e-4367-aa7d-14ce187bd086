import React from "react";
import Button from "../Button";
import { cn } from "~/utils/cn";
import { Minus, Plus } from "lucide-react";

const CustomizeButton: React.FC<{
  qty: number;
  variantCount: number;
  isDisabled: boolean;
  onClick: () => void;
  className?: string;
  btnConfig?: {
    showUnit?: boolean;
    btnType?: "primary" | "secondary";
  };
}> = ({ qty, onClick, isDisabled, className, btnConfig: userBtnConfig }) => {
  const btnConfig = {
    showUnit: true,
    btnType: "primary",
    ...userBtnConfig
  };

  const isSecondary = btnConfig.btnType === "secondary";
  const buttonBgColor = isDisabled
    ? "bg-white"
    : isSecondary
    ? "bg-primary-50"
    : qty > 0
    ? "bg-primary"
    : "bg-primary-50";

  const buttonBorderColor = isDisabled ? "border-gray-400" : "border-primary";

  const textColor = isDisabled
    ? "text-gray-400"
    : isSecondary
    ? "text-primary"
    : qty > 0
    ? "text-white"
    : "text-primary";

  const iconColor = isDisabled
    ? "text-gray-400"
    : isSecondary
    ? "text-primary"
    : "text-white";

  return (
    <Button
      className={cn(
        "border rounded-lg flex flex-row items-center justify-center gap-2 px-3 py-2 text-[.7rem]",
        buttonBgColor,
        buttonBorderColor,
        className
      )}
      onClick={onClick}
    >
      {qty > 0 && (
        <button
          onClick={onClick}
          className={`text-center font-bold ${iconColor}`}
          disabled={isDisabled}
        >
          <Minus size={16} className={iconColor} />
        </button>
      )}{" "}
      <span className={cn("text-center font-semibold text-xs", textColor)}>
        {qty > 0 ? qty : "ADD"}
      </span>
      {qty > 0 && (
        <button
          onClick={onClick}
          className={`text-center font-bold ${iconColor}`}
          disabled={isDisabled}
        >
          <Plus size={16} className={iconColor} />
        </button>
      )}
    </Button>
  );
};

export default CustomizeButton;
