import { create } from "zustand";
import {
  AvailableItem,
  ImageViewType,
  ItemCategoryDtos,
  ItemOptionsData
} from "~/types";
import { CategoryType, SearchPage } from "~/types/chooseitems.types";

interface ChooseitemsStore {
  loading: boolean;
  categoryType: CategoryType;
  categoryTypeList: CategoryType[];
  selectedCategory?: number;
  selectedParentCategory?: ItemCategoryDtos;
  searchPage: SearchPage;
  itemOptionsData: ItemOptionsData | null;
  isScrolled: boolean;
  imageViewType: ImageViewType;
  itemMap: Map<number, AvailableItem>;
  setItemOptionsData: (itemOptionsData: ItemOptionsData) => void;
  setCategoryType: (categoryType: CategoryType) => void;
  setCategoryTypeList: (categoryType: CategoryType) => void;
  setSelectedCategory: (selectedCategory: number) => void;
  setSelectedParentCategory: (selectedCategory?: ItemCategoryDtos) => void;
  setSearchPage: (searchPage: SearchPage) => void;
  setLoading: (loading: boolean) => void;
  setIsScrolled: (isScrolled: boolean) => void;
  setImageViewType: (imageViewType: ImageViewType) => void;
  resetChooseItemsStore: () => void;
}

export const chooseitemsStore = create<ChooseitemsStore>((set) => ({
  loading: false,
  categoryType: null,
  selectedCategory: undefined,
  categoryTypeList: [],
  searchPage: null,
  itemOptionsData: null,
  isScrolled: false,
  imageViewType: null,
  itemMap: new Map(),
  setItemOptionsData: (itemOptionsData) =>
    set(() => {
      const itemMap = new Map<number, AvailableItem>();
      itemOptionsData?.availableItems?.forEach((item) => {
        itemMap.set(item.sellerItemId, item);
      });
      return {
        itemOptionsData: { ...itemOptionsData },
        itemMap
      };
    }),
  setCategoryType: (categoryType) => set(() => ({ categoryType })),
  setCategoryTypeList: (categoryType) =>
    set((state) => ({
      categoryTypeList: [...state.categoryTypeList, categoryType]
    })),
  setSelectedCategory: (selectedCategory) => set(() => ({ selectedCategory })),
  setSelectedParentCategory: (newCat) =>
    set(() => ({ selectedParentCategory: newCat })),
  setSearchPage: (searchPage) => set(() => ({ searchPage })),
  setLoading: (loading) => set(() => ({ loading })),
  setIsScrolled: (isScrolled) => set(() => ({ isScrolled })),
  setImageViewType: (imageViewType) => set(() => ({ imageViewType })),
  resetChooseItemsStore: () =>
    set(() => ({
      loading: false,
      categoryType: null,
      selectedCategory: undefined,
      categoryTypeList: [],
      searchPage: null,
      itemOptionsData: null,
      isScrolled: false,
      itemMap: new Map()
    }))
}));
