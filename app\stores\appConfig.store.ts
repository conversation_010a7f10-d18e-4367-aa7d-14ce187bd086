import { create } from "zustand";
import { AppDomain, ImageViewType, NetworkConfig } from "~/types";
import { AppConfig, AppSource } from "~/types/app";
import { useCartStore } from "./cart.store";
import { useLocation } from "@remix-run/react";
import { useEffect } from "react";

interface ChooseItemConfig {
  imageViewType: ImageViewType;
}

interface AppConfigStore {
  appSource: AppSource;
  appConfig: AppConfig;
  networkConfig: NetworkConfig | null;
  chooseItemConfig?: ChooseItemConfig;
  appDomain: AppDomain;
  showWhatsappCTA: boolean;
  setAppConfig: (appConfig: Partial<AppConfig>) => void;
  setAppSource: (appSource: AppSource) => void;
  setNetworkConfig: (networkConfig: NetworkConfig | null) => void;
  setChooseItemConfig: (chooseItem: ChooseItemConfig) => void;
  setAppDomain: (appDomain: AppDomain) => void;
  updateShowWhatsappCTA: (pathname: string) => void;
}

export const useAppConfigStore = create<AppConfigStore>((set) => ({
  appSource: "buyer-web",
  appConfig: {
    appStartRoute: "/home",
    appExitRoutes: ["/logout"]
  },
  networkConfig: null,
  chooseItemConfig: {
    imageViewType: "GALLERY"
  },
  appDomain: "RET10",
  showWhatsappCTA: false,
  setAppConfig: (newConfig) =>
    set((state) => ({
      appConfig: {
        ...state.appConfig,
        ...newConfig
      }
    })),
  setAppSource: (appSource) =>
    set((state) => {
      // Update showWhatsappCTA when appSource changes
      const showCTA = appSource === "whatsappchat";
      return {
        ...state,
        appSource,
        showWhatsappCTA: showCTA
      };
    }),
  setNetworkConfig: (config) =>
    set(() => {
      // Always determine appSource from network config
      // Set to "whatsappchat" if wabMobileNumber exists, otherwise "buyer-web"
      const shouldUseWhatsApp =
        !!config?.wabMobileNumber || !!config?.wabEnabled;
      const appSource = shouldUseWhatsApp ? "whatsappchat" : "buyer-web";

      return {
        networkConfig: config,
        appDomain: config?.ondcDomain || "RET10",
        // Always set appSource based on wabMobileNumber presence
        appSource,
        // Update showWhatsappCTA based on appSource
        showWhatsappCTA: shouldShowWhatsappCTA(appSource, location.pathname)
      };
    }),
  setChooseItemConfig: (newConfig) =>
    set((state) => ({
      chooseItemConfig: {
        ...state.chooseItemConfig,
        ...newConfig
      }
    })),
  setAppDomain: (appDomain) => set(() => ({ appDomain })),
  updateShowWhatsappCTA: (pathname) =>
    set((state) => {
      const showCTA = shouldShowWhatsappCTA(state.appSource, pathname);
      return { showWhatsappCTA: showCTA };
    })
}));

// Custom hook to manage WhatsApp CTA visibility based on route and cart
export const useWhatsAppCTA = () => {
  const location = useLocation();
  const updateShowWhatsappCTA = useAppConfigStore(
    (state) => state.updateShowWhatsappCTA
  );
  const showWhatsappCTA = useAppConfigStore((state) => state.showWhatsappCTA);
  const itemCount = useCartStore((state) => state.itemCount);

  // Update visibility whenever route or cart changes
  useEffect(() => {
    updateShowWhatsappCTA(location.pathname);
  }, [location.pathname, itemCount, updateShowWhatsappCTA]);

  return showWhatsappCTA;
};

const shouldShowWhatsappCTA = (appSource: AppSource, pathname: string) => {
  // Check if we should show WhatsApp CTA based on app source and pathname
  const isWhatsAppSource = appSource === "whatsappchat";

  // Get cart item count from cartStore to check condition
  const cartItemCount = useCartStore.getState().itemCount;

  // Check if current route matches any of the routes where CTA should be hidden
  const isRestrictedRoute =
    pathname === "/chooseitems" ||
    pathname === "/home/<USER>" ||
    pathname === "/cart" ||
    pathname === "/r/cart";

  // Only show CTA if it's from WhatsApp source AND
  // not on a restricted route with items in cart
  const showCTA = isWhatsAppSource && !(isRestrictedRoute && cartItemCount > 0);

  return showCTA;
};
