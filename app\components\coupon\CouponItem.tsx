import { CouponDTO } from "~/types/coupon.types";
import { useCouponStore } from "~/stores/coupon.store";
import { AlertCircleIcon } from "lucide-react";

interface CouponItemProps {
  coupon: CouponDTO;
}

export const CouponItem = ({ coupon }: CouponItemProps) => {
  const { selectCoupon, openCouponDetails } = useCouponStore();

  const handleSelect = () => {
    selectCoupon(coupon.id);
  };

  const handleShowDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    openCouponDetails(coupon.id);
  };

  return (
    <div
      className="bg-white rounded-lg p-3 mb-3 shadow-sm"
      onClick={handleSelect}
    >
      <div className="flex justify-between items-start gap-4">
        <div className="flex">
          <div className="w-6 h-6 mr-3 flex-shrink-0">
            <img
              src="/coupon-icon.svg"
              alt="coupon"
              className="w-full h-full rounded-full min-w-6 min-h-6"
            />
            {/* <div className="w-full h-full bg-teal-500 rounded-full flex items-center justify-center">
              <span className="text-white text-lg">₹</span>
            </div> */}
          </div>
          <div>
            <div className="flex flex-row items-start gap-1.5">
              <h3 className="font-semibold text-gray-900">
                {coupon.description
                  ? coupon.description
                  : `Flat ₹${coupon.discountValue?.toFixed(0)} OFF`}
              </h3>
              <span className="mt-1" onClick={handleShowDetails} role="button">
                <AlertCircleIcon className="w-4 h-4 text-gray-400 cursor-pointer" />
              </span>
            </div>
            <p className="font-medium text-blue-600 text-sm">
              Save ₹{coupon.discountValue?.toFixed(0)} with this code
            </p>
            <div className="mt-1 p-1 w-fit border border-neutral-600 rounded-md bg-[#FCFCFD]">
              <h3 className="text-xs font-medium text-typography-400">
                {coupon.code}
              </h3>
            </div>
          </div>
        </div>
        <button
          className="w-[18px] h-[18px] min-w-[18px] min-h-[18px] rounded-full border-2 border-teal-500 flex items-center justify-center cursor-pointer"
          aria-label={`Select coupon ${coupon.code}`}
          role="radio"
          aria-checked={coupon.isSelected}
        >
          {coupon.isSelected && (
            <div className="w-[10px] h-[10px] bg-teal-500 rounded-full" />
          )}
        </button>
      </div>
    </div>
  );
};
