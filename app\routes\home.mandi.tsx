// app / routes / home.mandi.tsx;

import React, { useEffect } from "react";
import { LoaderFunction, json, redirect } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import dayjs from "dayjs";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules";

import SellerCard from "~/components/SellerCard";
import PendingCard from "~/components/PendingCard";
import Button from "~/components/Button";
import LocationConfirmModal from "~/components/location/LocationConfirmModal";

import { BuyerHomeData, DeliveryOption, NetworkConfig, User } from "~/types";
import { getDeliveryOptionsAPI } from "~/services/home.service";
import {
  getSession,
  destroySession,
  commitSession
} from "~/utils/session.server";
import {
  getItem,
  setItem,
  getStoredData,
  removeItem,
  removeAllCarts
} from "@utils/localStorage";
// import { createClientResponse } from "~/utils/clientReponse";
import { NetworkAsset } from "~/components/NetworkAssests";
import ErrorBoundary from "~/components/ErrorBoundary";
import { getNetworkConfig } from "~/services/auth.server";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { getAppSource } from "~/utils/loader";
import { AppSource } from "~/types/app";
import { useUpdateAppConfig } from "~/hooks/useUpdateAppConfig";
import { useUser } from "~/contexts/userContext";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import { useCartStore } from "~/stores/cart.store";
import { isEmptyNullOrUndefinedString } from "~/utils/string";
import { useHomeStore } from "~/stores/home.store";
import { useRequireAuth } from "~/hooks/useRequireAuth";
import InstallPWAButton from "~/components/InstallPWAButton";
// Define the loader function to fetch initial data
export const loader: LoaderFunction = async ({ request }) => {
  console.log("step001");
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token");
  const user: User | null = session.get("user");

  const url = new URL(request.url);

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({
      ...auth,
      buyerHomeData: {},
      user: null,
      networkConfig: {},
      appSource: getAppSource(request)
    });
  }

  if (!access_token || !user) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", {
      appSource: getAppSource(request),
      appStartRoute: url.pathname
    });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/login?redirectTo=${url.pathname}`, { headers });
  }

  try {
    const buyerHomeData = await getDeliveryOptionsAPI(request);

    const networkConfig = await getNetworkConfig(request);

    return createClientResponse(
      request,
      {
        buyerHomeData: buyerHomeData.data,
        user,
        networkConfig: networkConfig.data,
        appSource: getAppSource(request)
      },
      buyerHomeData
    );
  } catch (error) {
    console.error("Error fetching delivery options:", error);
    return json({ error: "Failed to fetch data" }, { status: 500 });
  }
};

const HomeMandi = () => {
  const navigate = useNavigate();
  const { buyerHomeData, networkConfig, appSource } = useLoaderData<{
    buyerHomeData: BuyerHomeData;
    user: User;
    error?: string;
    networkConfig: NetworkConfig;
    appSource: AppSource;
  }>();

  const [storeName, setStoreName] = React.useState("");
  const [retailerId, setRetailerId] = React.useState("");
  const { setUser } = useUser();
  const { resetChooseItemsStore } = chooseitemsStore((state) => state);
  const { clearCart } = useCartStore((state) => state);
  const { setHideBottomBar } = useHomeStore();
  // const [bannerImages, setBannerImages] = React.useState<string[]>([""]);

  // Using the custom hook instead of inline logic
  const { authRequired } = useRequireAuth();

  useUpdateAppConfig({ appSource });

  // reload networkConfig
  useEffect(() => {
    setItem("networkConfig", networkConfig);
  }, []);

  // clear cart
  useEffect(() => {
    removeAllCarts();
    removeItem("order");
    setUser({ existingOrderGroupId: undefined });
    resetChooseItemsStore();
    clearCart("all");
  }, []);

  React.useEffect(() => {
    const fetchStoreData = async () => {
      try {
        const res = await getItem("retailerDetails");
        if (res) {
          setStoreName(res.name || "");
          setRetailerId(res.buyerId?.toString() || "");

          if (!res.name) {
            const userDetails = await getStoredData("userDetails");
            if (userDetails && userDetails.fullName) {
              setStoreName(userDetails.fullName);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching store data:", error);
      }
    };

    fetchStoreData();
  }, []);

  const handleRefresh = () => {
    // Trigger a reload to re-run the loader
    navigate(0);
  };

  const handleAdd = (deliveryDate: string) => {
    const currentSelectedDeliveryOption = buyerHomeData?.deliveryOptions?.find(
      (deliveryOption) => deliveryOption.deliveryDate === deliveryDate
    );
    if (networkConfig?.multiSeller && (currentSelectedDeliveryOption?.avSellerCount && currentSelectedDeliveryOption?.avSellerCount > 1)) {
      navigate(
        `/selectseller?deliveryDate=${encodeURIComponent(deliveryDate)}`
      );
    } else {
      navigate(
        `/chooseitems?deliveryDate=${encodeURIComponent(
          deliveryDate
        )}&sellerId=${currentSelectedDeliveryOption?.appSellerSellerId}`
      );
    }

    // navigate(`/selectseller?deliveryDate=${encodeURIComponent(deliveryDate)}`);
  };

  const handlePress = (tabName: string) => {
    navigate("/home/<USER>");
  };

  // if (error) {
  //   return <ErrorModel message="Something went wrong" />;
  // }

  const defaultBannerImages = ["/home_banner.jpg"];

  // Add this check for address to determine if we should show the confirm address button
  const showAddressBtn =
    (buyerHomeData?.defaultAddress &&
      isEmptyNullOrUndefinedString(
        buyerHomeData.defaultAddress.address || ""
      )) ||
    (parseInt(buyerHomeData?.defaultAddress?.latitude || "0") === 0 &&
      parseInt(buyerHomeData?.defaultAddress?.longitude || "0") === 0) ||
    !buyerHomeData?.defaultAddress?.buyerInServiceArea;

  // Update the bottom bar visibility based on showAddressBtn
  useEffect(() => {
    setHideBottomBar(showAddressBtn);

    // Reset when component unmounts
    return () => {
      setHideBottomBar(false);
    };
  }, [showAddressBtn, setHideBottomBar]);

  // Handler for location update button
  const handleLocationUpdate = () => {
    if (
      buyerHomeData?.defaultAddress &&
      parseInt(buyerHomeData?.defaultAddress?.latitude || "0") === 0 &&
      parseInt(buyerHomeData?.defaultAddress?.longitude || "0") === 0
    ) {
      navigate("/changeaddress?redirectTo=/home/<USER>", {
        state: {
          address: buyerHomeData?.defaultAddress,
          isEdit: true,
          from: "/home/<USER>",
          returnTo: "/home/<USER>"
        }
      });
    } else {
      navigate("/select-address?flowType=select-address&returnTo=/home/<USER>", {
        state: {
          from: "/home/<USER>",
          returnTo: "/home/<USER>",
          flowType: "select-address"
        }
      });
    }
  };

  if (authRequired) {
    return (
      <div className="flex items-center justify-center h-screen">
        <NetworkAsset assetName={"loginBanner"} />
      </div>
    );
  }

  return (
    <div className="fixed inset-0 flex flex-col bg-neutral-50">
      <InstallPWAButton 
         themeColor="Primary"
         title="Get The App"
         subtitle="For Better Experience"
         />
      <div className="sticky top-0 z-10">
      {/* Buyer Info */}
      <div className="flex flex-col justify-center px-3 py-4 bg-white shadow-md">
        <span className="text-sm font-medium text-typography-800">
          {buyerHomeData.masterBuyerName}
        </span>
      </div>
      </div>
      <div className="mx-2">
        <div className="mt-2 rounded-md overflow-hidden">
          <Swiper
            pagination={{ clickable: true }}
            className="mySwiper"
            modules={[Autoplay, Pagination]}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            loop={true}
            spaceBetween={10}
            slidesPerView={1}
          >
            {(buyerHomeData.networkBannerUrls || defaultBannerImages).map(
              (image, index) => (
                <SwiperSlide key={index}>
                  <img
                    src={image}
                    alt={`Slide ${index + 1}`}
                    className="rounded-xl w-full max-h-44 object-fill"
                  />
                </SwiperSlide>
              )
            )}
          </Swiper>
        </div>
        {/* Delivery info */}
        <div className="flex justify-between items-center my-4">
          <p className="text-xs text-gray-700">
            TODAY :{" "}
            <span className="font-semibold text-blue-500">
              {dayjs().format("dddd, DD/MM/YYYY").toUpperCase()}
            </span>
          </p>

          <Button
            onClick={handleRefresh}
            className="flex items-center bg-teal-500 hover:bg-teal-600 text-white text-xs font-semibold py-0.5 px-2 rounded"
          >
            <div className="flex items-center justify-center bg-green-teal h-5 rounded px-1">
              <span className="text-xs text-white">REFRESH</span>
            </div>
          </Button>
          {/* <RefreshButton onClick={handleRefresh} /> */}
        </div>
      </div>

      {!showAddressBtn && (
        <div className="flex-grow w-full px-4 overflow-y-scroll h-full flex flex-col">
          {/* Delivery Options */}
          {buyerHomeData && buyerHomeData?.deliveryOptions ? (
            <div>
              {buyerHomeData.deliveryOptions.map(
                (eachOption: DeliveryOption) => (
                  <SellerCard
                    key={eachOption.deliveryDate}
                    deliveryOption={eachOption}
                    isToday={dayjs(eachOption.deliveryDate).isSame(
                      dayjs(),
                      "day"
                    )}
                    onAdd={() => handleAdd(eachOption.deliveryDate)}
                    onPress={handlePress}
                  />
                )
              )}

              {/* Pending Payments */}
              {buyerHomeData.pendingPaymentsCount > 0 && (
                <PendingCard
                  buyerHomeData={buyerHomeData}
                  onPress={handlePress}
                />
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 bg-gray-100 rounded-lg mx-2 my-2 ">
              <img
                src="/no_sellers.png"
                alt="No Sellers"
                className="w-44 h-44"
              />
              <p className="text-xs font-light text-gray-500 mt-8 text-center">
                Booking is Closed Now Please Refresh after sometime
              </p>
            </div>
          )}
          {/* Footer Image */}
          <div className="w-20 h-15 mb-20 mx-auto self-end">
            <NetworkAsset assetName="footer" />
          </div>
        </div>
      )}

      {/* Use the LocationConfirmModal component */}
      {showAddressBtn && (
        <LocationConfirmModal
          address={buyerHomeData?.defaultAddress?.address}
          message={
            parseInt(buyerHomeData?.defaultAddress?.latitude || "0") === 0 &&
            parseInt(buyerHomeData?.defaultAddress?.longitude || "0") === 0
              ? "We need your accurate location to"
              : "We do not deliver in your area since it's far away."
          }
          onUpdateLocation={handleLocationUpdate}
        />
      )}
    </div>
  );
};

export default HomeMandi;

export { ErrorBoundary };
