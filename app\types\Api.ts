export interface ApiResponse<T> {
  statusCode: number;
  data: T;
  headers?: Headers;
}

export enum ErrorCode {
  VALIDATION_ERROR = "VALIDATION_ERROR",
  NOT_FOUND = "NOT_FOUND",
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
  BAD_REQUEST = "BAD_REQUEST"
}

export interface ErrorDto {
  code: ErrorCode;
  message: string;
}

export interface MnetCoreResponse<T> {
  success: boolean;
  data?: T;
  error?: ErrorDto;
}

export const createSuccessResponse = <T>(data: T): MnetCoreResponse<T> => ({
  success: true,
  data
});

export const createErrorResponse = <T>(
  message: string,
  code: ErrorCode
): MnetCoreResponse<T> => ({
  success: false,
  error: {
    code,
    message
  }
});

// Example Types
export interface User {
  id: number;
  name: string;
  email: string;
}

export interface Order {
  orderId: string;
  amount: number;
  items: Array<{
    id: string;
    quantity: number;
  }>;
}

// Example Usage:
export const examples = {
  // 1. Success Response with User data
  userSuccessExample: {
    success: true,
    data: {
      id: 1,
      name: "John Doe",
      email: "<EMAIL>"
    }
  } as MnetCoreResponse<User>,

  // 2. Success Response with Order data
  orderSuccessExample: {
    success: true,
    data: {
      orderId: "ORD123",
      amount: 1500,
      items: [
        { id: "ITEM1", quantity: 2 },
        { id: "ITEM2", quantity: 1 }
      ]
    }
  } as MnetCoreResponse<Order>,

  // 3. Error Response - User Not Found
  userNotFoundExample: {
    success: false,
    error: {
      code: ErrorCode.NOT_FOUND,
      message: "User not found"
    }
  } as MnetCoreResponse<User>,

  // 4. Error Response - Invalid Order
  invalidOrderExample: {
    success: false,
    error: {
      code: ErrorCode.VALIDATION_ERROR,
      message: "Invalid order amount"
    }
  } as MnetCoreResponse<Order>,

  // Using helper functions
  helperExamples: {
    successUserResponse: createSuccessResponse<User>({
      id: 2,
      name: "Jane Doe",
      email: "<EMAIL>"
    }),

    errorOrderResponse: createErrorResponse<Order>(
      "Order validation failed",
      ErrorCode.VALIDATION_ERROR
    )
  }
};
