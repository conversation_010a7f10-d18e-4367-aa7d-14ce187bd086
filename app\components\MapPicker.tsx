// ~/components/MapPicker.tsx
import React, { useCallback, useEffect, useRef, useState } from "react";
import { LocateFixed } from "lucide-react";
import {
  GoogleMap,
  useJsApiLoader,
  Autocomplete,
  Libraries
} from "@react-google-maps/api";
import Toast from "./Toast";

interface MapPickerProps {
  latitude: string;
  longitude: string;
  onLocationSelect: (lat: string, lng: string, addressString: string) => void;
  googleMapsApiKey: string;
  searchFocused?: boolean;
  onCurrentLocationClick?: () => void;
  isLoadingCurrentLocationParent?: boolean;
}

const containerStyle = {
  width: "100%",
  height: "100%"
};

const defaultCenter = {
  lat: 12.9780357, // Default changed from San Francisco to Office, i.e Goodworks Whitefield
  lng: 77.7169915
};

// Maximum number of retries for geolocation
const MAX_LOCATION_RETRIES = 3;
// Delay between retries in milliseconds
const RETRY_DELAY = 2000;

// Define libraries as a constant to prevent reload warnings
const GOOGLE_MAPS_LIBRARIES: Libraries = ["places"];

const MapPicker: React.FC<MapPickerProps> = ({
  latitude,
  longitude,
  onLocationSelect,
  googleMapsApiKey,
  searchFocused,
  onCurrentLocationClick,
  isLoadingCurrentLocationParent
}) => {
  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey,
    libraries: GOOGLE_MAPS_LIBRARIES
  });

  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number }>(
    latitude && longitude
      ? { lat: parseFloat(latitude), lng: parseFloat(longitude) }
      : defaultCenter
  );

  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const searchAddressRef = useRef<HTMLInputElement>(null);
  const [isLoadingCurrentLocation, setIsLoadingCurrentLocation] =
    useState(false);
  const [locationRetryCount, setLocationRetryCount] = useState(0);
  const [locationError, setLocationError] = useState<string | null>(null);

  useEffect(() => {
    if (searchAddressRef.current) {
      searchAddressRef.current.focus();
    }
  }, [searchFocused]);

  useEffect(() => {
    if (!isLoadingCurrentLocationParent) {
      setIsLoadingCurrentLocation(false);
    }
  }, [isLoadingCurrentLocationParent]);

  const getLocationWithRetry = useCallback(
    (
      successCallback: PositionCallback,
      errorCallback: PositionErrorCallback,
      retryCount = 0
    ) => {
      setLocationRetryCount(retryCount);

      navigator.geolocation.getCurrentPosition(
        successCallback,
        (error) => {
          console.warn(
            `Geolocation error (attempt ${retryCount + 1}/${
              MAX_LOCATION_RETRIES + 1
            }):`,
            error.message
          );

          // Check if it's a temporary error that we should retry
          if (
            (error.code === error.TIMEOUT ||
              error.code === error.POSITION_UNAVAILABLE) &&
            retryCount < MAX_LOCATION_RETRIES
          ) {
            setLocationError(
              `Temporary location error. Retrying... (${
                retryCount + 1
              }/${MAX_LOCATION_RETRIES})`
            );

            // Retry after delay
            setTimeout(() => {
              getLocationWithRetry(
                successCallback,
                errorCallback,
                retryCount + 1
              );
            }, RETRY_DELAY);
          } else {
            // Max retries reached or permanent error
            setLocationError(null);
            errorCallback(error);
          }
        },
        {
          timeout: 15000,
          maximumAge: 0,
          enableHighAccuracy: true
        }
      );
    },
    []
  );

  useEffect(() => {
    if (initialLoad && navigator.geolocation) {
      setIsLoadingCurrentLocation(true);
      setLocationError(null);

      getLocationWithRetry(
        (position) => {
          const currentPos = {
            lat: parseFloat(latitude) || position.coords.latitude,
            lng: parseFloat(longitude) || position.coords.longitude
          };
          setMapCenter(currentPos);
          onLocationSelect(
            currentPos.lat.toString(),
            currentPos.lng.toString(),
            autocompleteRef?.current?.getPlace()?.formatted_address || ""
          );

          if (mapRef.current) {
            mapRef.current.panTo(currentPos);
          }
          setInitialLoad(false);
          setIsLoadingCurrentLocation(false);
          setLocationError(null);
        },
        () => {
          console.warn(
            "Geolocation permission denied or unavailable after retries."
          );
          setInitialLoad(false);
          setIsLoadingCurrentLocation(false);
          setLocationError(null);
        }
      );
    }
  }, [
    initialLoad,
    onLocationSelect,
    getLocationWithRetry,
    latitude,
    longitude
  ]);

  const onMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
  }, []);

  const onIdle = useCallback(() => {
    if (mapRef.current) {
      const center = mapRef.current.getCenter();
      if (center) {
        const lat = center.lat().toString();
        const lng = center.lng().toString();
        onLocationSelect(
          lat,
          lng,
          autocompleteRef?.current?.getPlace()?.formatted_address || ""
        );
      }
    }
  }, [onLocationSelect]);

  const onLoadAutocomplete = (
    autocomplete: google.maps.places.Autocomplete
  ) => {
    autocompleteRef.current = autocomplete;
  };

  const onPlaceChanged = () => {
    if (autocompleteRef.current !== null) {
      const place = autocompleteRef.current.getPlace();
      if (place.geometry && place.geometry.location) {
        const newCenter = {
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng()
        };
        setMapCenter(newCenter);
        onLocationSelect(
          newCenter.lat.toString(),
          newCenter.lng.toString(),
          place.formatted_address || ""
        );
        if (mapRef.current) {
          mapRef.current.panTo(newCenter);
        }
      }
    }
  };

  const handleCurrentLocation = () => {
    if (navigator.geolocation) {
      setIsLoadingCurrentLocation(true);
      setLocationError(null);

      getLocationWithRetry(
        (position) => {
          const currentPos = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setMapCenter(currentPos);
          onLocationSelect(
            currentPos.lat.toString(),
            currentPos.lng.toString(),
            autocompleteRef?.current?.getPlace()?.formatted_address || ""
          );
          if (mapRef.current) {
            mapRef.current.panTo(currentPos);
          }
          setIsLoadingCurrentLocation(false);
          setLocationError(null);
        },
        (error) => {
          setIsLoadingCurrentLocation(false);
          console.error("Error getting current location:", error);

          let errorMessage = "Unable to retrieve your location.";
          if (error.code === error.TIMEOUT) {
            errorMessage = "Location request timed out. Please try again.";
          } else if (error.code === error.POSITION_UNAVAILABLE) {
            errorMessage =
              "Location information unavailable. Please try again later.";
          } else if (error.code === error.PERMISSION_DENIED) {
            // Hide the error message for now because we're using a modal to handle it
            // errorMessage =
            //   "Location permission denied. Please enable location services.";
          }

          setLocationError(errorMessage);
        }
      );
    } else {
      setLocationError("Geolocation is not supported by your browser.");
    }
  };

  // Add this effect to update the map when latitude or longitude props change
  useEffect(() => {
    if (latitude && longitude && mapRef.current) {
      const newCenter = {
        lat: parseFloat(latitude),
        lng: parseFloat(longitude)
      };

      console.log("Map marker moving to coordinates:", newCenter);

      // Update the map center state
      setMapCenter(newCenter);

      // Pan the map to the new location
      mapRef.current.panTo(newCenter);
    }
  }, [latitude, longitude]);

  if (loadError) {
    return <div>Error loading maps</div>;
  }

  return isLoaded ? (
    <div
      data-prevent-pull-to-refresh="true"
      className="h-full flex flex-col"
      style={{ position: "relative" }}
    >
      <div className="absolute top-0 w-full z-10 p-2 ">
        <Autocomplete
          onLoad={onLoadAutocomplete}
          onPlaceChanged={onPlaceChanged}
          restrictions={{ country: "IN" }}
        >
          <input
            ref={searchAddressRef}
            className="px-4 border-2 rounded-3xl border-gray-300 shadow-lg shadow-gray-200 focus-within:border-teal-600"
            type="text"
            placeholder="Search for a building, street name, or area"
            style={{
              boxSizing: "border-box",
              width: "100%",
              height: "44px",
              fontSize: "16px",
              outline: "none",
              marginBottom: "10px",
              backgroundColor: "white"
            }}
          />
        </Autocomplete>
      </div>

      {!isLoadingCurrentLocation && (
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -100%)",
            zIndex: 1
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            height="1.5rem"
            viewBox="0 0 24 24"
            width="1.5rem"
            fill="#d00"
          >
            <path d="M0 0h24v24H0z" fill="none" />
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5S10.62 6.5 12 6.5s2.5 1.12 2.5 2.5S13.38 11.5 12 11.5z" />
          </svg>
        </div>
      )}

      {isLoadingCurrentLocation ? (
        <div className="flex items-center justify-center h-full">
          <div className="w-12 h-12 border-4 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : (
        <GoogleMap
          mapContainerStyle={containerStyle}
          center={mapCenter}
          zoom={
            mapCenter.lat !== defaultCenter.lat ||
            mapCenter.lng !== defaultCenter.lng
              ? 18
              : 12
          }
          onLoad={onMapLoad}
          onIdle={onIdle}
          options={{
            gestureHandling: "greedy"
          }}
        />
      )}

      <div
        className="flex flex-col items-center"
        style={{
          position: "absolute",
          bottom: 0,
          zIndex: 10,
          width: "calc(100% - 40px)"
        }}
      >
        {locationError && (
          <Toast
            message={locationError}
            type="error"
            position="bottom-center"
            showCloseButton={true}
            autoClose={true}
            width="full"
          />
        )}
        <button
          type="button"
          className={`bg-white border border-teal-600 text-teal-600 py-1 px-4 rounded-3xl my-4 ml-[40px] flex items-center min-w-[150px] justify-center ${
            isLoadingCurrentLocation ? "opacity-75" : ""
          }`}
          onClick={() => {
            handleCurrentLocation();
            onCurrentLocationClick?.();
          }}
          disabled={isLoadingCurrentLocation}
        >
          {isLoadingCurrentLocation ? (
            <>
              <div className="w-5 h-5 border-2 border-teal-600 border-t-transparent rounded-full animate-spin mr-2"></div>
              <span>
                {locationRetryCount > 0
                  ? `Retrying (${locationRetryCount}/${MAX_LOCATION_RETRIES})...`
                  : "Loading..."}
              </span>
            </>
          ) : (
            <>
              <LocateFixed size={18} className="mr-2 self-center" />
              <span>Current Location</span>
            </>
          )}
        </button>
      </div>
    </div>
  ) : (
    <div>Loading Maps...</div>
  );
};

export default React.memo(MapPicker);
