export type AddressType = "home" | "work" | "other";

export interface AddressDto {
  addressId: number;
  businessId: number;
  name: string | AddressType;
  address: string;
  latitude: string;
  longitude: string;
  isDefault: boolean;
  buyerInServiceArea: boolean;
}

export interface BuyerAddressDto {
  name?: string | AddressType;
  address?: string;
  latitude?: number;
  longitude?: number;
  disable?: boolean;
}

export interface AddressListDto {
  addressList: AddressDto[];
}
