import { ActionFunction, json } from "@remix-run/node";
import { precheckOrderAPI } from "~/services/buyer.service";
import { requireAuth } from "~/utils/clientReponse";
import type { PrecheckOrderPayload, User } from "~/types";
import { getSession } from "~/utils/session.server";

export const action: ActionFunction = async ({ request }) => {
  // Ensure user is authenticated
  const auth = await requireAuth(request);
  if (auth && auth.authRequired) {
    return json({ success: false, error: "Authentication required" }, { status: 401 });
  }

  const session = await getSession(request.headers.get("Cookie"));
  const user: User | null = session.get("user") as User | null;

  try {
    let payload: PrecheckOrderPayload | null = null;
    if (request.headers.get("Content-Type")?.includes("application/json")) {
      const body = await request.json();
      payload = { ...body, buyerId: user?.buyerId || 0 };
    }
    if (payload) {
      const response = await precheckOrderAPI(payload, request);
      return json(response.data);
    }
  } catch (error) {
    console.error("Error in precheck-order API:", error);
    return json({ success: false, error: "Failed to precheck order" }, { status: 500 });
  }
};
