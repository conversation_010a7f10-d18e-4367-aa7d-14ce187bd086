import React from "react";

import { ItemCategoryDtos } from "~/types";
import Button from "../Button";
interface CategoryItemProps {
  category: ItemCategoryDtos;
  isSelected: boolean;
  onSelect: () => void;
}
export const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  isSelected,
  onSelect
}) => {
  return (
    <Button className="flex flex-col items-center w-20" onClick={onSelect}>
      <div className="flex items-center justify-center w-20 h-20 rounded-[1rem] bg-white">
        <img
          src={category.picture}
          alt={category.name}
          className="w-16 h-16 object-cover"
        />
      </div>
      <p className="text-center mt-2 text-xs font-medium">{category.name}</p>
    </Button>
  );
};

export default CategoryItem;
