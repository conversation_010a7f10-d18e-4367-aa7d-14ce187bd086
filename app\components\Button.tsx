// app/components/Button.tsx

import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    loading?: boolean;
}

const Button: React.FC<ButtonProps> = ({ children, loading, disabled, className, ...props }) => {
    return (
        <button
            {...props}
            disabled={disabled || loading}
            className={`${className} ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
            {loading ? (
                <svg
                    className="animate-spin h-5 w-5 text-white mx-auto"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                    ></circle>
                    <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v8H4z"
                    ></path>
                </svg>
            ) : (
                children
            )}
        </button>
    );
};

export default Button;
