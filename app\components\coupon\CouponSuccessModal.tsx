import { useCouponStore } from "~/stores/coupon.store";

export const CouponSuccessModal = () => {
  const { selectedCoupon, showSuccessModal, hideSuccessModal } =
    useCouponStore();

  if (!showSuccessModal || !selectedCoupon) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg w-full max-w-md overflow-hidden relative">
        {/* Confetti decoration */}
        <div className="absolute top-0 left-0">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4 2L2 4"
              stroke="#8B5CF6"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d="M3 10L1 12"
              stroke="#EC4899"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d="M12 3L10 1"
              stroke="#F59E0B"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        </div>
        <div className="absolute top-2 right-6">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20 2L22 4"
              stroke="#8B5CF6"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d="M21 10L23 12"
              stroke="#EC4899"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d="M12 3L14 1"
              stroke="#F59E0B"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        </div>

        <div className="p-6 text-center">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
              <svg
                className="w-8 h-8 text-green-500"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </div>
          </div>

          <p className="text-gray-700 mb-2">
            &lsquo;{selectedCoupon.code}&rsquo; applied
          </p>

          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            You saved ₹{selectedCoupon.discountValue.toFixed(2)}
          </h2>

          <p className="text-gray-600 mb-6">with this coupon code.</p>

          <p className="text-teal-500 font-medium mb-4">Woohoo! Thanks</p>
        </div>

        <div className="p-4">
          <button
            onClick={hideSuccessModal}
            className="w-full py-3 px-6 text-white font-medium bg-teal-500 rounded-md hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50"
          >
            Continue Shopping
          </button>
        </div>
      </div>
    </div>
  );
};
