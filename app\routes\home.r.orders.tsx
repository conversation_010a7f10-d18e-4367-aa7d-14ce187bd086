import { NetworkAsset } from "../components/NetworkAssests";
// app/routes/home.orders.tsx

import { useState, useMemo, useEffect } from "react";
import {
  ActionFunction,
  LoaderFunction,
  json,
  redirect
} from "@remix-run/node";
import { Outlet, useLoaderData, useNavigate } from "@remix-run/react";
import {
  getOrdersAPI,
  getUpiPaymentStatus,
  initiatePayment
} from "~/services/buyer.service";
import {
  getSession,
  destroySession,
  commitSession
} from "~/utils/session.server";
import { InitiatePaymentResponse, OrderResponse, User, Order } from "~/types";
import OrderCard from "~/components/OrderCard";
import Button from "~/components/Button";
import { parseJWT } from "~/utils/token-utils";
import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { DecodedToken } from "~/types/user";
import { ArrowLeftIcon } from "lucide-react";
import { handleWhatsappClick } from "~/components/WhatsappCTA";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { useRequireAuth } from "~/hooks/useRequireAuth";
interface LoaderData {
  orders: OrderResponse;
  user: User;
  mobileNumber?: string;
}

interface LoaderErrorData {
  error: string;
}

interface ActionData {
  success?: boolean;
  message?: string;
  errors?: {
    [key: string]: string;
  };
  paymentUrl?: string;
  refId?: number;
}

const processInitiatePayment = async (
  request: Request,
  formData: FormData,
  access_token: string
) => {
  const decoded = parseJWT(access_token) as DecodedToken; // Adjust type as per your DecodedToken
  const buyerId = decoded.userDetails.buyerId;
  const amount = parseFloat(formData.get("amount") as string);
  const orderGroupId = parseInt(formData.get("orderGroupId") as string, 10);
  const note = `Initiating Upi payment for Rs ${amount}`;

  const errors: { [key: string]: string } = {};
  if (!buyerId) errors.buyerId = "buyerId is required";
  if (!amount) errors.amount = "amount is required";
  if (!orderGroupId) errors.orderGroupId = "orderGroupId is required";

  if (Object.keys(errors).length > 0) {
    return json<ActionData>({ errors }, { status: 400 });
  }

  const response = await initiatePayment(
    orderGroupId,
    {
      initiatedByUserId: buyerId,
      amount,
      note
    },
    request
  );
  // console.log("Payment response: " + JSON.stringify(response, null, 2));
  if (response) {
    // const paymentUrl = `upi://pay?pa=${encodeURIComponent(
    //   response.pa
    // )}&pn=${encodeURIComponent(response.pn)}&tr=${encodeURIComponent(
    //   response.tr
    // )}&am=${encodeURIComponent(response.am)}&cu=${encodeURIComponent(
    //   response.cu
    // )}&mc=${encodeURIComponent(response.mc)}`;

    // console.log("upiString: ", paymentUrl);
    return createClientResponse<ActionData, InitiatePaymentResponse>(
      request,
      {
        success: true,
        message: "Payment initiated successfully",
        paymentUrl: response.data.queryString,
        refId: response.data.refId
      },
      response
    );
  } else {
    return json<ActionData>({
      success: false,
      message: "Failed to initiate payment"
    });
  }
};

const processPaymentStatus = async (request: Request, formData: FormData) => {
  const refId = formData.get("refId") as string;
  if (!refId) {
    return json<ActionData>(
      { errors: { refId: "refId is required" } },
      { status: 400 }
    );
  }

  const response = await getUpiPaymentStatus(parseInt(refId, 10), request);

  if (response.data?.status === "PAID") {
    return createClientResponse(
      request,
      { paymentStatus: "SUCCESS" },
      response
    );
  } else if (
    response.data?.status === "INITIATED" ||
    response.data?.status === "PENDING"
  ) {
    return createClientResponse(
      request,
      { paymentStatus: "PENDING" },
      response
    );
  } else {
    return createClientResponse(request, { paymentStatus: "FAILED" }, response);
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token) {
    return redirect("/login");
  }

  const url = new URL(request.url);
  const formData = await request.formData();
  const requestName = formData.get("requestName");

  if (!requestName) {
    return json(
      { errors: { requestName: "requestName is required" } },
      { status: 400 }
    );
  }

  try {
    // Handle payment status check separately
    if (url.searchParams.has("check-status")) {
      if (requestName === "PaymentStatus") {
        return processPaymentStatus(request, formData);
      }
      return json({ error: "Invalid request" }, { status: 400 });
    }

    // Handle other actions
    if (requestName === "InitiatePayment") {
      return processInitiatePayment(request, formData, access_token);
    } else if (requestName === "PaymentStatus") {
      return processPaymentStatus(request, formData);
    } else {
      return json(
        { errors: { requestName: "Invalid requestName" } },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.error("Error processing payment:", error);
    if (error instanceof Error) {
      return json(
        { message: error.message || "Failed to process payment" },
        { status: 500 }
      );
    }
    return json({ message: "An unknown error occurred" }, { status: 500 });
  }
};

export const loader: LoaderFunction = async ({ request }) => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({
      ...auth,
      orders: { pendingOrders: [], completedOrders: [] }
    });
  }

  const url = new URL(request.url);
  const appSource = url.searchParams.get("source");

  if (!access_token || !user) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appSource, appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/home/<USER>
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      const headers = new Headers();
      headers.append("Set-Cookie", await destroySession(session));
      session = await getSession();
      session.set("appConfig", { appSource, appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return redirect(`/home/<USER>
    }
    const response = await getOrdersAPI(user.buyerId, request);

    return createClientResponse<LoaderData, OrderResponse>(
      request,
      {
        orders: {
          pendingOrders: (response.data?.pendingOrders || []).sort(
            (a, b) => b.id - a.id
          ),
          completedOrders: (response.data?.completedOrders || []).sort(
            (a, b) => b.id - a.id
          )
        },
        user,
        mobileNumber: decoded.userDetails.mobileNumber
      },
      response
    );
  } catch (error) {
    console.error("Error fetching orders:", error);
    throw json<LoaderErrorData>(
      { error: "Failed to fetch orders" },
      { status: 500 }
    );
  }
};

export default function Orders() {
  const navigate = useNavigate();
  const { orders, error } = useLoaderData<LoaderData & LoaderErrorData>();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { appSource, networkConfig, appDomain } = useAppConfigStore(
    (state) => state
  );

  useRequireAuth();

  useEffect(() => {
    if (error) setErrorMessage(error);
  }, [error]);

  const handleRefresh = () => navigate(0);

  const activeOrders = useMemo(
    () =>
      orders.pendingOrders
        .filter((order) => {
          const status = order.status?.toLowerCase();
          return status !== "delivered" && status !== "cancelled";
        })
        .map((order) => ({
          ...order
        }))
        .sort(
          (a, b) =>
            new Date(b.createdOn).getTime() - new Date(a.createdOn).getTime()
        ),
    [orders.pendingOrders]
  );

  const pastOrders = useMemo(
    () =>
      [
        ...orders.completedOrders,
        ...orders.pendingOrders.filter((order) => {
          const status = order.status?.toLowerCase();
          return status === "delivered" || status === "cancelled";
        })
      ]
        .map((order) => ({
          ...order
        }))
        .sort(
          (a, b) =>
            new Date(b.createdOn).getTime() - new Date(a.createdOn).getTime()
        ),
    [orders.completedOrders, orders.pendingOrders]
  );

  return (
    <div className="bg-gray-100">
      <Outlet />
      <div className="fixed flex flex-col h-screen bg-gray-50 w-full">
        {/* Header */}
        <div className="flex justify-between items-center px-4 py-3 bg-white shadow-sm">
          <div className="flex items-center">
            <button
              onClick={() => {
                if (appSource === "whatsappchat" && appDomain !== "RET11") {
                  handleWhatsappClick(
                    networkConfig?.wabMobileNumber || "",
                    "Track order"
                  );
                } else {
                  navigate(-1);
                }
              }}
              className="mr-3"
            >
              <ArrowLeftIcon className="w-5 h-5 text-gray-500" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">My Orders</h1>
          </div>
        </div>

        {/* Error Message */}
        {errorMessage && (
          <div className="p-4 bg-red-100 text-red-700 text-center">
            {errorMessage}
          </div>
        )}

        {/* Orders List */}
        <div className="flex-1 overflow-y-auto">
          {/* Active Orders Section */}
          {activeOrders.length > 0 && (
            <div className="mb-6">
              <h2 className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50">
                Active Orders
              </h2>
              <div className="px-4">
                {activeOrders.length > 0 ? (
                  activeOrders.map((order) => (
                    <OrderCard
                      key={order.id}
                      orderDetails={order as Order}
                      onPress={() =>
                        navigate(`/home/<USER>/order/${order.id}`)
                      }
                    />
                  ))
                ) : (
                  <p className="text-center text-gray-500 py-4">
                    No active orders
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Past Orders Section */}
          {pastOrders.length > 0 && (
            <div>
              <h2 className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50">
                Past Orders
              </h2>
              <div className="px-4">
                {pastOrders.length > 0 ? (
                  pastOrders.map((order) => (
                    <OrderCard
                      key={order.id}
                      orderDetails={order as Order}
                      onPress={() =>
                        navigate(`/home/<USER>/order/${order.id}`)
                      }
                    />
                  ))
                ) : (
                  <p className="text-center text-gray-500 py-4">
                    No past orders
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Empty State */}
          {activeOrders.length === 0 && pastOrders.length === 0 && (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <svg
                className="w-16 h-16 mb-4"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 3c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zm0 10c-2.2 0-4-1.8-4-4s1.8-4 4-4 4 1.8 4 4-1.8 4-4 4z" />
              </svg>
              <p className="text-lg font-medium">No orders yet</p>
              <p className="text-sm">Your orders will appear here</p>
            </div>
          )}

          {/* Footer Image */}
          <div className="self-end w-20 h-15 mt-20 mb-20 mx-auto">
            <NetworkAsset assetName="footer" />
          </div>
        </div>
      </div>
    </div>
  );
}

export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(0)} />;
}
