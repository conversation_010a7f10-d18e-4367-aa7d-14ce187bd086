import React from "react";
import { cn } from "~/utils/cn";
import { getFirstImageFromString } from "~/utils/string";

const CustomImage: React.FC<{
  src: string;
  alt: string;
  className: string;
}> = ({ src, alt, className }) => {
  return (
    <img
      src={getFirstImageFromString(src) || ""}
      alt={alt}
      onError={(e) => {
        e.currentTarget.onerror = null; // Prevent looping if fallback fails
        e.currentTarget.src = "https://ik.imagekit.io/u7uktwxu0/Business%20Icons/168430722_10471061%204.svg";
      }} 
      className={cn("", className)}
    />
  );
};

export default CustomImage;
