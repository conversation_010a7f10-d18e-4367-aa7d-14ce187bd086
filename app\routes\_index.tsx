// app/routes/index.tsx

import type { MetaFunction, LoaderFunction } from "@remix-run/node";
import { redirect, json } from "@remix-run/node";
import { getSession } from "~/utils/session.server";
import { useEffect } from "react";
import { requireAuth } from "~/utils/clientReponse";
import { useLoginStore } from "~/stores/login.store";

export const meta: MetaFunction = () => {
  return [
    { title: "New Remix App" },
    { name: "description", content: "Welcome to Remix!" }
  ];
};

// Loader function to check authentication and redirect accordingly
export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token");

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (access_token) {
    // User is authenticated, redirect to /home
    return redirect("/home");
  } else {
    // User is not authenticated, redirect to /login
    return redirect("/login");
  }
};

// Optional: If you want to keep the UI as a fallback or for other purposes
export default function Index() {
  // This component might never render because of the redirect,
  // but it's good to have it as a fallback.

  return null; // Or you can keep the existing UI if needed

  const { openLogin } = useLoginStore();
  useEffect(() => {
    openLogin();
  }, [openLogin]);
}
