// context/UserContext.tsx

import React, { createContext, useContext, useState, useMemo } from "react";
import { AppInfo, Cart } from "~/types";

// User interface (same as before)
export interface User {
  id?: string;
  name?: string;
  email?: string;
  existingOrderGroupId?: number;
  cart?: Cart;
  appInfo?: AppInfo
}

interface UserContextType {
  user: User | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const [user, setUser] = useState<User | null>(null);
  const value = useMemo(() => ({ user, setUser }), [user]);

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
