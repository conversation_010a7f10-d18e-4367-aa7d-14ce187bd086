// CLID (Click Identifier) utilities
// Handles extraction, storage, and retrieval of ctwa_clid for WhatsApp Click-to-Ads tracking
import { logWarn } from "./capi-logger";

/**
 * CLID expiration time (7 days in milliseconds)
 * Facebook recommends tracking attribution for up to 7 days
 */
const CLID_EXPIRATION_TIME = 7 * 24 * 60 * 60 * 1000; // 7 days

/**
 * CLID data structure for storage
 */
export interface CLIDData {
  ctwa_clid: string;
  ctwa_token: string;
  waba_id: string;
  wabDatasetId: string;
  timestamp: number;
  expiresAt: number;
}

/**
 * Extract clid from URL parameters
 * @param url - URL to extract clid from
 * @returns clid, token
 */
export function extractCLIDFromUrl(url: string): { ctwa_clid: string, ctwa_token: string, waba_id: string } {
  let clid = { ctwa_clid: "", ctwa_token: "", waba_id: "" };
  try {
    const urlObj = new URL(url);
    const ctwa_clid = urlObj.searchParams.get("ctwa_clid");
    const ctwa_token = urlObj.searchParams.get("ctwa_token");
    const waba_id = urlObj.searchParams.get("waba_id");
    clid.ctwa_clid = ctwa_clid && ctwa_clid.trim() !== "" ? ctwa_clid.trim() : "";
    clid.ctwa_token = ctwa_token && ctwa_token.trim() !== "" ? ctwa_token.trim() : "";
    clid.waba_id = waba_id && waba_id.trim() !== "" ? waba_id.trim() : "";

    return clid;
  } catch (error) {
    logWarn("Error extracting clid from URL", { error: error.message, url });
    return clid;
  }
}


/**
 * Set up CLID data for storage
 * @param clid - CLID to store
 * @param wabDatasetId - Whatsapp business Dataset Id
 * @returns CLIDData
 */
export function setUpCLIDData(clid: { ctwa_clid: string, ctwa_token: string, waba_id: string }, wabDatasetId: string): CLIDData {
  let clidData = { ctwa_clid: "", ctwa_token: "", waba_id: "", wabDatasetId: "", timestamp: 0, expiresAt: 0 };
  try {
    clidData.ctwa_clid = clid.ctwa_clid;
    clidData.ctwa_token = clid.ctwa_token;
    clidData.waba_id = clid.waba_id;
    clidData.wabDatasetId = wabDatasetId;
    const timestamp = Date.now();
    clidData.timestamp = timestamp;
    clidData.expiresAt = timestamp + CLID_EXPIRATION_TIME;
  } catch (error) {
    logWarn("Error setting up clid data", { error: error.message, clid, wabDatasetId });
  }

  return clidData;
}


/**
 * Retrieve CLID data from cookie
 * @returns CLIDData or null if not found
 */
export function getCLIDData() : CLIDData | null {
  if (typeof window === "undefined") return null;
  
  try {
    const cookie = document.cookie;
    if (!cookie) return null;
    const clidCookie = cookie.split(";").find((c) => c.trim().startsWith("wab_clid="));
    if (!clidCookie) return null;
    const clidData = JSON.parse(decodeURIComponent(clidCookie.split("=")[1]));
    return clidData;
  } catch (error) {
    logWarn("Error retrieving clid data from cookie", { error: error.message });
    return null;
  }
}


/**
 * Retrieve CLID data from request
 * @param request - Request object
 * @returns CLIDData or null if not found
 */
export function retrieveCLIDData(request: Request): CLIDData | null {
  try {
    const cookie = request.headers.get("Cookie");
    if (!cookie) return null;
    const clidCookie = cookie.split(";").find((c) => c.trim().startsWith("wab_clid="));
    if (!clidCookie) return null;
    const clidData = JSON.parse(decodeURIComponent(clidCookie.split("=")[1]));
    return clidData;
  } catch (error) {
    logWarn("Error retrieving clid data from cookie", { error: error.message });
    return null;
  }
}