// app/components/MoQPopup.tsx

import React from "react";
import Button from "@components/Button";
import { formatCurrency } from "~/utils/format";

interface MoQPopupProps {
  visible: boolean;
  onClose: () => void;
  qty: number;
  currentQty: number;
  value: number;
  showMoq: boolean;
  showMov: boolean;
}

const MoQPopup: React.FC<MoQPopupProps> = ({
  visible,
  onClose,
  qty,
  currentQty,
  value,
  showMoq,
  showMov
}) => {
  if (!visible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="flex flex-col items-center justify-center bg-white p-6 rounded-lg shadow-lg w-[90%]">
        <h2 className="text-md font-medium mb-2 text-gray-900">
          {showMoq ? "Order too small!" : "Minimum order amount not met"}
        </h2>
        <div className="text-center">
          {showMoq && (
            <p className="flex flex-col gap-1 text-sm text-typography-700 mb-2">
              {/* <span className="text-red-500">{qty} KG</span>. */}
              <span>
                Online orders are valid only for quantities above{" "}
                <span className="font-bold">{qty}kg</span>.
              </span>
              <span>
                Please add at least{" "}
                <span className="text-secondary font-bold">
                  {Math.abs(qty - currentQty)}kg
                </span>{" "}
                more to proceed.
              </span>
            </p>
          )}
          {showMov && (
            <p className="text-sm text-typography-700 mb-2">
              {"Your total order value must be at least"}
              <span className="font-bold text-secondary">
                {" "+formatCurrency(value)}
              </span>
              {" to place an order. Please add more items to proceed."}
            </p>
          )}
        </div>
        <Button
          onClick={onClose}
          className="text-md w-full bg-primary text-white py-2 rounded mt-3"
        >
          OK
        </Button>
      </div>
    </div>
  );
};

export default MoQPopup;
