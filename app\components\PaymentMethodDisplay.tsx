import React from "react";
import { ChevronRight, CreditCard } from "lucide-react";
import { PaymentMethod } from "./PaymentMethodSheet";

interface PaymentMethodDisplayProps {
  paymentMethod: PaymentMethod;
  onClick: () => void;
  className?: string;
}

/**
 * Component to display the currently selected payment method
 */
const PaymentMethodDisplay: React.FC<PaymentMethodDisplayProps> = ({
  paymentMethod,
  onClick,
  className = ""
}) => {
  // Helper function to get payment method icon
  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case "google_pay":
        return (
          <img src="/gpay-logo.png" alt="Google Pay" className="w-6 h-6" />
        );
      case "phone_pe":
        return (
          <img src="/phonepe-logo.png" alt="Phone Pe" className="w-6 h-6" />
        );
      case "cod":
        return <img src="/cash_icon.svg" alt="COD" className="w-6 h-6" />;
      case "card":
        return <CreditCard size={24} className="text-gray-600" />;
      default:
        return <CreditCard size={24} className="text-gray-600" />;
    }
  };

  // Helper function to get payment method name
  const getPaymentMethodName = (method: PaymentMethod) => {
    switch (method) {
      case "google_pay":
        return "Google Pay";
      case "phone_pe":
        return "Phone Pe";
      case "cod":
        return "Cash on Delivery";
      case "card":
        return "Credit / Debit Card";
      default:
        return "Payment Method";
    }
  };

  return (
    <button
      className={`flex items-center gap-2 border rounded-lg p-2 mt-1 ${className}`}
      onClick={onClick}
      type="button"
    >
      <div className="flex align-center item-center justify-center w-6 h-6">
        {getPaymentMethodIcon(paymentMethod)}
      </div>
      <div className="text-xs font-medium text-typography-800">
        {getPaymentMethodName(paymentMethod)}
      </div>
      <ChevronRight size={16} className="ml-auto text-gray-500" />
    </button>
  );
};

export default PaymentMethodDisplay;
