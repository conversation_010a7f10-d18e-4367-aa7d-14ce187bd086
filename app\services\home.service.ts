// app/services/home.service.ts

import { apiRequest, getApiUrl } from "~/utils/api";
import { BuyerHomeData } from "~/types";
import { getDomainFromRequest } from "@utils/domain";
import { ApiResponse, MnetCoreResponse } from "~/types/Api";

/**
 * Fetches delivery options for the buyer's home screen.
 * @param buyerId The ID of the buyer.
 * @returns A promise that resolves to BuyerHomeData.
 */
export async function getDeliveryOptionsAPI(
  request: Request
): Promise<ApiResponse<BuyerHomeData>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);

  const url = getApiUrl(
    "/home",
    undefined,
    {},
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<BuyerHomeData>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("No data found");
    }
  } catch (error) {
    console.error("Error fetching delivery options:", error);
    throw error;
  }
}

export type FreeItemType = {
  id: number;
  active: boolean;
  code: string;
  expiresAt: string;
  networkBuyerId: number;
  sellerItemId: number;
  sellerItemName: string;
  status: string;
  uuid: string;
};

const API_BASE_URL = process.env.API_BASE_URL;

export async function getFreeItemDetails(
  id: string,
  request: Request
): Promise<ApiResponse<MnetCoreResponse<FreeItemType>>> {
  const url = API_BASE_URL + `/buyer/offline-free-item/${id}`;

  try {
    const response = await apiRequest<MnetCoreResponse<FreeItemType>>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Get free item details api failed");
    }
  } catch (error) {
    console.error("Error get free item details api:", error);
    throw error;
  }
}

export async function redeemFreeItem(
  id: string,
  request: Request
): Promise<ApiResponse<MnetCoreResponse<FreeItemType>>> {
  const url = API_BASE_URL + `/buyer/offline-free-item/redeem?uuid=${id}`;

  try {
    const response = await apiRequest<MnetCoreResponse<FreeItemType>>(
      url,
      "PUT",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Redeem free item api failed");
    }
  } catch (error) {
    console.error("Error redeem free item api:", error);
    throw error;
  }
}
