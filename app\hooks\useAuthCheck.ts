import { useEffect } from "react";
import { useFetcher, useLocation } from "@remix-run/react";
import { useLoginStore } from "~/stores/login.store";
import type { User } from "~/types";

interface AuthCheckData {
  isAuthenticated: boolean;
  user?: User;
}

/**
 * Hook to check if user is authenticated and open login modal if not
 * @param requiresAuth - Whether the current page requires authentication
 * @param redirectPath - Path to redirect after login (defaults to current path)
 */
export function useAuthCheck(requiresAuth: boolean = false) {
  const fetcher = useFetcher<AuthCheckData>();
  const location = useLocation();
  const { openLogin, setRedirectPath } = useLoginStore();

  useEffect(() => {
    if (requiresAuth) {
      fetcher.load("/api/auth-check");
    }
  }, [requiresAuth, fetcher]);

  useEffect(() => {
    if (fetcher.data && !fetcher.data.isAuthenticated && requiresAuth) {
      setRedirectPath(location.pathname + location.search);
      openLogin();
    }
  }, [
    fetcher.data,
    openLogin,
    location.pathname,
    location.search,
    requiresAuth,
    setRedirectPath
  ]);

  return {
    isAuthenticated: fetcher.data?.isAuthenticated ?? false,
    isLoading: fetcher.state === "loading",
    user: fetcher.data?.user
  };
}
