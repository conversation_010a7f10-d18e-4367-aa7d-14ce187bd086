// utils/localStorage.ts

/**
 * Saves a value to localStorage under the specified key.
 * Serializes the value to a JSON string before storing.
 * Works with objects, numbers, and strings.
 *
 * @param key - The key under which the value is stored.
 * @param value - The value to store (can be of any type).
 */
export function setItem<T>(key: string, value: T): void {
  if (typeof window !== "undefined" && window.localStorage) {
    try {
      const serializedValue = JSON.stringify(value);
      window.localStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error(`Error saving ${key} to localStorage:`, error);
    }
  }
}

/**
 * Retrieves a value from localStorage by key.
 * Deserializes the JSON string back to the original type.
 *
 * @param key - The key of the value to retrieve.
 * @returns The retrieved value of type T, or null if not found.
 */
export function getItem<T>(key: string): T | null {
  if (typeof window !== "undefined" && window.localStorage) {
    try {
      const serializedValue = window.localStorage.getItem(key);
      if (serializedValue === null) {
        return null;
      }
      return JSON.parse(serializedValue) as T;
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
      return null;
    }
  }
  return null;
}

export async function getStoredData(
  key: string,
  parse: boolean = false
): Promise<any> {
  if (typeof window !== "undefined") {
    const data = localStorage.getItem(key);
    return parse ? JSON.parse(data || "{}") : data;
  }
  return null;
}

export function cleanLocalStorage() {
  if (typeof window !== "undefined" && window.localStorage) {
    try {
      window.localStorage.clear();
    } catch (error) {
      console.error(`Error cleaning the localStorage:`, error);
      return null;
    }
  }
  return null;
}

export function removeItem(item: string) {
  if (typeof window !== "undefined" && window.localStorage) {
    try {
      window.localStorage.removeItem(item);
    } catch (error) {
      console.error(`Error removing ${item} from localStorage:`, error);
      return null;
    }
  }
  return null;
}

export const removeAllCarts = () => {
  if (typeof window !== "undefined" && window.localStorage) {
    try {
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key?.startsWith("cart")) {
          localStorage.removeItem(key);
        }
      }

      // remove all order notes
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key?.startsWith("orderNote")) {
          localStorage.removeItem(key);
        }
      }

      return null;
    } catch (error) {
      console.error(`Error removing all carts from localStorage:`, error);
      return null;
    }
  }
};

export const removeAllInvalidCarts = (cartKey: string) => {
  if (typeof window !== "undefined" && window.localStorage) {
    try {
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key?.startsWith("cart") && key !== "cart_" + cartKey) {
          localStorage.removeItem(key);
        }
      }

      return null;
    } catch (error) {
      console.error(`Error removing all carts from localStorage:`, error);
      return null;
    }
  }
};
