import { useEffect, useState } from "react";

export const useCurrentLocation = () => {
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [isRequesting, setIsRequesting] = useState(false);
  const [error, setError] = useState<GeolocationPositionError | null>(null);

  useEffect(() => {
    refresh();
  }, []);

  const request = (
    onSuccess?: (position: GeolocationPosition) => void,
    onError?: (error: GeolocationPositionError) => void
  ) => {
    // Request current location
    setIsRequesting(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        };

        setLatitude(coords.latitude);
        setLongitude(coords.longitude);
        setError(null);
        // Set in cookie (stringify + encode)
        document.cookie = `coordinates=${encodeURIComponent(JSON.stringify(coords))}; path=/; max-age=3600`;

        setIsRequesting(false);
        onSuccess && onSuccess(position);
      },
      (error) => {
        console.error("Error requesting location:", error);
        setLatitude(null);
        setLongitude(null);
        setError(error);
        // Clear cookie
        document.cookie = `coordinates=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC; max-age=0`;

        setIsRequesting(false);
        onError && onError(error);
      },
      { enableHighAccuracy: true, maximumAge: 0 }
    );
  };

  const refresh = (
    onSuccess?: (position: GeolocationPosition) => void,
    onError?: (error: GeolocationPositionError) => void
  ) => {
    // Check if geolocation permission is granted
    navigator.permissions.query({ name: 'geolocation' }).then((result) => {
      if (result.state === 'granted') {
        // Request current location
        setIsRequesting(true);
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const coords = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            };

            setLatitude(coords.latitude);
            setLongitude(coords.longitude);
            setError(null);
            // Set in cookie (stringify + encode)
            document.cookie = `coordinates=${encodeURIComponent(JSON.stringify(coords))}; path=/; max-age=3600`;
            
            setIsRequesting(false)
            onSuccess && onSuccess(position);
          },
          (error) => {
            console.error("Error refreshing location:", error.message);
            setLatitude(null);
            setLongitude(null);
            setError(error);
            // Clear cookie
            document.cookie = `coordinates=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC; max-age=0`;
          
            setIsRequesting(false)
            onError && onError(error);
          },
          { enableHighAccuracy: true, maximumAge: 0 }
        );
      } else {
        onError && onError({ code: 0, message: "Permission denied", PERMISSION_DENIED: true } as any);
      }
    });
  };

  return { latitude, longitude, isRequesting, error, request, refresh };
};
