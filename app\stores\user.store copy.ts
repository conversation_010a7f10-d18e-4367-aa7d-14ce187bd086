import { create } from "zustand";
import { User } from "~/types";
import { DecodedToken } from "~/types/user";

interface userStore {
  tokenUser: User | null;
  decodedToken: DecodedToken | null;
  sellerDetails: {
    sellerId?: string;
    mobileNumber?: string;
  };
  setSellerDetails: (sellerId: string, mobileNumber: string) => void;
  setTokenUser: (tokenUser: User) => void;
  setDecodedToken: (newToken: DecodedToken) => void;
}

export const userStore = create<userStore>((set) => ({
  tokenUser: null,
  decodedToken: null,
  sellerDetails: {},
  setSellerDetails: (sellerId: string, mobileNumber: string) =>
    set({
      sellerDetails: {
        sellerId,
        mobileNumber
      }
    }),
  setTokenUser: (newUser) => set(() => ({ tokenUser: newUser })),
  setDecodedToken: (newToken) => set(() => ({ decodedToken: newToken }))
}));
