// app/routes/selectseller.tsx

import React, { useState, useMemo, useEffect } from "react";
import { LoaderFunction, json, redirect } from "@remix-run/node";
import { useLoaderData, useNavigate, useLocation } from "@remix-run/react";
import dayjs from "dayjs";

import SelectSellerCard from "@components/SelectSellerCard";
import RefreshButton from "@components/RefreshButton";
import { getSellerOptionsAPI } from "@services/buyer.service";
import { getSession, destroySession } from "@utils/session.server";
import { SellerOption, User } from "~/types";
import { BackNavHeader } from "~/components/BackNavHeader";
import { NetworkAsset } from "~/components/NetworkAssests";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { useUser } from "~/contexts/userContext";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import { removeAllCarts } from "~/utils/localStorage";
import { useRequireAuth } from "~/hooks/useRequireAuth";
interface LoaderData {
  sellerOptions: SellerOption[];
  deliveryDate: string;
  user: User;
}

interface LoaderErrorData {
  error: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({
      ...auth,
      user: {},
      sellerOptions: []
    });
  }

  if (!access_token || !user) {
    return redirect("/login", {
      headers: {
        "Set-Cookie": await destroySession(session)
      }
    });
  }

  const url = new URL(request.url);
  const deliveryDate = url.searchParams.get("deliveryDate");

  if (!deliveryDate) {
    return json<LoaderErrorData>(
      { error: "Delivery date is required" },
      { status: 400 }
    );
  }

  try {
    const sellerOptionsResponse = await getSellerOptionsAPI(
      deliveryDate,
      request
    );

    const sellerOptions = sellerOptionsResponse.data;

    console.log(sellerOptions);

    return createClientResponse<LoaderData, SellerOption[]>(
      request,
      {
        sellerOptions,
        deliveryDate,
        user
      },
      sellerOptionsResponse
    );
  } catch (error) {
    console.error("Error fetching seller options:", error);
    return json<LoaderErrorData>(
      { error: "Failed to fetch seller options" },
      { status: 500 }
    );
  }
};

const SelectSeller: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const loaderData = useLoaderData<LoaderData & LoaderErrorData>();

  useRequireAuth();

  const { sellerOptions, deliveryDate, error } = loaderData;

  const [searchStr, setSearchStr] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { setUser } = useUser();
  const { resetChooseItemsStore } = chooseitemsStore((state) => state);

  const filteredSellers = useMemo(() => {
    const lowerSearch = searchStr.toLowerCase();
    return sellerOptions.filter(
      (seller) =>
        seller.sellerName.toLowerCase().includes(lowerSearch) ||
        seller.availableItems.some((item) =>
          item.itemName.toLowerCase().includes(lowerSearch)
        )
    );
  }, [sellerOptions, searchStr]);

  useEffect(() => {
    console.log("zazazazaza clearing");
    localStorage.removeItem("order");
    // localStorage.removeItem("cart");
    removeAllCarts();
    setUser({ existingOrderGroupId: undefined });
    resetChooseItemsStore();
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Trigger a loader refresh by navigating to the same route
      navigate(location.pathname + location.search, { replace: true });
    } catch (err) {
      console.error("Error refreshing seller options:", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleSellerSelect = (seller: SellerOption) => {
    navigate(
      `/chooseitems?deliveryDate=${encodeURIComponent(deliveryDate)}&sellerId=${
        seller.sellerId
      }`
    );
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-white">
        <p className="text-red-500 text-lg">{error}</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header Section */}
      <BackNavHeader
        buttonText="Select Seller"
        handleBack={() => navigate("/home/<USER>")}
      >
        <div className="flex justify-between items-center mt-4">
          {/* Search Bar */}
          <div className="flex items-center px-3 py-1 border-2 border-gray-300 rounded-full">
            <img src="/search.png" alt="Search" className="w-4 h-4 mr-2" />
            <input
              type="text"
              placeholder="Search Item or Seller"
              value={searchStr}
              onChange={(e) => setSearchStr(e.target.value)}
              className="outline-none bg-white text-gray-600"
            />
          </div>
          {/* Refresh Button */}
          <RefreshButton onClick={handleRefresh} loading={isRefreshing} />
        </div>
      </BackNavHeader>

      {/* Delivery Date Section */}
      <div className="flex items-center p-4">
        <span className="text-sm text-gray-600">Delivery On:</span>
        <span className="ml-2 text-md font-normal text-teal-500">
          {`${dayjs(deliveryDate).format("dddd, D MMM")} (${
            dayjs(deliveryDate).isSame(dayjs(), "day") ? "today" : "tomorrow"
          })`}
        </span>
      </div>

      {/* Seller Options */}
      {sellerOptions.length > 0 ? (
        <div className="flex-1 overflow-y-auto p-4">
          {filteredSellers.length > 0 ? (
            filteredSellers
              // ?.filter((seller) => seller.buyerInServiceArea)
              .map((seller) => (
                <SelectSellerCard
                  key={seller.id}
                  seller={seller}
                  onSelect={() => handleSellerSelect(seller)}
                />
              ))
          ) : (
            <p className="text-center text-gray-500">
              No sellers match your search.
            </p>
          )}
          <div className="h-10" />
          <div className="self-end w-20 h-15 mt-20 mb-5 mx-auto">
            <NetworkAsset assetName="footer" />
          </div>
        </div>
      ) : (
        <div className="flex-1 flex flex-col items-center justify-center p-4">
          <img
            src="/no_sellers.png"
            alt="No Sellers"
            className="w-44 h-44 mb-8"
          />
          <p className="text-sm text-gray-500 text-center">
            Currently we are not available in your area. Please try later.
          </p>
        </div>
      )}
    </div>
  );
};

export default SelectSeller;
