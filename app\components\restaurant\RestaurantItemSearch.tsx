import React, { useState, useEffect, useRef, useCallback } from "react";
import { AvailableItem } from "~/types";
import SearchBar from "~/components/common/SearchBar";
import FilterBar, { FilterTag } from "./FilterBar";
import {
  extractTagsFromItems,
  filterItems,
  filterItemsSync,
  searchConfig
} from "~/utils/menuUtils";

interface RestaurantItemSearchProps {
  items: AvailableItem[];
  onFilteredItemsChange: (
    filteredItems: AvailableItem[],
    isSearchActive: boolean
  ) => void;
  className?: string;
  useAbbreviations?: boolean;
  debounceTime?: number;
  preferSync?: boolean; // Prefer synchronous search (faster but less accurate)
}

/**
 * Search component for the restaurant menu with filtering and search functionality
 */
const RestaurantItemSearch: React.FC<RestaurantItemSearchProps> = ({
  items,
  onFilteredItemsChange,
  className = "",
  useAbbreviations = searchConfig.useAbbreviationDictionary,
  debounceTime = searchConfig.debounceTime,
  preferSync = false
}) => {
  // State
  const [searchValue, setSearchValue] = useState("");
  const [availableTags, setAvailableTags] = useState<FilterTag[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isDebouncing, setIsDebouncing] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Refs for tracking state between renders
  const lastFilteredItems = useRef<AvailableItem[]>([]);
  const lastSearchActive = useRef<boolean>(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const pendingSearchRef = useRef<Promise<AvailableItem[]> | null>(null);

  /**
   * Extract tags from available items to populate the filter bar
   */
  useEffect(() => {
    if (items && items.length > 0) {
      const tags = extractTagsFromItems(items);
      setAvailableTags(tags);
    }
  }, [items]);

  /**
   * Compare filter results with previous values to avoid unnecessary updates
   */
  const shouldUpdateResults = useCallback(
    (filtered: AvailableItem[], isSearchActive: boolean): boolean => {
      const hasFilteredItemsChanged =
        JSON.stringify(filtered.map((item) => item.sellerItemId)) !==
        JSON.stringify(
          lastFilteredItems.current.map((item) => item.sellerItemId)
        );
      const hasSearchActiveChanged =
        isSearchActive !== lastSearchActive.current;

      return hasFilteredItemsChanged || hasSearchActiveChanged;
    },
    []
  );

  /**
   * Handle search results whether they come from sync or async search
   */
  const handleSearchResults = useCallback(
    (results: AvailableItem[], isSearchActive: boolean) => {
      if (shouldUpdateResults(results, isSearchActive)) {
        lastFilteredItems.current = results;
        lastSearchActive.current = isSearchActive;
        onFilteredItemsChange(results, isSearchActive);
      }
      setIsSearching(false);
    },
    [shouldUpdateResults, onFilteredItemsChange]
  );

  /**
   * Perform the actual filtering operation and update the parent component
   */
  const performFiltering = useCallback(
    (searchTrim: string) => {
      const isSearchActive = searchTrim.length > 0;
      const searchOptions = { useAbbreviationDictionary: useAbbreviations };

      // Cancel any pending searches
      pendingSearchRef.current = null;

      // If preferSync is true, use the synchronous version
      if (preferSync) {
        // This only uses regex matching (faster but less accurate)
        const syncResults = filterItemsSync(
          items,
          searchTrim,
          selectedTags,
          searchOptions
        );
        handleSearchResults(syncResults, isSearchActive);
        return;
      }

      // Otherwise use the potentially async version with fuzzy search
      setIsSearching(true);
      const searchResult = filterItems(
        items,
        searchTrim,
        selectedTags,
        searchOptions
      );

      // Handle both synchronous and asynchronous results
      if (searchResult instanceof Promise) {
        // Store reference to the current promise
        pendingSearchRef.current = searchResult;

        searchResult
          .then((results) => {
            // Only update if this is still the most recent search
            if (pendingSearchRef.current === searchResult) {
              handleSearchResults(results, isSearchActive);
            }
          })
          .catch((error) => {
            console.error("Search error:", error);
            setIsSearching(false);
          });
      } else {
        // Handle synchronous results
        handleSearchResults(searchResult, isSearchActive);
      }
    },
    [items, selectedTags, useAbbreviations, preferSync, handleSearchResults]
  );

  /**
   * Apply debouncing to search operations to prevent excessive filtering
   */
  useEffect(() => {
    // Clear any existing timeout
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    const searchTrim = searchValue.trim();

    // If search is being cleared, perform filtering immediately
    if (searchTrim === "") {
      performFiltering(searchTrim);
      return;
    }

    // Otherwise, set a debounce flag and timer
    setIsDebouncing(true);

    // Set a new timeout for the configured debounce time
    debounceTimerRef.current = setTimeout(() => {
      performFiltering(searchTrim);
      setIsDebouncing(false);
    }, debounceTime);

    // Cleanup on unmount
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [
    items,
    searchValue,
    selectedTags,
    useAbbreviations,
    debounceTime,
    performFiltering
  ]);

  /**
   * Event Handlers
   */
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  const handleSearchClear = () => {
    setSearchValue("");
  };

  const handleTagSelect = (tagValue: string) => {
    setSelectedTags((prev) => {
      if (prev.includes(tagValue)) {
        return prev.filter((tag) => tag !== tagValue);
      } else {
        return [...prev, tagValue];
      }
    });
  };

  // Combine loading states
  const isLoading = isDebouncing || isSearching;

  /**
   * Render the search interface
   */
  return (
    <div className={`flex flex-col bg-white shadow-sm ${className}`}>
      <SearchBar
        value={searchValue}
        onChange={handleSearchChange}
        onClear={handleSearchClear}
        placeholder="Search dishes, ingredients, brands..."
        isLoading={isLoading && searchValue.trim().length > 0}
      />
      {isLoading && searchValue.trim().length > 0 && (
        <div className="px-4 py-1 text-xs text-gray-500 animate-pulse">
          {isDebouncing
            ? "Preparing search..."
            : "Searching for complex matches..."}
        </div>
      )}
      <FilterBar
        tags={availableTags}
        selectedTags={selectedTags}
        onTagSelect={handleTagSelect}
      />
    </div>
  );
};

export default RestaurantItemSearch;
