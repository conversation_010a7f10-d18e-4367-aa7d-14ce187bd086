// app/routes/home.tsx

import {
  Outlet,
  useLocation,
  Link,
  Navigate,
  useNavigation
} from "@remix-run/react";
import { Store, ShoppingBasket, UserRound, Wallet } from "lucide-react";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { NetworkConfig } from "~/types";
import { EnumToRouteMap } from "~/utils/constant";
import { getItem } from "~/utils/localStorage";
import { useHomeStore } from "~/stores/home.store";

// import AddToHomeScreenBanner from "@components/AddToHomeScreenBanner";

const BottomNavItem = ({
  icon,
  label,
  to,
  isActive
}: {
  icon: JSX.Element;
  to: string;
  label: string;
  isActive: boolean;
}) => (
  <Link
    to={to}
    className={`flex flex-col items-center transition-all duration-300 ease-in-out ${
      isActive ? "text-teal-500" : "text-p"
    }`}
  >
    {icon}
    <span className="text-xs mt-1">{label}</span>
  </Link>
);

export default function HomeLayout() {
  const location = useLocation();
  const navigate = useNavigation();
  const networkConfig = getItem<NetworkConfig>("networkConfig");
  const { appSource, appDomain } = useAppConfigStore((state) => state);
  const { hideBottomBar } = useHomeStore();

  {
    navigate.state === "loading" ||
      (navigate.state === "submitting" ? (
        <div className="fixed h-screen bg-gray-400 flex items-center justify-end">
          Loading
        </div>
      ) : null);
  }

  // If we're at exactly /home, redirect to /home/<USER>
  if (location.pathname === "/home" || location.pathname === "/home/") {
    if (appDomain === "RET11") {
      return <Navigate to="/home/<USER>" replace />;
    }

    if (appSource === "whatsappchat") {
      return <Navigate to="/chooseitems" replace />;
    }

    if (
      networkConfig?.defaultStartPage &&
      EnumToRouteMap[networkConfig.defaultStartPage] !== undefined
    ) {
      return (
        <Navigate to={EnumToRouteMap[networkConfig.defaultStartPage]} replace />
      );
    }
    return <Navigate to="/home/<USER>" replace />;
  }

  const isActive = (path: string) => {
    if (location.pathname === "/home") return path === "/home/<USER>";
    return location.pathname.includes(path);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      <div className="flex-grow overflow-auto">
        <Outlet />
      </div>
      {/* <AddToHomeScreenBanner /> */}

      {!hideBottomBar && appDomain !== "RET11" && (
        <nav className="bg-white border-t border-gray-200 fixed bottom-0 left-0 right-0 z-20">
          <div className="flex justify-around items-center h-16">
            {appDomain === "RET11" ? (
              <BottomNavItem
                to="/home/<USER>"
                icon={<Store size={24} />}
                label="Home"
                isActive={isActive("/home/<USER>")}
              />
            ) : (
              <BottomNavItem
                to="/home/<USER>"
                icon={<Store size={24} />}
                label="Mandi"
                isActive={isActive("/home/<USER>")}
              />
            )}
            {appDomain === "RET11" ? (
              <BottomNavItem
                to="/home/<USER>/orders"
                icon={<ShoppingBasket size={24} />}
                label="Orders"
                isActive={isActive("/home/<USER>/orders")}
              />
            ) : (
              <BottomNavItem
                to="/home/<USER>"
                icon={<ShoppingBasket size={24} />}
                label="Orders"
                isActive={isActive("/home/<USER>")}
              />
            )}
            <BottomNavItem
              to="/home/<USER>"
              icon={<Wallet size={24} />}
              label="wallet"
              isActive={isActive("/home/<USER>")}
            />
            <BottomNavItem
              to="/home/<USER>"
              icon={<UserRound size={24} />}
              label="Account"
              isActive={isActive("/home/<USER>")}
            />
          </div>
          {/* )} */}
        </nav>
      )}
    </div>
  );
}

// Export the loader from the Mandi component to handle data fetching for the default route
export { loader } from "./home.mandi";
