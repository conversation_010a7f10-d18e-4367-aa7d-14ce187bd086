import { ActionFunction, LoaderFunction, json } from "@remix-run/node";
import {
  useLoaderD<PERSON>,
  useFetcher,
  useNavigate,
  redirect,
  useLocation,
  ShouldRevalidateFunction
} from "@remix-run/react";
import { useEffect, useState } from "react";
import { getSession } from "~/utils/session.server";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import AddressList from "~/components/address/AddressList";
import {
  deleteAddressAPI,
  getAddressListAPI,
  markDefaultAddressAPI
} from "~/services/address.service";
import { AddressDto } from "~/types/address.types";
import Toast from "~/components/Toast";
import { BackNavHeader } from "~/components/BackNavHeader";
import { useRequireAuth } from "~/hooks/useRequireAuth";
import ConfirmModal from "~/components/ConfirmModal";
import { TriangleAlertIcon } from "lucide-react";
import { getSelectedSellerCookie } from "~/utils/cookie.server";

interface LoaderData {
  addresses: AddressDto[];
  selectedAddressId?: number;
  flowType: "select-address" | "address-list";
  from?: string;
  returnTo?: string;
}

interface ActionData {
  success?: boolean;
  error?: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, addresses: [] });
  }

  if (!access_token) {
    return redirect("/login");
  }

  const url = new URL(request.url);
  const flowType = url.searchParams.get("flowType") || "select-address";
  const from = url.searchParams.get("from") || "";
  const returnTo = url.searchParams.get("returnTo") || "";
  const selectedSeller = getSelectedSellerCookie(request);

  try {
    const response = await getAddressListAPI(
      request,
      selectedSeller?.sellerId ? Number(selectedSeller.sellerId) : undefined
    );
    const addresses = response.data || [];

    // Find the default address if available
    const defaultAddress = addresses?.find((address) => address.isDefault);
    const selectedId = defaultAddress?.addressId || addresses?.[0]?.addressId;

    return createClientResponse<LoaderData, AddressDto[]>(
      request,
      {
        addresses: addresses || [],
        selectedAddressId: selectedId,
        flowType: flowType as "select-address" | "address-list",
        from,
        returnTo
      },
      {
        ...response,
        data: addresses
      }
    );
  } catch (error) {
    console.error("Error fetching addresses:", error);
    throw new Error("Failed to load addresses");
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, success: false, error: "Authentication required" });
  }

  if (!access_token) {
    return json<ActionData>({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent");
  const addressId = formData.get("addressId");

  try {
    if (intent === "delete" && addressId) {
      try {
        const res = await deleteAddressAPI(
          { addressId: Number(addressId), address: { disable: true } },
          request
        );
        if (res.data?.success === true) {
          return json<ActionData>({ success: true });
        }
        return json<ActionData>({ success: false });
      } catch (error) {
        console.error("Error deleting address:", error);
        return json<ActionData>(
          { success: false, error: "Failed to delete address" },
          { status: 500 }
        );
      }
    }

    if (intent === "select" && addressId) {
      try {
        await markDefaultAddressAPI({ addressId: Number(addressId) }, request);
        return json<ActionData>({ success: true });
      } catch (error) {
        console.error("Error setting default address:", error);
        return json<ActionData>(
          { success: false, error: "Failed to set default address" },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error("Error processing form data:", error);
    return json<ActionData>(
      { success: false, error: "Something went wrong" },
      { status: 500 }
    );
  }

  return json<ActionData>(
    { success: false, error: "Invalid action" },
    { status: 400 }
  );
};

export const shouldRevalidate: ShouldRevalidateFunction = ({
  actionResult,
  defaultShouldRevalidate,
  formData
}) => {
  const intent = formData?.get("intent");
  if (actionResult?.success === true && intent === "select") {
    return false;
  }
  return defaultShouldRevalidate;
};

export default function AddressPage() {
  useRequireAuth();

  const { addresses, selectedAddressId, flowType, returnTo } =
    useLoaderData<LoaderData>();
  const deleteFetcher = useFetcher<ActionData>();
  const selectFetcher = useFetcher<ActionData>();
  const navigate = useNavigate();
  const location = useLocation();
  const [localSelectedAddressId, setLocalSelectedAddressId] = useState<
    number | undefined
  >(selectedAddressId);
  const [showToast, setShowToast] = useState(false);
  const [toastConfig, setToastConfig] = useState({
    message: "",
    type: "info" as "success" | "error" | "warning" | "info"
  });
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [deleteAddressId, setDeleteAddressId] = useState<number | null>(null);

  const handleDelete = (addressId: number) => {
    setIsDeleteModalOpen(true);
    setDeleteAddressId(addressId);
  };

  const handleEdit = (addressId: number) => {
    const editReturnTo = returnTo || "/select-address";
    const addressToEdit = addresses.find(
      (address) => address.addressId === addressId
    );

    navigate(`/changeaddress?redirectTo=${editReturnTo}`, {
      state: {
        address: addressToEdit,
        isEdit: true,
        from: "select-address",
        returnTo: editReturnTo,
        flowType: flowType
      }
    });
  };

  const handleSelect = (addressId: number) => {
    setLocalSelectedAddressId(addressId);
    selectFetcher.submit(
      { intent: "select", addressId: addressId.toString() },
      { method: "post" }
    );
  };

  const handleBack = () => {
    // Use returnTo from state first (for dynamic returns), then from URL params
    const stateReturnTo = location.state?.returnTo;
    // Default to cart for select-address flow, otherwise go to account
    const defaultPath =
      flowType === "select-address" ? "/cart" : "/home/<USER>";
    const effectiveReturnTo = stateReturnTo || returnTo || defaultPath;

    // Navigate to the return path with appropriate state
    navigate(effectiveReturnTo, {
      replace: true,
      state: {
        fromAddress: true,
        ...(location.state || {})
      }
    });
  };

  // Handle delete fetcher data
  useEffect(() => {
    if (deleteFetcher.data) {
      if (deleteFetcher.data.success) {
        setToastConfig({
          message: "Address deleted successfully",
          type: "success"
        });
        setShowToast(true);
      } else if (deleteFetcher.data.success === false) {
        setToastConfig({
          message: deleteFetcher.data.error || "Failed to delete address",
          type: "error"
        });
        setShowToast(true);
      }
    }
  }, [deleteFetcher.data]);

  // Handle select fetcher data
  useEffect(() => {
    if (selectFetcher.data?.success) {
      setToastConfig({
        message: "Default address updated successfully",
        type: "success"
      });
      setShowToast(true);

      // If we're in select-address flow, navigate back after a short delay
      if (flowType === "select-address") {
        setTimeout(() => {
          handleBack();
        }, 1000);
      }
    } else if (selectFetcher.data && selectFetcher.data.success === false) {
      setToastConfig({
        message: selectFetcher.data.error || "Failed to update default address",
        type: "error"
      });
      setShowToast(true);
    }
  }, [selectFetcher.data, flowType]);

  useEffect(() => {
    if (selectedAddressId) {
      setLocalSelectedAddressId(selectedAddressId);
    }
  }, [selectedAddressId]);

  // Get the appropriate title based on flow type
  const getPageTitle = () => {
    return flowType === "select-address"
      ? "Select Delivery Address"
      : "Address Book";
  };

  return (
    <div className="flex flex-col h-screen bg-[#F6F6F8]">
      <BackNavHeader
        buttonText={getPageTitle()}
        handleBack={handleBack}
        className="px-3 bg-[#F6F6F8]"
      />

      {showToast && (
        <Toast
          message={toastConfig.message}
          type={toastConfig.type}
          duration={3000}
          position="bottom-center"
          onClose={() => setShowToast(false)}
          width="full"
        />
      )}

      <div className="bg-[#F6F6F8]">
        <div className="mx-5 border-t border-neutral-500"></div>
      </div>

      <div className="flex-grow py-4 px-2 bg-[#F6F6F8] pb-40">
        <AddressList
          addresses={addresses}
          selectedAddressId={localSelectedAddressId}
          isLoading={false}
          onSelect={flowType === "select-address" ? handleSelect : undefined}
          onEdit={handleEdit}
          onDelete={handleDelete}
          redirectPath={`/changeaddress?redirectTo=${
            returnTo || "/select-address"
          }`}
          showDeliveryStatus={flowType === "select-address"}
        />
      </div>

      <ConfirmModal
        isOpen={isDeleteModalOpen}
        title="Confirm Deletion"
        message="Are you sure you want to delete this address?"
        icon={<TriangleAlertIcon className="w-5 h-5 text-red-600 mb-1" />}
        onConfirm={() => {
          deleteFetcher.submit(
            { intent: "delete", addressId: "" + deleteAddressId },
            { method: "post" }
          );
          setIsDeleteModalOpen(false);
          setDeleteAddressId(null);
        }}
        onCancel={() => {
          setIsDeleteModalOpen(false);
          setDeleteAddressId(null);
        }}
      />
    </div>
  );
}
