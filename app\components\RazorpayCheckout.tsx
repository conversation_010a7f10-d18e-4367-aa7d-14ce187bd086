import { useEffect, useState, useCallback } from "react";
import { useFetcher } from "@remix-run/react";
import type {
  RazorpayOptions,
  RazorpayResponse,
  PaymentRequest
} from "~/types/payment.types";
import Button from "~/components/Button";

interface RazorpayCheckoutProps {
  paymentRequest: PaymentRequest;
  onSuccess?: (response: RazorpayResponse) => void;
  onFailure?: (error: Error) => void;
  onClose?: () => void;
  buttonText?: string;
  buttonClassName?: string;
  disabled?: boolean;
  companyName?: string;
  theme?: {
    color?: string;
  };
}

interface RazorpayOrderResponse {
  error?: string;
  razorpayKey?: string;
  orderId?: string;
  amount?: number;
  currency?: string;
}

declare global {
  interface Window {
    Razorpay: new (options: RazorpayOptions) => {
      open: () => void;
    };
  }
}

const RazorpayCheckout = ({
  paymentRequest,
  onSuccess,
  onFailure,
  onClose,
  buttonText = "Pay Now",
  buttonClassName = "",
  disabled = false,
  companyName = "BRIH Solutions",
  theme = { color: "#2d7bf3" }
}: RazorpayCheckoutProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isRazorpayLoaded, setIsRazorpayLoaded] = useState(false);
  const orderFetcher = useFetcher<RazorpayOrderResponse>();
  const verifyFetcher = useFetcher();

  // Load Razorpay script
  useEffect(() => {
    if (typeof window === "undefined") return;

    if (window.Razorpay) {
      setIsRazorpayLoaded(true);
      return;
    }

    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.async = true;
    script.onload = () => setIsRazorpayLoaded(true);
    document.body.appendChild(script);

    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  // Handle payment verification
  const verifyPayment = useCallback(
    (response: RazorpayResponse) => {
      verifyFetcher.submit(
        {
          intent: "verifyPayment",
          razorpayOrderId: response.razorpay_order_id,
          razorpayPaymentId: response.razorpay_payment_id,
          razorpaySignature: response.razorpay_signature
        },
        { method: "post", action: "/api/razorpay" }
      );

      // Assume success and let the backend verification handle actual failures
      setIsLoading(false);
      onSuccess?.(response);
    },
    [verifyFetcher, onSuccess]
  );

  // Handle order creation response
  useEffect(() => {
    if (orderFetcher.state === "idle" && orderFetcher.data) {
      if (orderFetcher.data.error) {
        setIsLoading(false);
        onFailure?.(new Error(orderFetcher.data.error));
        return;
      }

      // Initialize Razorpay payment
      if (orderFetcher.data.orderId && window.Razorpay) {
        const { razorpayKey, orderId, amount, currency } = orderFetcher.data;

        const options: RazorpayOptions = {
          key: razorpayKey || "",
          amount: amount || 0,
          currency: currency || "INR",
          name: companyName,
          description: paymentRequest.description || "Purchase",
          order_id: orderId || "",
          handler: verifyPayment,
          prefill: {
            name: paymentRequest.customerName,
            email: paymentRequest.customerEmail,
            contact: paymentRequest.customerContact
          },
          notes: paymentRequest.notes,
          theme: theme,
          modal: {
            ondismiss: () => {
              setIsLoading(false);
              onClose?.();
            }
          }
        };

        try {
          const razorpayInstance = new window.Razorpay(options);
          razorpayInstance.open();
        } catch (error) {
          console.error("Failed to initialize Razorpay:", error);
          setIsLoading(false);
          onFailure?.(
            error instanceof Error
              ? error
              : new Error("Failed to initialize payment gateway")
          );
        }
      }
    }
  }, [
    orderFetcher.state,
    orderFetcher.data,
    companyName,
    onClose,
    onFailure,
    paymentRequest,
    theme,
    verifyPayment
  ]);

  // Handle payment initiation
  const initiatePayment = useCallback(() => {
    if (disabled || isLoading || !isRazorpayLoaded) return;

    setIsLoading(true);

    // Create order via the server-side API
    orderFetcher.submit(
      {
        intent: "createOrder",
        amount: Math.round(paymentRequest.amount * 100).toString(), // Convert to smallest currency unit (paise)
        currency: paymentRequest.currency || "INR",
        receipt: paymentRequest.receipt || `order_${Date.now()}`,
        notes: paymentRequest.notes || "{}",
        customerName: paymentRequest.customerName || "",
        customerEmail: paymentRequest.customerEmail || "",
        customerContact: paymentRequest.customerContact || "",
        preconfirmUid: paymentRequest.preconfirmUid || ""
      },
      { method: "post", action: "/api/razorpay" }
    );
  }, [disabled, isLoading, isRazorpayLoaded, orderFetcher, paymentRequest]);

  return (
    <Button
      onClick={initiatePayment}
      disabled={disabled || isLoading || !isRazorpayLoaded}
      className={buttonClassName}
    >
      {isLoading ? "Processing..." : buttonText}
    </Button>
  );
};

export default RazorpayCheckout;
