import { FC } from "react";
import { X } from "lucide-react";
import { OrderStatus } from "~/types";
import { cn } from "~/utils/cn";
interface TimelineDeliveryStatusProps {
  label: string;
  time: string;
  status?: OrderStatus;
}

const TimelineDeliveryStatus: FC<TimelineDeliveryStatusProps> = ({
  label,
  time,
  status
}) => {
  return (
    <div
      className={cn(
        "flex items-center justify-between rounded-lg",
        status === "Cancelled" ? "" : ""
      )}
    >
      <div className="flex items-center gap-2 space-x-2">
        {status === "Delivered" && (
          <div className="w-9 h-9 rounded-full bg-[#DFF2EF] text-primary flex items-center justify-center">
            <svg
              className="w-8 h-8 text-primary"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        )}
        {status === "Cancelled" && (
          <div className="w-9 h-9 rounded-full bg-[#FFECEF] flex items-center justify-center text-secondary text-sm">
            <X size={16} />
          </div>
        )}
        <div className="flex flex-col gap-0">
          <span className={cn("text-sm font-medium text-typography-600")}>
            {label}
          </span>
          <span className={cn("text-xs font-semibold text-[#3280DB]")}>
            {time}
          </span>
        </div>
      </div>

      {/* {status === "Cancelled" && (
        <button className="text-red-600 text-sm flex items-center space-x-1">
          <CircleX size={16} />
          <span>Cancelled</span>
        </button>
      )} */}
    </div>
  );
};

export default TimelineDeliveryStatus;
