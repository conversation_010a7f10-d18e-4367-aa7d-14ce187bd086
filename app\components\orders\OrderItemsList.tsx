import React from "react";
import { SellerOrderItem } from "~/types";
import ItemRow from "@components/ItemRow";
// import { roundOff } from "@utils/roundOff";
import { ConfirmedItemsList } from "./ConfirmedItemsList";
import { OrderNote } from "./OrderNote";
import { useCartStore } from "~/stores/cart.store";
import RestaurantItemRow from "../cart/RestaurantItemRow";
interface OrderItemsListProps {
  items: SellerOrderItem[];
  cartItems: Record<string, { qty: number }>;
  onAdd: (item: SellerOrderItem) => void;
  onRemove: (item: SellerOrderItem) => void;
  showNoteFeature?: boolean;
  orderNote?: string;
  onOpenNotePopup?: () => void;
  onAddonsUpdate?: () => void;
  displayPrices?: boolean;
}

export const OrderItemsList: React.FC<OrderItemsListProps> = ({
  items,
  cartItems,
  onAdd,
  onRemove,
  showNoteFeature = false,
  orderNote = "",
  onOpenNotePopup,
  onAddonsUpdate,
  displayPrices,
}) => {
  const { cartClientType } = useCartStore((state) => state);

  const availableItems = items.filter((item) => {
    const currentQty = item.quantity || 0;
    const orderedQty = item.availableCartItem?.orderedQty || 0;
    return (!item.isSoldOut || item.quantity > 0) && currentQty > orderedQty;
  });

  //   const allItems = items.filter((item) => {
  //     const currentQty = cartItems[item.sellerItemId]?.qty || 0;
  //     const orderedQty = item.availableCartItem?.orderedQty || 0;
  //     return !item.isSoldOut || item.quantity > 0;
  //   });

  //   if (allItems.length === 0) return null;

  //   const totalNewOrderAmount = availableItems.reduce((total, item) => {
  //     const currentQty = item.quantity || 0;
  //     const orderedQty = item.availableCartItem?.orderedQty || 0;
  //     const newQty = currentQty - orderedQty;
  //     return total + newQty * item.pricePerUnit;
  //   }, 0);

  return (
    <div className="mx-3 px-3 py-3 bg-white rounded-2xl shadow-lg flex flex-col">
      <div className="flex justify-between text-xs text-typography-400 tracking-wider border-b border-neutral-200 pb-1">
        <span>ORDER DETAILS</span>
        <span>
          {items.length} {`${items.length > 1 ? "items" : "item"}`}
        </span>
      </div>
      <ConfirmedItemsList items={items} displayPrices={displayPrices} />
      {availableItems.length === 0 ? (
        <div className="flex flex-col text-center text-sm font-normal text-typography-400 gap-2 py-2">
          No new item added to the cart.
        </div>
      ) : (
        <div className="flex flex-col">
          {availableItems.map((item, index) => {
            const currentQty = cartItems[item.sellerItemId]?.qty || 0;
            const orderedQty = item.availableCartItem?.orderedQty || 0;
            const newQty = currentQty - orderedQty;

            return (
              <React.Fragment key={item.sellerItemId}>
                {cartClientType === "retailer" ? (
                  <ItemRow
                    itemDetails={item}
                    onAdd={() => onAdd(item)}
                    onRemove={() => onRemove(item)}
                    quantity={newQty}
                    amount={newQty * item.pricePerUnit}
                    itemType="new"
                    displayPrices={displayPrices}
                  />
                ) : (
                  <RestaurantItemRow
                    itemDetails={item}
                    onAdd={() => onAdd(item)}
                    onRemove={() => onRemove(item)}
                    quantity={newQty}
                    amount={newQty * item.pricePerUnit}
                    itemType="new"
                    onAddonsUpdate={onAddonsUpdate}
                    strikeOffAmt={item.strikeOffAmount}
                  />
                )}
                {index < availableItems.length - 1 && (
                  <div className="border-b border-neutral-200" />
                )}
              </React.Fragment>
            );
          })}
        </div>
      )}

      {/* Order note section */}
      {showNoteFeature && onOpenNotePopup && (
        <div className="border-t border-neutral-200 pt-2.5">
          <OrderNote note={orderNote} onOpenNotePopup={onOpenNotePopup} />
        </div>
      )}

      {/* <div className="flex justify-between pt-3 border-t border-neutral-200">
        <span className="font-medium text-sm text-primary-500">
          New Order Total
        </span>
        <span className="font-semibold text-sm text-primary-500">
          ₹ {roundOff(totalNewOrderAmount, true)}
        </span>
      </div> */}
    </div>
  );
};
