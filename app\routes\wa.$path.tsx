// routes/wa/$path.tsx

import { LoaderFunction } from "@remix-run/node";
import { Outlet, useMatches } from "@remix-run/react";
import { useEffect } from "react";

// Loader function to dynamically fetch existing route data
export const loader: LoaderFunction = async (loaderArgs) => {
  const { path } = loaderArgs.params;

  // Call the appropriate loader for the existing route
  // Here, you need to map "path" to the corresponding existing route
  if (path === "login") {
    // Import or call the loader from your existing home route
    const homeLoader = await import("./login");
    return homeLoader.loader(loaderArgs);
  } else if (path === "help") {
    // Import or call the loader from your existing help route
    const helpLoader = await import("./help");
    return helpLoader.loader(loaderArgs);
  } else {
    throw new Response("Not Found", { status: 404 });
  }
};

export default function WaDynamicRoute() {
  const matches = useMatches();
  useEffect(() => {
    console.log("Matched routes:", matches);
  }, [matches]);

  return (
    <div>
      <Outlet />
    </div>
  );
}
