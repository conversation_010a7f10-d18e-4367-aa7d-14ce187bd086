import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import path from "path";

export default defineConfig({
  plugins: [
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true
      }
    }),
    tsconfigPaths()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./app"),
      "@components": path.resolve(__dirname, "./app/components"),
      "@styles": path.resolve(__dirname, "./app/styles"),
      "@utils": path.resolve(__dirname, "./app/utils"),
      "@routes": path.resolve(__dirname, "./app/routes"),
      "@stores": path.resolve(__dirname, "./app/stores")
    }
  },
  build: {
    // sourcemap: true, // Enable source maps in production build if needed
  },
  server: {
    // sourcemap: true, // Enable source maps in development server
  }
});
