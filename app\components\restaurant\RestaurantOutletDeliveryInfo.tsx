import { Button } from "@headlessui/react";
import React from "react";
import { useNavigate } from "@remix-run/react";
import { AddressDto } from "~/types/address.types";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import { ChevronDown, MapPin, Store, UserRound, Clock } from "lucide-react";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { SellerInfo } from "~/types";
import { capitalizeSentence, getLastSegment } from "~/utils/string";

interface RestaurantOutletDeliveryInfoProps {
  sellerList: SellerInfo[];
  estDeliveryTime: number;
  defaultAddress: AddressDto | undefined;
  onSellerClick: () => void;
  onAddressClick?: () => void;
  onProfileClick?: () => void;
}

const RestaurantOutletDeliveryInfo: React.FC<
  RestaurantOutletDeliveryInfoProps
> = ({
  sellerList,
  estDeliveryTime,
  defaultAddress,
  onSellerClick,
  onAddressClick,
  onProfileClick
}) => {
  const navigate = useNavigate();
  const { itemOptionsData } = chooseitemsStore((state) => state);
  const { appDomain, networkConfig } = useAppConfigStore((state) => state);

  // Find current seller from the list
  const currentSeller = sellerList.find(
    (seller) => seller.id === itemOptionsData?.sellerId
  );
  const currentSellerName =
    currentSeller?.name || itemOptionsData?.sellerName || "Select Outlet";

  const handleAddressClick = () => {
    if (onAddressClick) {
      onAddressClick();
    } else {
      if (appDomain === "RET11") {
        navigate(`/changeaddress?redirectTo=/home/<USER>/rsrp`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      } else {
        navigate(`/changeaddress?redirectTo=/chooseitems`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      }
    }
  };

  return (
    <>
      <div className="flex flex-row justify-between p-3 pt-4 pb-1">
        <div className="flex flex-col gap-3 flex-1 min-w-0">
          <Button
            className="flex flex-row items-center gap-1 pr-5 text-white w-full"
            onClick={handleAddressClick}
          >
            <MapPin size={24} className="text-white shrink-0" />
            {defaultAddress?.address ? (
              <div className="flex items-center overflow-hidden min-w-0">
                {defaultAddress?.name && (
                  <span className="font-semibold whitespace-nowrap">
                    {defaultAddress.name.toUpperCase().slice(0, 8)}
                    {defaultAddress.name.length > 8 ? "..." : ""}&nbsp;-&nbsp;
                  </span>
                )}
                <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                  {defaultAddress?.address}
                </span>
              </div>
            ) : (
              <div className="flex items-center overflow-hidden min-w-0 mr-2">
                <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                  Add a delivery address
                </span>
              </div>
            )}
            <ChevronDown className="w-5 h-5 text-white shrink-0" />
          </Button>

          <div className="flex items-center gap-4 text-white w-full pl-1">
            <div className="flex items-center gap-2">
              <Clock size={18} className="text-white shrink-0" />
              <span className="font-semibold">
                {estDeliveryTime ? `${estDeliveryTime} mins` : "30 - 45 mins"}
              </span>
            </div>
            <Button
              className="flex items-center gap-2 text-white min-w-0 flex-1"
              onClick={() => {
                if (networkConfig?.multiSeller) {
                  onSellerClick();
                }
              }}
            >
              <div className="w-1 h-1 bg-white rounded-full opacity-60" />
              <Store size={18} className="text-white shrink-0" />
              <div className="flex flex-row items-center gap-1 overflow-hidden min-w-0 flex-1">
                <div className="overflow-hidden text-ellipsis whitespace-nowrap">
                  {capitalizeSentence(getLastSegment(currentSellerName))}
                </div>
                {networkConfig?.multiSeller && (
                  <ChevronDown className="w-4 h-4 text-white shrink-0" />
                )}
              </div>
            </Button>
          </div>
        </div>

        <Button
          onClick={onProfileClick}
          className="rounded-full bg-black bg-opacity-30 p-3 h-fit my-auto ml-4 shrink-0"
        >
          <UserRound size={20} fill="#FFF" strokeWidth={0} />
        </Button>
      </div>
    </>
  );
};

export default RestaurantOutletDeliveryInfo;
