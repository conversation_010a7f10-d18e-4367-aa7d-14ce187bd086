// context/GlobalStateContext.tsx

// example for create an complex global state context

import React, {
  createContext,
  useContext,
  useReducer,
  use<PERSON>emo,
  Dispatch
} from "react";
// import { DecodedToken } from "~/types/user";

// Define User interface
interface User {
  // id: string;
  // name: string;
  // mobileNumber: string;
  // tokenDecode?: DecodedToken;
  // user?: User;
  existingGroupOrderId?: string;
}

// Define Settings interface
interface Settings {
  theme: "light" | "dark";
  language: string;
}

// Define the shape of your global state
interface State {
  user: User | null;
  settings: Settings;
}

// Define Action types
type Action =
  | { type: "SET_USER"; payload: User }
  | { type: "SET_EXISTING_GROUP_ORDER_ID"; payload: string }
  | { type: "UPDATE_SETTINGS"; payload: Partial<Settings> }
  | { type: "LOGOUT" };

// Define the context value type
interface GlobalStateContextType {
  state: State;
  dispatch: Dispatch<Action>;
}

// Initial state
const initialState: State = {
  user: null,
  settings: {
    theme: "light",
    language: "dark"
  }
};

// Reducer function
function reducer(state: State, action: Action): State {
  switch (action.type) {
    case "SET_USER":
      return { ...state, user: action.payload };
    case "SET_EXISTING_GROUP_ORDER_ID": {
      return {
        ...state,
        user: { ...state.user, existingGroupOrderId: action.payload }
      };
    }
    case "UPDATE_SETTINGS":
      return {
        ...state,
        settings: { ...state.settings, ...action.payload }
      };
    case "LOGOUT":
      return { ...initialState, settings: state.settings };
    default:
      return state;
  }
}

// Create context
const GlobalStateContext = createContext<GlobalStateContextType | undefined>(
  undefined
);

// Provider component
export const GlobalStateProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  // Memoize the context value to optimize performance
  const contextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <GlobalStateContext.Provider value={contextValue}>
      {children}
    </GlobalStateContext.Provider>
  );
};

// Custom hook to use the GlobalStateContext
export const useGlobalState = (): GlobalStateContextType => {
  const context = useContext(GlobalStateContext);
  if (!context) {
    throw new Error("useGlobalState must be used within a GlobalStateProvider");
  }
  return context;
};
