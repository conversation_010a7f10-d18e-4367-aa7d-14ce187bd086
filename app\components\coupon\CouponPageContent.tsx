import { SearchIcon, XCircleIcon } from "lucide-react";
import { BackNavHeader } from "../BackNavHeader";
import { CouponList } from "./CouponList";
import { CouponDetailModal } from "./CouponDetailModal";
import { useNavigate } from "@remix-run/react";
import { useCouponStore } from "~/stores/coupon.store";
import { CouponDTO } from "~/types/coupon.types";

export default function CouponPageContent({
  handleApplyCoupon,
  handleBack
}: {
  handleApplyCoupon: (coupon: CouponDTO) => void;
  handleBack: () => void;
}) {
  const { selectedCoupon, applyCoupon, searchText, setSearchText } =
    useCouponStore();
  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    if (!selectedCoupon) {
      e.preventDefault();
      return;
    }

    handleApplyCoupon(selectedCoupon);
    navigate("/r/cart?coupon=true", {
      state: {
        appliedCoupon: selectedCoupon,
        showSuccessModal: true
      },
      replace: true
    });
  };

  // const isSubmitting = fetcher.state === "submitting";
  const isSubmitting = false;
  const handleBackClick = () => {
    if (handleBack) {
      handleBack();
    } else {
      navigate("/r/cart");
    }
  };

  return (
    <div className="flex flex-col h-screen min-h-screen bg-[#F2F4F9]">
      <div className="sticky top-0 z-10 bg-[#F7FFFD]">
        <BackNavHeader
          buttonText="Coupons"
          handleBack={handleBackClick}
          pageName="SRP"
          className="!min-h-12 shadow-none"
        />
        <div aria-labelledby="coupons-search" className="px-4 pb-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <SearchIcon className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Have a coupon code? Type here"
              className="w-full pl-10 pr-10 py-2 bg-white border border-neutral-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              value={searchText}
              onChange={handleChange}
            />
            {searchText && (
              <div className="absolute inset-y-0 right-3 flex items-center">
                <button
                  onClick={() => setSearchText("")}
                  className="text-gray-400 hover:text-gray-600"
                  aria-label="Clear search"
                >
                  <XCircleIcon className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex-1 px-3 pt-2 pb-36 overflow-y-auto no-scrollbar">
        <CouponList />
      </div>

      <div
        className={`fixed bottom-0 left-0 right-0 px-5 py-4 ${
          selectedCoupon
            ? "bg-[linear-gradient(360deg,_#FFFFFF_37.6%,_#89A5E3_100%)]"
            : "bg-white"
        } border-t border-gray-200 rounded-t-lg`}
      >
        {selectedCoupon && (
          <>
            <div className="text-typography-800 text-sm font-medium">
              🥳 Coupon selected by you
            </div>
            <div className="mt-1 mb-2 p-2 rounded-md bg-white flex flex-row items-center gap-2">
              <img
                src="/coupon-icon.svg"
                alt="coupon"
                className="w-4 h-4 min-w-4 min-h-4"
              />
              <p className="text-typography-600 text-xs font-semibold">
                Save ₹{selectedCoupon?.discountValue.toFixed(0)} with this ‘
                {selectedCoupon?.code}’
              </p>
            </div>
          </>
        )}
        <button
          type="button"
          onClick={handleSubmit}
          disabled={!selectedCoupon || isSubmitting}
          className={`w-full py-2 px-6 text-white font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50 ${
            selectedCoupon && !isSubmitting
              ? "bg-teal-500 hover:bg-teal-600 focus:ring-teal-500"
              : "bg-gray-300 cursor-not-allowed"
          }`}
        >
          {isSubmitting ? "Applying..." : "Apply Coupon"}
        </button>
      </div>

      <CouponDetailModal />
    </div>
  );
}
