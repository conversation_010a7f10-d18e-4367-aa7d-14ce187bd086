// app/routes/home.account.tsx

import React from "react";
import { useNavigate, useLoaderData, json } from "@remix-run/react";
import { ActionFunction, LoaderFunction, redirect } from "@remix-run/node";
import { destroySession, getSession } from "~/utils/session.server";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken, Transaction, WalletDetails } from "~/types/user";
import {
  getWalletDetails,
  getWalletRecentTransactions
} from "~/services/buyer.service";
import { User } from "~/types";
import RefreshButton from "~/components/RefreshButton";
import dayjs from "dayjs";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { BackNavHeader } from "~/components/BackNavHeader";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { useRequireAuth } from "~/hooks/useRequireAuth";
export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token || !user) {
    return redirect("/login");
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      return redirect("/login");
    }

    const walletDetailsResponse = await getWalletDetails(
      user.userId,
      access_token,
      request
    );
    const walletRecentTransactionsResponse = await getWalletRecentTransactions(
      user.userId,
      access_token,
      request
    );

    return createClientResponse(
      request,
      {
        walletDetails: walletDetailsResponse.data,
        transactions: walletRecentTransactionsResponse.data || []
      },
      walletDetailsResponse
    );
  } catch (error) {
    console.error("Error decoding access_token:", error);
    const auth = await requireAuth(request, "", true);
    if (auth && auth.authRequired) {
      return json(auth);
    }
    return redirect("/login");
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }
  return redirect("/login", {
    headers: {
      "Set-Cookie": await destroySession(session)
    }
  });
};

export default function Wallet() {
  const navigate = useNavigate();
  useRequireAuth();
  const appDomain = useAppConfigStore((state) => state.appDomain);
  const loaderData = useLoaderData<{
    walletDetails: WalletDetails;
    transactions: Transaction[];
  }>();

  const walletDetails = loaderData?.walletDetails || {};
  const transactions = loaderData?.transactions || [];

  // const transactions: Partial<Transaction>[] = [
  //   {
  //     id: 1,
  //     narration: "Grocery Shopping",
  //     transactionTime: "2024-11-04T10:31:22.972Z",
  //     balance: 25000,
  //     creditValue: 0,
  //     debitValue: 500,
  //   },
  //   {
  //     id: 2,
  //     narration: "Salary Credited",
  //     transactionTime: "2024-11-03T08:15:00.000Z",
  //     balance: 25500,
  //     creditValue: 5000,
  //     debitValue: 0,
  //   },
  //   // Add more transactions as needed
  // ];
  const handleBack = () => {
    if (appDomain === "RET11") {
      navigate("/home/<USER>", { replace: true });
    } else {
      navigate(-1);
    }
  };

  return (
    <div className="h-screen">
      <BackNavHeader buttonText="My Wallet" handleBack={handleBack} />
      <div className="flex flex-col items-center bg-gray-100 px-2 py-2">
        {/* Balance Card */}
        <div className="flex flex-col px-6 py-4 bg-white shadow-md w-full rounded-lg mb-4">
          <span className="text-sm text-gray-700 font-medium mb-4 ">
            Account Balance
          </span>
          <span className="text-2xl font-semibold text-gray-700">{`₹ ${
            walletDetails.walletBalance || 0
          }`}</span>
        </div>
        {/* transactions */}
        <div className="flex flex-col w-full px-4">
          <div className="flex flex-row justify-between items-center">
            <span className="text-md font-medium text-gray-700">
              Transactions
            </span>
            <RefreshButton onClick={() => navigate(0)} />
          </div>
          <div className="flex flex-col items-center mt-8">
            {transactions && transactions.length === 0 ? (
              <span className="text-sm font-normal text-gray-700">
                No Recent Transactions
              </span>
            ) : (
              <TransactionList transactions={transactions} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

interface TransactionProps {
  narration: string;
  txnDate: string;
  balance: number;
  creditValue: number;
  debitValue: number;
}

const TransactionRow: React.FC<TransactionProps> = ({
  narration,
  txnDate,
  balance,
  creditValue,
  debitValue
}) => {
  return (
    <div className="flex justify-between py-4 border-b border-gray-300">
      <div>
        <p className="font-medium text-xs text-gray-700">{narration}</p>
        <p className="text-xs font-light text-gray-700 mt-1">
          {dayjs(txnDate).format("DD MMM | hh:mm A")}
        </p>
      </div>
      <div className="text-right">
        {creditValue > 0 ? (
          <p className="font-medium text-xs text-teal-600">
            +₹{creditValue.toLocaleString("en-IN")}
          </p>
        ) : (
          <p className="font-medium text-xs text-gray-700">
            -₹{debitValue.toLocaleString("en-IN")}
          </p>
        )}
        <p className="text-xs font-light text-gray-700 mt-1">
          Balance: ₹{balance.toLocaleString("en-IN")}
        </p>
      </div>
    </div>
  );
};

interface TransactionListProps {
  transactions: Transaction[];
}

const TransactionList: React.FC<TransactionListProps> = ({ transactions }) => {
  return (
    <div className="overflow-y-scroll  w-full">
      {transactions.map((transaction) => (
        <TransactionRow
          key={transaction.id}
          narration={transaction.narration}
          txnDate={transaction.transactionTime}
          balance={transaction.balance}
          creditValue={transaction.creditValue}
          debitValue={transaction.debitValue}
        />
      ))}
    </div>
  );
};
