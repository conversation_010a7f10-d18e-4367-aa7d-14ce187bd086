export function isValidNumber(value: string | number): boolean {
  if (value === "") {
    return true;
  }

  if (
    typeof value === "number" ||
    (typeof value === "string" && /^[+-]?\d+(\.\d+)?$/.test(value))
  ) {
    return true;
  }

  return false;
}

export const isValidPhoneNo = (phoneNumber: string) => {
  return /^\d{10}$/.test(phoneNumber);
};

export function capitalizeFirstLetter(string: string) {
  if (!string) return ""; // Handle empty or null input
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export const formatPrice = (price: number) =>
  price % 1 === 0 ? price.toFixed(0) : price.toFixed(2);

export function isEmptyNullOrUndefinedString(input: string) {
  return (
    input === null ||
    input === undefined ||
    /^(null|undefined|\s*)$/.test(input)
  );
}

export function getImagesUrlsFromString(imageUrl: string) {
  if (!imageUrl) {
    return [];
  }
  return imageUrl.split(",");
}

export function getFirstImageFromString(imageUrl: string) {
  if (!imageUrl) {
    return "";
  }
  return imageUrl.split(",")?.[0];
}

export function camelCaseToWords(text: string) {
  return text
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
}

export function capitalizeSentence(sentence: string): string {
  if (!sentence) return "";

  // Step 1: Convert the entire string to lowercase
  return sentence.toLowerCase()
    .split(' ') // Step 2: Split by whitespace
    .map(word => {
      // Step 3: Find the first alphabet and capitalize it
      let firstAlphaIdx = -1;
      for (let i = 0; i < word.length; i++) {
        if (/[a-zA-Z]/.test(word[i])) {
          firstAlphaIdx = i;
          break;
        }
      }

      // If we found an alphabetic character, capitalize it
      if (firstAlphaIdx !== -1) {
        word = word.slice(0, firstAlphaIdx) + word[firstAlphaIdx].toUpperCase() + word.slice(firstAlphaIdx + 1);
      }

      return word; // Return the modified word
    })
    .join(' '); // Step 4: Join words back into a string
}

export function getLastSegment(sentence: string) {
  // Trim any leading/trailing spaces and split by pipe character
  const parts = sentence.trim().split('|');
  
  // Return the last part (after trimming spaces)
  return parts.length > 0 ? parts[parts.length - 1].trim() : sentence;
}