import React, { useState, useMemo, useEffect } from "react";
import {
  LoaderFunction,
  json,
  redirect,
  ActionFunction
} from "@remix-run/node";
import {
  useLoaderData,
  useNavigate,
  useFetcher,
  useRouteError
} from "@remix-run/react";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);

import ItemCard from "@components/ItemCard";
import MoQPopup from "@components/MoQPopup";
import RefreshButton from "@components/RefreshButton";
import Button from "@components/Button";

import {
  getItemOptionsAPI,
  precheckAndConfirmOrderAPI
} from "@services/buyer.service";
import {
  getSession,
  destroySession,
  commitSession
} from "@utils/session.server";
import {
  User,
  ItemOptionsData,
  AvailableItem,
  PrecheckOrderPayload,
  Cart,
  ItemCategoryDtos,
  // ConfirmOrderPayload,
  ConfirmOrderResponse
} from "~/types";

// import ScrollViewWithRefresh from "@components/ScrollViewWithRefresh";
import { BackNavHeader } from "~/components/BackNavHeader";
import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken } from "~/types/user";
import { createClientResponse } from "~/utils/clientReponse";
import { useUser } from "~/contexts/userContext";
import { removeItem } from "~/utils/localStorage";
import { verifySessionToken } from "~/utils/jwt";
import SuccessDialog from "~/components/SuccessDialog";
// import usePostMessage from "~/hooks/usePostMessage";
// import { EventType } from "~/constants/Iframe";
import { IframeData } from "~/types/iframe";
import InfoModel from "~/components/models/InfoModel";
import InstallPWAButton from "~/components/InstallPWAButton";

interface LoaderData {
  itemOptionsData: ItemOptionsData;
  user: User;
  iframeConfig: IframeConfig;
}

interface IframeConfig {
  parent: string;
}

interface LoaderErrorData {
  error: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const url = new URL(request.url);
  const iframeToken = url.searchParams.get("token");
  const iframeConfig: IframeConfig = {
    parent: "bc"
  };

  if (iframeToken) {
    const sessionData = verifySessionToken(iframeToken);
    if (!sessionData) {
      return json({}, { status: 400, statusText: "Invalid session token" });
    }

    session = await getSession();

    session.set("access_token", sessionData.access_token);
    session.set("refresh_token", sessionData.refresh_token);
    session.set("iframeSessionData", sessionData.data);

    const tokenData = parseJWT(sessionData.access_token ?? "");
    const userDetails = tokenData.userDetails;

    const iframeUser: User = {
      userId: userDetails.userId,
      userName: userDetails.userName,
      businessName: userDetails.businessName,
      buyerId: userDetails.buyerId,
      buyerFromBC: true,
      mobileNumber: "9807666322"
    };

    session.set("user", iframeUser);

    return redirect("/seller/chooseitems", {
      headers: {
        "Set-Cookie": await commitSession(session)
      }
    });
  }

  if (!access_token || !user) {
    console.log("Auth token or user not found, redirecting to /login");
    return redirect("/login", {
      headers: {
        "Set-Cookie": await destroySession(session)
      }
    });
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      return json({}, { status: 400, statusText: "Invalid session token" });
    }

    const iframeSessionData = session.get("iframeSessionData") as IframeData;

    const itemOptions = await getItemOptionsAPI(
      iframeSessionData?.buyerData?.mobileNumber
        ? iframeSessionData?.buyerData?.mobileNumber
        : null,
      request,
      undefined,
      undefined
    );

    const itemOptionsData = itemOptions.data;

    if (!itemOptionsData) {
      throw json({}, { status: 400, statusText: "NO_ITEM" });
    }

    return createClientResponse<LoaderData, ItemOptionsData>(
      request,
      { itemOptionsData, user, iframeConfig },
      itemOptions
    );
  } catch (error) {
    console.error("Error fetching item options:", error);

    if (error.statusText === "NO_ITEM") {
      throw json({}, { status: 400, statusText: "NO_ITEM" });
    }
    return json({}, { status: 400, statusText: "Items are not available" });
  }
};

interface ActionData {
  success?: boolean;
  error?: string;
  data?: ConfirmOrderResponse;
}

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  if (!access_token || !user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const cartData = formData.get("cart");
  const deliveryDate = formData.get("deliveryDate") as string;
  const sellerId = formData.get("sellerId") as string;
  const isCodAllowed = formData.get("isCodAllowed") === "true";
  const sellerDataId = formData.get("sellerDataId") as string;
  const buyerId = Number(formData.get("buyerId") as string);
  const existingOrderGroupId =
    (formData.get("existingOrderGroupId") !== null &&
      Number(formData.get("existingOrderGroupId"))) ||
    undefined;

  if (!cartData || typeof cartData !== "string") {
    return json({ error: "Invalid cart data" }, { status: 400 });
  }

  let cart: Cart;
  try {
    cart = JSON.parse(cartData);
  } catch (error) {
    console.error("Error parsing cart data:", error);
    return json({ error: "Invalid cart data format" }, { status: 400 });
  }

  // Prepare items array for API
  const itemsForAPI: PrecheckOrderPayload["items"] = Object.values(cart).map(
    (cartItem) => ({
      inventoryItemId: cartItem.itemId,
      sellerId: Number(sellerId),
      sellerItemId: cartItem.itemId, // Adjust if sellerItemId is different
      pricePerUnit: cartItem.amount / cartItem.qty,
      quantity: cartItem.qty
    })
  );

  // Prepare payload
  const payload: PrecheckOrderPayload = {
    sellerInventoryId: Number(sellerDataId),
    buyerId,
    deliveryDate,
    sellerId: Number(sellerId),
    codOpted: isCodAllowed,
    items: itemsForAPI,
    legacy: true,
    moneyCollectionId: 0,
    existingOrderGroupId
  };

  try {
    const response = await precheckAndConfirmOrderAPI(payload, request);
    console.log("precheckAndConfirmOrderAPI: " + JSON.stringify(response));
    if (response.statusCode === 200) {
      return {
        success: true,
        message: "Order confirmed successfully",
        data: response.data
      };
    } else {
      return { success: false, message: "Unable to place the order" };
    }
  } catch (error) {
    console.error("Error prechecking order:", error);
    return json({ error: "Failed to confirm order" }, { status: 500 });
  }
};

const ChooseItems: React.FC = () => {
  const navigate = useNavigate();
  const { itemOptionsData, error, iframeConfig } = useLoaderData<
    LoaderData & LoaderErrorData
  >();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fetcher = useFetcher<ActionData>();

  const {
    deliveryDate,
    id,
    buyerId,
    sellerId,
    deliveryTime,
    isCodAllowed,
    minOrderQty,
    minOrderValue,
    prepayDiscountPercentage,
    existingOrderGroupId
  } = itemOptionsData;

  const [searchStr, setSearchStr] = useState("");
  const [showMoq, setShowMoq] = useState(false);
  const [showMov, setShowMov] = useState(false);
  const { user, setUser } = useUser();
  // const [placedOrder, setPlacedOrder] = useState<
  //   ConfirmOrderPayload | undefined
  // >(undefined);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  useEffect(() => {
    if (existingOrderGroupId) {
      setUser({ existingOrderGroupId });
    }
  }, [existingOrderGroupId, setUser]);

  const itemMap = new Map<number, AvailableItem>();
  itemOptionsData.availableItems.forEach((item) => {
    itemMap.set(item.id, item);
  });

  console.log("iframe code................................");

  // ------------------------------iframe-code-start----------------------------------
  // const [isIframe, setIframe] = useState(false);

  useEffect(() => {
    if (iframeConfig && iframeConfig.parent) {
      // setIframe(true);
      window.parent.postMessage(
        { type: "IFRAME_LOADED", payload: { success: true } },
        "*"
      );
    }
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      // Add event listener for messages
      const handleMessage = (event: MessageEvent) => {
        console.log("Message received:", event.data);
      };

      window.addEventListener("message", handleMessage);

      // Cleanup on unmount
      return () => {
        window.removeEventListener("message", handleMessage);
      };
    }
  }, []);

  const sendMessageToParent = () => {
    if (typeof window !== "undefined" && window.parent) {
      window.parent.postMessage(
        { type: "GREETING", data: "Hello from Child!" },
        "*"
      );
    }
  };

  sendMessageToParent();

  // ------------------------------iframe-code-end----------------------------------

  // Initialize cart from localStorage or loader data
  const [cart, setCart] = useState<Cart>(() => {
    const initialCart: Cart = {};
    itemOptionsData.availableItems?.forEach((item) => {
      if (item.orderedQty > 0) {
        initialCart[item.id] = {
          itemId: item.id,
          qty: item.orderedQty,
          amount: item.orderedQty * item.pricePerUnit
        };
      }
    });

    removeItem("cart");
    return initialCart;
  });

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("cart", JSON.stringify(cart));
    }
  }, [cart]);

  const handleMinQuantityAndPrice = () => {
    const minQty: boolean =
      Object.values(cart).reduce((acc, item) => acc + item.qty, 0) <
      minOrderQty;
    const minPrice: boolean =
      Object.values(cart).reduce((acc, item) => acc + item.amount, 0) <
      minOrderValue;

    if (minQty) {
      setShowMoq(true);
      return true;
    }
    if (minPrice) {
      setShowMov(true);
      return true;
    }

    return minQty || minPrice;
  };

  const handleRefresh = async () => {
    // clear cart
    removeItem("cart");
    setIsRefreshing(true);
    try {
      // Trigger a loader refresh by navigating to the same route
      navigate(0);
    } catch (err) {
      console.error("Error refreshing item options:", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleAddItem = (item: AvailableItem) => {
    setCart((prevCart) => {
      const currentQty = prevCart[item.id]?.qty || 0;
      const incrementOrderQty = item.incrementOrderQty || 1;
      if (currentQty >= item.maxAvailableQty) return prevCart;

      const newQty = currentQty + incrementOrderQty;
      const newAmount = newQty * item.pricePerUnit;

      return {
        ...prevCart,
        [item.id]: {
          itemId: item.id,
          qty: newQty,
          amount: newAmount
        }
      };
    });
  };

  const handleRemoveItem = (item: AvailableItem) => {
    setCart((prevCart) => {
      const currentQty = prevCart[item.id]?.qty || 0;
      const orderedQty = item.orderedQty || 0;
      const incrementOrderQty = item.incrementOrderQty || 1;
      if (currentQty <= orderedQty) return prevCart;

      const newQty = currentQty - incrementOrderQty;
      const newAmount = newQty * item.pricePerUnit;

      const updatedCart = { ...prevCart };
      if (newQty === 0) {
        delete updatedCart[item.id];
      } else {
        updatedCart[item.id] = {
          itemId: item.id,
          qty: newQty,
          amount: newAmount
        };
      }

      return updatedCart;
    });
  };

  const hasNewItems = useMemo(() => {
    return itemOptionsData.availableItems.some((item) => {
      const cartItem = cart[item.id];
      return cartItem && cartItem.qty > item.orderedQty;
    });
  }, [cart, itemOptionsData.availableItems]);

  const handleRedirect = () => {
    if (typeof window !== "undefined" && window.parent) {
      window.parent.postMessage(
        { type: "PLACE_ORDER", payload: { success: true } },
        "*"
      );
    }
    setShowSuccessDialog(false);
  };

  // Handle the fetcher response
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if ("error" in fetcher.data) {
        setErrorMessage(fetcher.data.error || "Something went wrong");
      } else {
        // Capture the placed order details before clearing
        // setPlacedOrder(fetcher.data.data);
        setShowSuccessDialog(true);
        // Clear cart
        removeItem("cart");
      }
    }
  }, [fetcher.state, fetcher.data, navigate]);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-white">
        <span className="text-red-500 text-lg">{error}</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-neutral-50 w-full overflow-auto">
      {/* Header Section */}
      <BackNavHeader
        backButton={false}
        buttonText="Add Items"
        handleBack={() => navigate("/home/<USER>")}
        rightText={
          <RefreshButton onClick={handleRefresh} loading={isRefreshing} />
        }
      /> 
      {/* Seller Information */}
      <DeliveryInfo
        deliveryDate={deliveryDate}
        deliveryTime={deliveryTime}
        isCodAllowed={isCodAllowed}
        prepayDiscountPercentage={prepayDiscountPercentage}
      />

      {/* Search */}
      <div className="px-4 py-2 shadow-lg">
        {/* Search Bar */}
        <div className="flex items-center p-1 border border-gray-300 rounded-full w-full">
          <img src="/search.png" alt="Search" className="ml-2 w-4 h-4 mr-2" />
          <input
            type="text"
            placeholder="Search Items"
            value={searchStr}
            onChange={(e) => setSearchStr(e.target.value)}
            className="flex-1 outline-none bg-white text-gray-700 font-light mr-2"
          />
        </div>
      </div>

      {/* Error message from order confirmation */}
      {errorMessage && (
        <div className="p-4 bg-red-100 text-red-700 text-center mx-4 rounded-md">
          {errorMessage}
        </div>
      )}

      {/* Item Listing */}
      <div className="overflow-auto flex-auto mb-16">
        <CategoryItemList
          cart={cart}
          data={itemOptionsData}
          onAddItem={handleAddItem}
          onRemoveItem={handleRemoveItem}
          searchStr={searchStr}
        />
      </div>

      {/* Form to handle Confirm Order via Fetcher */}
      <div className="fixed bottom-0 left-0 right-0">
        <fetcher.Form
          method="post"
          className="flex items-center justify-between bg-white shadow-lg shadow-gray-900 w-full"
          onSubmit={(event) => {
            if (handleMinQuantityAndPrice()) {
              event.preventDefault();
            }
          }}
        >
          {/* item total and total items */}
          <div className="w-1/2 text-center">
            <span className="text-sm text-gray-700">
              {`${Object.values(cart).reduce(
                (acc) => acc + 1,
                0
              )} Items | ₹ ${Object.values(cart)
                .reduce((acc, item) => acc + item.amount, 0)
                .toFixed(2)}`}
            </span>
          </div>
          {/* Hidden Inputs to Pass Necessary Data */}
          <input type="hidden" name="cart" value={JSON.stringify(cart)} />
          <input type="hidden" name="deliveryDate" value={deliveryDate} />
          <input type="hidden" name="sellerId" value={sellerId} />
          <input type="hidden" name="buyerId" value={buyerId} />
          <input type="hidden" name="sellerDataId" value={id} />
          <input
            type="hidden"
            name="isCodAllowed"
            value={isCodAllowed ? "true" : "false"}
          />
          <input
            type="hidden"
            name="existingOrderGroupId"
            value={user?.existingOrderGroupId}
          />
          {/* confirm btn */}
          <Button
            // onClick={handleMinQuantityAndPrice}

            disabled={Object.values(cart).length === 0 || !hasNewItems}
            className={`px-6 py-4 w-1/2 bg-teal-600 ${
              Object.values(cart).length === 0 || !hasNewItems
                ? "bg-gray-300 text-white cursor-not-allowed"
                : "bg-primary hover:bg-primary-700 text-white"
            }`}
          >
            {fetcher.state === "submitting" ? "CONFIRMING..." : "CONFIRM"}
          </Button>
        </fetcher.Form>
      </div>

      {/* MoQ Popup */}
      <MoQPopup
        visible={showMoq || showMov}
        onClose={() => {
          setShowMoq(false);
          setShowMov(false);
        }}
        qty={minOrderQty}
        value={minOrderValue}
        showMoq={showMoq}
        showMov={showMov}
      />

      {/* Success Dialog */}
      {showSuccessDialog && (
        <div className="p-16">
          <SuccessDialog
            title="Order Placed"
            message="Thankyou for placing the order"
            buttonText="Okay!"
            buttonType="primary"
            onRedirect={handleRedirect}
            countdownStart={5} // Set countdown to 5 seconds
          />
        </div>
      )}
    </div>
  );
};

// TODO: move components to files

interface SellerProps {
  deliveryDate: string;
  deliveryTime: string;
  isCodAllowed: boolean;
  prepayDiscountPercentage?: number;
}

const DeliveryInfo: React.FC<SellerProps> = ({
  deliveryDate,
  deliveryTime,
  isCodAllowed,
  prepayDiscountPercentage
}) => {
  return (
    <div className="flex items-center py-3 px-3 bg-[#E6F6F4]">
      <div>
        <div className="flex items-center">
          <p className="text-sm font-medium text-gray-700">
            Delivery On :{" "}
            <span className="text-sm font-normal text-teal-500">
              {dayjs(deliveryDate).format("dddd, DD MMM")}
            </span>
            <span className="text-sm font-light text-blue-500">
              {" "}
              {dayjs(`1970-01-01 ${deliveryTime}`).format("h:mm A")}
            </span>
          </p>
          {isCodAllowed ? (
            <div className="flex items-center ml-1 py-1 px-2 rounded-sm">
              <span className="text-xs font-light text-teal-500">COD</span>
            </div>
          ) : (
            <div className="flex items-center ml-1 py-1 px-2 bg-red-100 rounded-sm border border-gray-300">
              <span className="text-xs font-normal text-purple-600">
                PP{" "}
                {prepayDiscountPercentage ? `${prepayDiscountPercentage}%` : ""}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface CategoryItemProps {
  category: ItemCategoryDtos;
  isSelected: boolean;
  onSelect: () => void;
}

const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  isSelected,
  onSelect
}) => {
  return (
    <div
      tabIndex={0}
      role="button"
      onKeyDown={() => {}}
      className={`w-full h-24 my-3 ${
        isSelected ? "border-r-2 border-primary" : ""
      }`}
      onClick={onSelect}
    >
      <button className="w-full flex text-center flex-col items-center">
        <div
          className={`w-12 h-12 rounded-full ${
            isSelected ? "bg-primary-100" : "bg-gray-200"
          } relative overflow-hidden`}
        >
          <img
            src={category.picture}
            alt={category.name}
            className={`absolute w-12 h-12 mx-1 ${
              isSelected ? "translate-y-1" : "translate-y-3"
            }`}
          />
        </div>
        <p
          className={`text-center w-full mt-1 text-xs font-normal ${
            isSelected ? "text-primary" : "text-gray-600"
          }`}
        >
          {category.name}
        </p>
      </button>
    </div>
  );
};

interface CategoryListProps {
  categories: ItemCategoryDtos[];
  selectedCategoryId: number | undefined;
  onSelectCategory: (categoryId: number) => void;
}

const CategoryList: React.FC<CategoryListProps> = ({
  categories,
  selectedCategoryId,
  onSelectCategory
}) => {
  return (
    <div className="bg-white rounded-t-lg overflow-y-scroll mr-2 w-24 h-full">
      {categories.map((category) => (
        <CategoryItem
          key={category.id}
          category={category}
          isSelected={selectedCategoryId === category.id}
          onSelect={() => onSelectCategory(category.id)}
        />
      ))}
    </div>
  );
};

interface ItemListProps {
  approxPricing: boolean;
  items: AvailableItem[];
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
}

const ItemList: React.FC<ItemListProps> = ({
  approxPricing,
  items,
  cart,
  onAddItem,
  onRemoveItem
}) => {
  return (
    <div className="max-h-full">
      {items.map((item) => (
        <ItemCard
          key={item.id}
          itemDetails={item}
          qty={cart[item.id]?.qty || 0}
          amount={cart[item.id]?.amount || 0}
          approxPricing={approxPricing}
          onAdd={() => onAddItem(item)}
          onRemove={() => onRemoveItem(item)}
        />
      ))}
    </div>
  );
};

interface EmptySearchResultProps {
  searchStr: string;
}

const EmptySearchResult: React.FC<EmptySearchResultProps> = ({ searchStr }) => {
  return (
    <div className="flex flex-col items-center justify-center mt-40 mx-2">
      <p className="text-sm font-medium text-gray-800">Uh-oh!</p>
      <p className="text-sm font-light text-gray-600">
        No results for {searchStr}. Please try something else.
      </p>
    </div>
  );
};

interface CategoryItemListProps {
  data: ItemOptionsData;
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  searchStr: string;
}

const CategoryItemList: React.FC<CategoryItemListProps> = ({
  data,
  cart,
  onAddItem,
  onRemoveItem,
  searchStr
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number>(); // Stores the selected category ID
  const [filteredItems, setFilteredItems] = useState<AvailableItem[]>(
    data?.availableItems || []
  );
  const [availableItems, setAvailableItems] = useState<AvailableItem[]>(
    data?.availableItems || []
  );
  const [categories, setCategories] = useState<ItemCategoryDtos[]>(
    data?.itemCategoryDtos || []
  );

  // add all categories
  useEffect(() => {
    if (data.categoriesEnabled && data.itemCategoryDtos.length) {
      availableItems.map((item) => item?.itemCategories?.push(1));
      setAvailableItems(availableItems);
      const allCategories = categories.find((cat) => cat.id === 1);
      if (!allCategories) {
        categories.unshift({
          id: 1,
          name: "All Items",
          picture: "/all_items.svg"
        });
        setCategories(categories);
      }
      setSelectedCategoryId(1);
    }
  }, [availableItems, categories]);

  // Search items
  useEffect(() => {
    if (searchStr.length) {
      setFilteredItems(
        availableItems.filter((item) =>
          item.itemName.toLowerCase().includes(searchStr.toLowerCase())
        )
      );
      setSelectedCategoryId(1);
    }
  }, [searchStr, availableItems]);

  useEffect(() => {
    if (selectedCategoryId) {
      const filteredItems = availableItems.filter((item) =>
        item?.itemCategories?.includes(selectedCategoryId)
      );

      setFilteredItems(filteredItems);
    }
  }, [selectedCategoryId]);
  // Handler for selecting a category
  const handleSelectCategory = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  return (
    <div className="flex bg-gray-100 pt-2 px-2 h-full w-full">
      {data?.categoriesEnabled && categories.length && (
        <CategoryList
          categories={categories}
          selectedCategoryId={selectedCategoryId}
          onSelectCategory={handleSelectCategory}
        />
      )}
      <div className="overflow-y-scroll overflow-x-hidden max-h-full w-full">
        {filteredItems.length > 0 ? (
          <ItemList
            approxPricing={data.approxPricing}
            items={filteredItems}
            cart={cart}
            onAddItem={onAddItem}
            onRemoveItem={onRemoveItem}
          />
        ) : (
          <EmptySearchResult searchStr={searchStr} />
        )}
      </div>
    </div>
  );
};

export default ChooseItems;

export function ErrorBoundary() {
  const navigate = useNavigate();
  const error = useRouteError();
  console.log("error", typeof error);

  const handleInfoClosed = () => {
    window.parent.postMessage(
      { type: "PLACE_ORDER", payload: { success: true } },
      "*"
    );
  };

  if (error.statusText === "NO_ITEM") {
    console.log(error);

    if (typeof window !== "undefined") {
      window.parent.postMessage(
        { type: "IFRAME_LOADED", payload: { success: true } },
        "*"
      );
    }

    return (
      <InfoModel
        title="Sorry!"
        message="Booking close now, Please try after sometime"
        buttonType="primary"
        buttonText="Go Back"
        onRedirect={handleInfoClosed}
      />
    );
  }

  if (typeof window !== "undefined") {
    window.parent.postMessage(
      {
        type: "IFRAME_ERROR",
        payload: { success: false, message: "Unable to load Iframe" }
      },
      "*"
    );
  }

  // usePostMessage<EventType, CommonMessage>(
  //   {
  //     type: "IFRAME_ERROR",
  //     payload: { success: false, message: "Unable to load Iframe" }
  //   },
  //   "*"
  // );

  return (
    <ErrorBoundaryComponent
      title="Hey there!"
      onClose={() => navigate("/seller/chooseitems")}
    />
  );
}
