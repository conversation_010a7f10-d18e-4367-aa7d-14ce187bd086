import React from "react";
export function UpdateAddressModel({
  setSaveAddressModelClicked,
  saveAddressModelClicked,
  autoCompleteAddress,
  address,
  // e,
  setAddress,
  actionData,
  // errors,
}) {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
      <div className="mb-6 bg-white p-2 rounded-t-lg p-6">
        {/* header */}
        <div className="flex justify-between items-center mb-2">
          <label htmlFor="address" className="block text-md text-gray-700 mb-2">
            Enter complete Address
          </label>
          <button
            onClick={() => setSaveAddressModelClicked(!saveAddressModelClicked)}
            className="border rounded-full w-6 h-6  items-center border-teal-600 text-gray-500"
          >
            x
          </button>
        </div>
        {/* line */}
        <div className="h-[1px] shadow-md bg-gray-300 mt-2 mb-2"></div>
        {/* Auto complete address */}
        <div className="mt-4 mb-4">
          <div className="flex justify-between items-center gap-2 w-full border p-2 rounded-lg">
            <span className="text-xs text-gray-500 mr-4 w-36 overflow-hidden ">
              {autoCompleteAddress}
            </span>
            <button
              onClick={() =>
                setSaveAddressModelClicked(!saveAddressModelClicked)
              }
              className="px-3 py-1 text-teal-600 border border-teal-600 rounded-lg text-xs hover:bg-green-100 focus:outline-none"
            >
              Change
            </button>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Updated based on your exact map pin
          </p>
        </div>
        {/* Auto complete address */}
        <fieldset className="border border-gray-300 rounded-lg focus-within:border-teal-600 focus-within:border-2">
          <legend className="text-xs ml-4">Complete address:</legend>
          <textarea
            name="address"
            id="address"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            className={`w-full px-4 py-2 border ${
              actionData?.errors?.address ? "border-red-500" : "border-gray-300"
            } rounded-md focus:outline-none bg-white border-0 text-sm text-gray-700`}
            required
          />
        </fieldset>
        {actionData?.errors?.address && (
          <p className="text-red-500 text-sm mt-1">
            {actionData.errors.address}
          </p>
        )}
        {/* Submit Button */}
        <div className="flex justify-end mt-6">
          <Button
            type="submit"
            className={`px-4 py-2  bg-teal-600 text-white rounded-md  bg-teal-600 w-full`}
          >
            Save Address
          </Button>
        </div>
      </div>
    </div>
  );
}
