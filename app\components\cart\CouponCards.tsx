import { ChevronRight } from "lucide-react";
import { CouponDTO } from "~/types/coupon.types";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";

export const ApplyCouponCard = ({ onClick }: { onClick?: () => void }) => {
  return (
    <div
      className="flex flex-row justify-center items-center p-[10px_12px] gap-2 w-full h-[45px] bg-white shadow-[0px_2px_16px_rgba(0,0,0,0.1)] rounded-xl"
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick?.();
        }
      }}
      role="button"
      tabIndex={0}
    >
      <div className="relative w-[18px] h-[18px] flex-none">
        <img src="/coupon-icon.svg" alt="" />
      </div>
      <span className="font-nunito font-semibold text-sm leading-[19px] tracking-[0.25px] text-[#222A33] flex-grow">
        Apply Coupon
      </span>
      <ChevronRight className="w-4 h-4 text-[#1F2A37] flex-none" />
    </div>
  );
};

export const AppliedCouponCard = ({
  coupon,
  onRemove
}: {
  coupon: CouponDTO;
  onRemove?: () => void;
}) => {
  return (
    <div className="flex flex-row justify-end items-center p-[10px_12px] gap-2 w-full min-h-[45px] bg-white shadow-[0px_2px_16px_rgba(0,0,0,0.1)] rounded-xl">
      <div className="flex justify-center items-center w-4 h-4 bg-[#00A38F] rounded-full flex-none">
        <svg
          width="10"
          height="10"
          viewBox="0 0 10 10"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.33334 2.5L3.75001 7.08333L1.66667 5"
            stroke="white"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>

      <div className="flex flex-col justify-center items-start gap-0 flex-grow">
        <span className="font-nunito font-semibold text-[13px] tracking-[0.25px] text-[#222A33] w-full">
          You saved ₹{coupon.discountValue} with &apos;{coupon.code}&apos;
        </span>

        {/* <div className="flex flex-row justify-end items-center gap-1">
          <Link
            to="/coupons"
            className="font-nunito font-semibold text-[10px] leading-[14px] text-right text-[#64717D]"
          >
            View all coupons
          </Link>
          <svg
            width="10"
            height="10"
            viewBox="0 0 10 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="2.188"
              y="1.886"
              width="5.624"
              height="6.228"
              fill="#DFF2EF"
              stroke="#00A38F"
              strokeWidth="1"
              strokeLinecap="round"
              strokeLinejoin="round"
              rx="1.5"
            />
          </svg>
        </div> */}
      </div>

      {onRemove && (
        <button
          onClick={() => {
            onRemove();
            console.log("onRemove");
          }}
          className="font-nunito font-normal text-[10px] leading-[14px] tracking-[0.25px] text-[#DB3532] flex-none"
        >
          Remove
        </button>
      )}
    </div>
  );
};

export const CouponAppliedBanner = ({
  discountValue,
  visible = true
}: {
  discountValue: number;
  visible?: boolean;
}) => {
  if (!visible) return null;

  return (
    <div className="flex flex-row items-center justify-start bg-blue-100 p-2 rounded-lg shadow-lg mt-2 mx-2">
      <DotLottieReact
        src="https://lottie.host/3825b43f-1cf3-4e2e-b0e2-a2645ea78952/Yb4eerTdoH.lottie"
        loop
        autoplay
        className="w-12"
      />
      <p className="text-xs font-bold text-blue-600 text-start">
        You saved ₹{discountValue} with this order
      </p>
    </div>
  );
};
