import { NetworkAsset } from "../components/NetworkAssests";
// app/routes/home.orders.tsx

import React, { useState, useMemo, useEffect } from "react";
import {
  ActionFunction,
  LoaderFunction,
  json,
  redirect
} from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import {
  getOrdersAPI,
  getUpiPaymentStatus,
  initiatePayment
} from "~/services/buyer.service";
import {
  getSession,
  destroySession,
  commitSession
} from "~/utils/session.server";
import { InitiatePaymentResponse, OrderResponse, User } from "~/types";
import OrderCard from "~/components/OrderCard";
import Button from "~/components/Button";
import { parseJWT } from "~/utils/token-utils";
import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { createClientResponse } from "~/utils/clientReponse";
import { DecodedToken } from "~/types/user";

interface LoaderData {
  orders: OrderResponse;
  user: User;
  mobileNumber?: string;
}

interface LoaderErrorData {
  error: string;
}

interface ActionData {
  success?: boolean;
  message?: string;
  errors?: {
    [key: string]: string;
  };
  paymentUrl?: string;
  refId?: number;
}

const processInitiatePayment = async (
  request: Request,
  formData: FormData,
  access_token: string
) => {
  const decoded = parseJWT(access_token) as DecodedToken; // Adjust type as per your DecodedToken
  const buyerId = decoded.userDetails.buyerId;
  const amount = parseFloat(formData.get("amount") as string);
  const orderGroupId = parseInt(formData.get("orderGroupId") as string, 10);
  const note = `Initiating Upi payment for Rs ${amount}`;

  const errors: { [key: string]: string } = {};
  if (!buyerId) errors.buyerId = "buyerId is required";
  if (!amount) errors.amount = "amount is required";
  if (!orderGroupId) errors.orderGroupId = "orderGroupId is required";

  if (Object.keys(errors).length > 0) {
    return json<ActionData>({ errors }, { status: 400 });
  }

  const response = await initiatePayment(
    orderGroupId,
    {
      initiatedByUserId: buyerId,
      amount,
      note
    },
    request
  );
  // console.log("Payment response: " + JSON.stringify(response, null, 2));
  if (response) {
    // const paymentUrl = `upi://pay?pa=${encodeURIComponent(
    //   response.pa
    // )}&pn=${encodeURIComponent(response.pn)}&tr=${encodeURIComponent(
    //   response.tr
    // )}&am=${encodeURIComponent(response.am)}&cu=${encodeURIComponent(
    //   response.cu
    // )}&mc=${encodeURIComponent(response.mc)}`;

    // console.log("upiString: ", paymentUrl);
    return createClientResponse<ActionData, InitiatePaymentResponse>(
      request,
      {
        success: true,
        message: "Payment initiated successfully",
        paymentUrl: response.data.queryString,
        refId: response.data.refId
      },
      response
    );
  } else {
    return json<ActionData>({
      success: false,
      message: "Failed to initiate payment"
    });
  }
};

const processPaymentStatus = async (request: Request, formData: FormData) => {
  console.log("refId: ", formData.get("refId"));
  const refId = formData.get("refId") as string;
  if (!refId) {
    return json<ActionData>(
      { errors: { refId: "refId is required" } },
      { status: 400 }
    );
  }

  const response = await getUpiPaymentStatus(parseInt(refId, 10), request);

  if (response.data?.status === "PAID") {
    return createClientResponse(
      request,
      { paymentStatus: "SUCCESS" },
      response
    );
  } else if (
    response.data?.status === "INITIATED" ||
    response.data?.status === "PENDING"
  ) {
    return createClientResponse(
      request,
      { paymentStatus: "PENDING" },
      response
    );
  } else {
    return createClientResponse(request, { paymentStatus: "FAILED" }, response);
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  if (!access_token) {
    return redirect("/login");
  }

  const formData = await request.formData();
  const requestName = formData.get("requestName");

  if (!requestName) {
    return json<ActionData>(
      { errors: { requestName: "requestName is required" } },
      { status: 400 }
    );
  }

  try {
    if (requestName === "InitiatePayment") {
      return processInitiatePayment(request, formData, access_token);
    } else if (requestName === "PaymentStatus") {
      return processPaymentStatus(request, formData);
    } else {
      return json<ActionData>(
        { errors: { requestName: "Invalid requestName" } },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.error("Error Order payment:", error);
    if (error instanceof Error) {
      return json<ActionData>(
        { message: error.message || "Failed to Process payment" },
        { status: 500 }
      );
    }
  }
};

export const loader: LoaderFunction = async ({ request }) => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user = session.get("user") as User | null;

  const url = new URL(request.url);
  const appSource = url.searchParams.get("source");

  if (!access_token || !user) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appSource, appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/home/<USER>
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      const headers = new Headers();
      headers.append("Set-Cookie", await destroySession(session));
      session = await getSession();
      session.set("appConfig", { appSource, appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return redirect(`/home/<USER>
    }
    const response = await getOrdersAPI(user.buyerId, request);

    return createClientResponse<LoaderData, OrderResponse>(
      request,
      {
        orders: {
          pendingOrders: (response.data?.completedOrders || []).sort(
            (a, b) => b.id - a.id
          ),
          completedOrders: (response.data?.pendingOrders || []).sort(
            (a, b) => b.id - a.id
          )
        },
        user,
        mobileNumber: decoded.userDetails.mobileNumber
      },
      response
    );
  } catch (error) {
    console.error("Error fetching orders:", error);
    throw json<LoaderErrorData>(
      { error: "Failed to fetch orders" },
      { status: 500 }
    );
  }
};

const OrdersPage: React.FC = () => {
  const { orders, error } = useLoaderData<LoaderData & LoaderErrorData>();
  const navigate = useNavigate();

  // const location = useLocation();

  const [chosenFilter, setChosenFilter] = useState<"pending" | "completed">(
    "pending"
  );
  const [expandedOrderId, setExpandedOrderId] = useState<number | undefined>(
    undefined
  );
  // const [isRefreshing, setIsRefreshing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (error) setErrorMessage(error);
  }, [error]);

  const handleRefresh = async () => {
    // setIsRefreshing(true);
    navigate(0);
    // setIsRefreshing(false);
  };

  const filteredOrders = useMemo(
    () =>
      chosenFilter === "pending"
        ? orders.pendingOrders
        : orders.completedOrders,
    [chosenFilter, orders]
  );

  return (
    <div className="fixed flex flex-col h-screen bg-gray-100 w-full">
      {/* Header */}
      <div className="flex justify-between items-center p-4 bg-white shadow-md p-2">
        <h1 className="text-lg font-semibold text-gray-700">Orders</h1>
        <Button
          className="text-xs bg-teal-600 text-white p-1 rounded-md"
          onClick={handleRefresh}
        >
          REFRESH
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex border-b bg-white">
        <button
          className={`flex-1 py-4 px-2 text-center ${
            chosenFilter === "pending"
              ? "border-b-2 border-teal-600 text-teal-600 bg-teal-50"
              : "text-gray-600"
          }`}
          onClick={() => setChosenFilter("pending")}
        >
          Pending Orders
        </button>
        <button
          className={`flex-1 py-4 px-2 text-center ${
            chosenFilter === "completed"
              ? "border-b-2 border-teal-600 text-teal-600 bg-teal-50"
              : "text-gray-600"
          }`}
          onClick={() => setChosenFilter("completed")}
        >
          Completed Orders
        </button>
      </div>

      {/* Error Message */}
      {errorMessage && (
        <div className="p-4 bg-red-100 text-red-700 text-center">
          {errorMessage}
        </div>
      )}

      {/* Orders List */}
      <div className="overflow-y-auto p-4">
        {filteredOrders && filteredOrders.length > 0 ? (
          filteredOrders.map((order) => (
            <OrderCard
              key={order.id}
              orderDetails={order}
              expanded={expandedOrderId === order.id}
              onPress={() =>
                setExpandedOrderId((prev) =>
                  prev === order.id ? undefined : order.id
                )
              }
            />
          ))
        ) : (
          <p className="text-center text-gray-500">No orders to display.</p>
        )}

        {/* Footer Image */}
        <div className="self-end w-20 h-15 mt-20 mb-20 mx-auto">
          <NetworkAsset assetName="footer" />
        </div>
      </div>
      {/* {appSource === "whatsappchat" && (
        <WhatsappCTA mobileNumber={mobileNumber} />
      )} */}
    </div>
  );
};

export default OrdersPage;

export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(0)} />;
}
