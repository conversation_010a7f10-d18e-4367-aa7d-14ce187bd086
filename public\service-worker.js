// This is a basic service worker that doesn't do much without HTTPS

// public/service-worker.js
self.addEventListener("install", () => {
  console.log("Service Worker installed");
  self.skipWaiting();
});
self.addEventListener("activate", () => {
  console.log("Service Worker activated");
  self.clients.claim();
});
self.addEventListener("fetch", () => {
  // (you can add caching logic here later)
});
