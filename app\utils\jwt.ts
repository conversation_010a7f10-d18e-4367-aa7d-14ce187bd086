import jwt from "jsonwebtoken";
import { IframeData } from "~/types";

export function isTokenExpired(token: string) {
  try {
    // Decode the token without verifying the signature
    const decoded = jwt.decode(token);
    // console.log("decoded token123", decoded);
    if (!decoded || !decoded.exp) {
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const bufferTime = 3600;

    // Check if the token is expired
    return decoded.exp < currentTime + bufferTime - 1800;
  } catch (error) {
    // If token decoding fails, treat it as expired
    return true;
  }
}

const JWT_SECRET = "process.env.JWT_SECRET";
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

export function verifySessionToken(token: string): {
  access_token: string;
  refresh_token: string;
  data: IframeData;
} | null {
  try {
    return jwt.verify(token, JWT_SECRET) as {
      access_token: string;
      refresh_token: string;
      data: IframeData;
    };
  } catch {
    return null;
  }
}
