import React, { useEffect, useState } from "react";
import { Transition } from "@headlessui/react";
import { AlertCircle, CheckCircle, Info, X, AlertTriangle, <PERSON>ie, Apple, Refrigerator } from "lucide-react";

type ToastPosition =
  | "top-left"
  | "top-right"
  | "top-center"
  | "bottom-left"
  | "bottom-right"
  | "bottom-center";

interface ToastProps {
  message: string;
  type?: "success" | "error" | "warning" | "info"|"itemLimited";
  duration?: number;
  onClose?: () => void;
  position?: ToastPosition;
  showIcon?: boolean;
  showCloseButton?: boolean;
  className?: string;
  width?: "auto" | "full";
  autoClose?: boolean;
}

const Toast: React.FC<ToastProps> = ({
  message,
  type = "info",
  duration = 3000,
  onClose,
  position = "bottom-center",
  showIcon = true,
  showCloseButton = true,
  className = "",
  width = "auto",
  autoClose = true
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    if (autoClose && duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onClose) {
          onClose();
        }
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onClose, autoClose]);

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) {
      onClose();
    }
  };

  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircle className="w-5 h-5" />;
      case "error":
        return <AlertCircle className="w-5 h-5" />;
      case "warning":
        return <AlertTriangle className="w-5 h-5" />;
      case "itemLimited":
        return <Refrigerator className="h-full w-auto text-black fill-primary-300 stroke-[1px]" />
      default:
        return <Info className="w-5 h-5" />;
    }
  };

  const getToastStyles = () => {
    const baseStyles = "flex gap-2 items-center p-3 rounded-lg shadow-lg ";
    switch (type) {
      case "success":
        return baseStyles + "bg-primary-50 text-primary border border-primary-200";
      case "error":
        return baseStyles + "bg-secondary-50 text-secondary border border-secondary-200";
      case "warning":
        return (
          baseStyles + "bg-yellow-50 text-yellow-600 border border-yellow-200"
        );
      case "itemLimited": 
      return (
        baseStyles + "bg-black bg-opacity-75 text-white"
      );
      default:
        return (
          baseStyles + "bg-primary-50 text-primary border border-primary-200"
        );
    }
  };

  const getPositionStyles = () => {
    const base = "fixed transform z-50 ";
    switch (position) {
      case "top-left":
        return base + "top-4 left-4";
      case "top-right":
        return base + "top-4 right-4";
      case "top-center":
        return base + "top-4 left-1/2 -translate-x-1/2";
      case "bottom-left":
        return base + "bottom-20 left-4";
      case "bottom-right":
        return base + "bottom-20 right-4";
      case "bottom-center":
        return base + "bottom-20 left-1/2 -translate-x-1/2";
      default:
        return base + "bottom-20 left-1/2 -translate-x-1/2";
    }
  };

  return (
    <Transition
      show={isVisible}
      enter="transition-all duration-300"
      enterFrom="opacity-0 translate-y-2"
      enterTo="opacity-100 translate-y-0"
      leave="transition-all duration-200"
      leaveFrom="opacity-100 translate-y-0"
      leaveTo="opacity-0 translate-y-2"
    >
      <div
        className={`${getPositionStyles()} ${
          width === "full" ? "w-[calc(100%-2rem)]" : "max-w-sm"
        }`}
      >
        <div className={`${getToastStyles()} ${className} w-full`}>
          {showIcon && <span className="flex-shrink-0 h-full w-auto">{getIcon()}</span>}
          <p className="text-sm font-medium flex-grow">{message}</p>
          {showCloseButton && (
            <button
              onClick={handleClose}
              className="ml-4 flex-shrink-0 hover:opacity-75 transition-opacity"
              aria-label="Close toast"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>
    </Transition>
  );
};

export default Toast;
