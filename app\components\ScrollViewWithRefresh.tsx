// app/components/ScrollViewWithRefresh.tsx

import React from "react";

interface ScrollViewWithRefreshProps {
  children: React.ReactNode;
  refreshing: boolean;
  onRefresh: () => void;
}

const ScrollViewWithRefresh: React.FC<ScrollViewWithRefreshProps> = ({
  children,
  refreshing,
  onRefresh,
}) => {
  return <div className="max-h-full overflow-y-auto">{children}</div>;
};

export default ScrollViewWithRefresh;
