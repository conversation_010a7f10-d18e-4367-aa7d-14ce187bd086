// app/routes/postlogin.tsx

import { LoaderFunction, redirect, json } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { getSession } from '~/utils/session.server'; // Ensure this path is correct
import type { User } from '~/types';

// Define the shape of the loader data
interface LoaderData {
    access_token: string;
    user: User;
}

// Loader Function: Fetches access_token and user from the session
export const loader: LoaderFunction = async ({ request }) => {
    // Retrieve the session from the request's cookies
    const session = await getSession(request.headers.get('Cookie'));

    // Extract access_token and user from the session
    const access_token = session.get('access_token');
    const user: User | null = session.get('user');

    // If access_token or user is missing, redirect to the login page
    if (!access_token || !user) {
        return redirect('/login');
    }

    // Return the access_token and user data as JSO<PERSON>
    return json<LoaderData>({ access_token: access_token, user });
};

// PostLogin Component: Displays user information
export default function PostLogin() {
    // Access the data returned by the loader
    const { access_token, user } = useLoaderData<LoaderData>();

    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
            <div className="bg-white p-8 rounded shadow-md text-center">
                <h1 className="text-2xl font-bold mb-4">Welcome, {user.userName}!</h1>
                <p className="text-gray-700 mb-2">
                    <strong>Business Name:</strong> {user.businessName}
                </p>
                <p className="text-gray-700 mb-2">
                    <strong>Buyer ID:</strong> {user.buyerId}
                </p>
                <p className="text-gray-700">
                    <strong>Authorized with token:</strong> {access_token}
                </p>
            </div>
        </div>
    );
}
