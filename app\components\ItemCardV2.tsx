import React, { useCallback, useState } from "react";
import { AvailableItem, Cart } from "~/types";
import Button from "./Button";
import ItemDetails from "./chooseitem/ItemDetails";
import Badge from "./chooseitem/Badge";
import AddItemButtonV2 from "./chooseitem/AddItemButtonV2";
import { capitalizeFirstLetter, formatPrice } from "~/utils/string";
import BottomSheet from "./BottmSheet";
import VariantButton from "./chooseitem/VariantButton";
import { ItemsVariantList } from "./ItemsVariantList";
import CustomImage from "./CustomImage";
import { chooseitemsStore } from "~/stores/chooseitems.store";

const GetBadge: React.FC<{ itemDetails: AvailableItem }> = ({
  itemDetails
}) => {
  if (itemDetails.closed || itemDetails.soldout) {
    return (
      <Badge
        color="orange"
        text={`${itemDetails.closed ? "Closed" : "Sold Out"}`}
        className="absolute right-0 top-0 rounded-tr-md bg-secondary-50 text-secondary-500 shadow-sm"
      />
    );
  }

  if (itemDetails.discPerc > 0) {
    return (
      <Badge
        color="blue"
        text={`${itemDetails.discPerc}% OFF`}
        className="absolute text-white right-0 top-0 rounded-tr-md bg-gradient-to-r from-[#48A2D7] to-[#0065A3] shadow"
      />
    );
  }

  if (itemDetails.newlyAdded) {
    return (
      <Badge
        color="orange"
        text="Newly Added"
        className="absolute text-white right-0 top-0 rounded-tr-md bg-gradient-to-r from-[#F8961E] to-[#E76C18] shadow"
      />
    );
  }
};

interface ItemCardProps {
  itemDetailsList: AvailableItem[];
  amount: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  cart: Cart;
}

const ItemCard: React.FC<ItemCardProps> = ({
  itemDetailsList,
  amount,
  cart,
  onAdd,
  onRemove,
  approxPricing
}) => {
  const [showItemDetails, setShowItemDetails] = useState(false);
  const [showVariants, setShowVariants] = useState(false);
  const displayPrices = chooseitemsStore((state) => state.itemOptionsData?.displayPrices);

  const sortedItems = useCallback(
    () =>
      [...itemDetailsList]
        .sort((a, b) => a.varSeq - b.varSeq)
        .sort((a, b) => {
          if ((a.soldout || a.closed) && !(b.soldout || b.closed)) return 1;
          if (!(a.soldout || a.closed) && (b.soldout || b.closed)) return -1;
          return a.varSeq - b.varSeq;
        }),
    [itemDetailsList]
  )();

  const itemDetails = useCallback(() => {
    const item = [...itemDetailsList]
      .sort((a, b) => a.varSeq - b.varSeq)
      .find((item) => !(item.soldout || item.closed));

    return item || itemDetailsList[0];
  }, [itemDetailsList])();

  const qty = useCallback(() => {
    return itemDetailsList.reduce(
      (acc, it) => acc + (cart[it.sellerItemId]?.qty || 0),
      0
    );
  }, [{ ...cart }])();

  return (
    <div
      className={`relative flex flex-col justify-between bg-white  shadow-md shadow-gray-200 px-1 py-1 rounded-xl`}
    >
      <div
      // className={`${
      //   itemDetails.closed || itemDetails.soldout ? "filter grayscale" : ""
      // }`}
      >
        <ItemInfo
          itemDetails={itemDetails}
          showItemDetails={showItemDetails}
          setShowItemDetails={setShowItemDetails}
          qty={qty}
          onAdd={onAdd}
          onRemove={onRemove}
          approxPricing={approxPricing}
          displayPrices={displayPrices}
          variantCount={itemDetailsList?.length || 0}
          setShowVariants={setShowVariants}
        />
      </div>

      {/* current order calculations */}
      {/* 
      {amount > 0 && (
        <div className="py-2 px-2 rounded-b-xl flex justify-between items-center bg-neutral-100  border-t border-dashed border-neutral-600 transition-all duration-1000 ease-in-out">
          <span className="text-[.7rem] text-gray-700 font">{itemDetails.unit === "unit" ? `${qty} x ₹${itemDetails.pricePerUnit}` : `${qty} ${itemDetails.unit} x ₹${itemDetails.pricePerUnit} /${itemDetails.unit}`}</span>
          <span className="text text-[.7rem] font-semibold text-gray-700">
            {amount > 0 ? `₹ ${formatPrice(amount)}` : ""} */}
      {/* {!approxPricing && (
            <span className="text-[.6rem] text-gray-500">(approx)</span>
          )} */}
      {/*  </span>
        </div>
      )} */}

      {/* Show Badges */}
      {itemDetails.closed || itemDetails.soldout ? (
        <div className="absolute inset-0 bg-white bg-opacity-60 rounded-lg ">
          <div className="relative">
            <GetBadge itemDetails={itemDetails} />
          </div>
        </div>
      ) : (
        <GetBadge itemDetails={itemDetails} />
      )}
      <ItemsVariantList
        showVariants={showVariants}
        setShowVariants={setShowVariants}
        variants={sortedItems}
        cart={cart}
        onAdd={onAdd}
        onRemove={onRemove}
        displayPrices={displayPrices}
      />
      <BottomSheet
        isOpen={showItemDetails}
        onClose={() => setShowItemDetails(false)}
        className=" bg-gray-100 p-2"
      >
        <ItemDetails
          items={sortedItems}
          cart={cart}
          onAdd={onAdd}
          onRemove={onRemove}
          approxPricing={approxPricing}
          displayPrices={displayPrices}
          setShowItemDetails={setShowItemDetails}
        />
      </BottomSheet>
    </div>
  );
};

// Component for displaying item image, name, and badges
const ItemInfo: React.FC<{
  itemDetails: AvailableItem;
  showItemDetails: boolean;
  setShowItemDetails: (value: boolean) => void;
  qty: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  displayPrices?: boolean;
  variantCount: number;
  setShowVariants: (show: boolean) => void;
}> = ({
  itemDetails,
  showItemDetails,
  setShowItemDetails,
  qty,
  onAdd,
  onRemove,
  approxPricing,
  displayPrices,
  variantCount,
  setShowVariants
}) => (
  <div className="">
    <div className="relative ">
      <div className="relative">
        <Button
          className="w-full h-full flex item-center"
          onClick={() => setShowItemDetails(!showItemDetails)}
        >
          <CustomImage
            src={itemDetails?.itemUrl}
            alt={""}
            className={`w-full aspect-square	object-cover rounded-xl bg-neutral-100 border border-neutral-300`}
          />
        </Button>
        <div className="absolute right-0 -bottom-1">
          {/* <AddItemButtonV2
            qty={qty}
            onAdd={onAdd}
            onRemove={onRemove}
            isDisabled={itemDetails.closed || itemDetails.soldout}
            unit={itemDetails.unit}
          /> */}
          {variantCount > 1 ? (
            <VariantButton
              qty={qty}
              variantCount={variantCount}
              isDisabled={itemDetails.closed || itemDetails.soldout}
              onClick={() => setShowVariants(true)}
              version={2}
            />
          ) : (
            <AddItemButtonV2
              qty={qty}
              onAdd={() => {
                onAdd(itemDetails);
              }}
              onRemove={() => onRemove(itemDetails)}
              isDisabled={itemDetails.closed || itemDetails.soldout}
              unit={itemDetails.unit}
              btnConfig={{
                showUnit: false
              }}
            />
          )}
        </div>
      </div>
      <div className="flex flex-col items-start p-2">
        {itemDetails.packaging && (
          <p className="text-[.5rem] text-gray-600 mt-1 bg-gray-100 py-1 px-2 rounded-md mb-2 border font-semibold tracking-wider">
            {itemDetails.packaging}
          </p>
        )}
        <h2 className="text-xs font-medium text-gray-700 line-clamp-2 ">
          {itemDetails.itemName}
        </h2>
        {/* <p className="text-[.65rem] text-gray-500 mt-1">
          {itemDetails.itemRegionalLanguageName}
        </p> */}
      </div>
      { displayPrices !== false && <div className="flex flex-col flex-wrap pl-2 pb-2 items-start">
        <div className="flex">
          {itemDetails.discPerc > 0 && (
            <span className="text-[.6rem] font-normal text-gray-500 line-through">
              {itemDetails.unit === "unit"
                ? `₹${itemDetails.strikeoffPrice}`
                : `₹${itemDetails.strikeoffPrice}/${itemDetails.unit}`}
            </span>
          )}
          {itemDetails.discPerc > 0 && (
            <span className="text-[.6rem] font-semibold text-blue-600 ml-1">{`${itemDetails.discPerc}% OFF`}</span>
          )}
        </div>
        <div className="flex items-center">
          <span className="text-[.7rem] font-bold text-gray-700 tracking-wider">
            {itemDetails.unit === "unit"
              ? `₹${itemDetails.pricePerUnit}`
              : `₹${itemDetails.pricePerUnit}/${itemDetails.unit}`}
          </span>
          {approxPricing && (
            <span className="text-[.6rem] text-gray-500 ml-1">(approx)</span>
          )}
        </div>
      </div>}
    </div>
  </div>
);

export default ItemCard;
