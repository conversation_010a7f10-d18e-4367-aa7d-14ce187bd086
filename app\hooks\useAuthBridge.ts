import { useEffect } from "react";
import { useLocation, useFetcher } from "@remix-run/react";
import { useLoginStore } from "~/stores/login.store";

interface PendingFormEntry {
  key: string;
  value: FormDataEntryValue;
}

/**
 * Hook to automatically open login modal when a response contains the auth required header
 * Works with the server-side requireAuth utility
 */
export function useAuthBridge() {
  const location = useLocation();
  const { openLogin } = useLoginStore();
  const fetcher = useFetcher();

  // Listen for responses with auth required
  useEffect(() => {
    // Create a response observer to detect auth requirements
    const responseObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === "childList") {
          // Check all fetcher responses for auth header
          const responses = document.querySelectorAll("[data-remix-response]");
          responses.forEach((response) => {
            try {
              const data = JSON.parse(response.textContent || "{}");

              // If the response has the auth required flag, open login modal
              if (data?.authRequired === true) {
                openLogin();
              }
            } catch (error) {
              // Ignore JSON parse errors
            }
          });
        }
      }
    });

    // Start observing the document for auth responses
    responseObserver.observe(document.body, { childList: true, subtree: true });

    return () => {
      responseObserver.disconnect();
    };
  }, [openLogin]);

  // Handle form resubmission after login success
  useEffect(() => {
    const handleStorageChange = () => {
      const loginSuccessFlag = localStorage.getItem("loginSuccess");

      if (loginSuccessFlag === "true") {
        // Clear the success flag immediately
        localStorage.removeItem("loginSuccess");

        // Check for stored form data in session storage
        const pendingFormDataString = sessionStorage.getItem("pendingFormData");
        const pendingFormMethod = sessionStorage.getItem("pendingFormMethod");
        const pendingFormAction = sessionStorage.getItem("pendingFormAction");

        if (pendingFormDataString && pendingFormMethod && pendingFormAction) {
          try {
            // Parse the stored form data
            const pendingFormData: PendingFormEntry[] = JSON.parse(
              pendingFormDataString
            );

            if (pendingFormData.length > 0) {
              // Create a new FormData object
              const formData = new FormData();

              // Populate the FormData with the stored entries
              pendingFormData.forEach((entry) => {
                formData.append(entry.key, entry.value);
              });

              // Resubmit the form
              fetcher.submit(formData, {
                method: pendingFormMethod as
                  | "get"
                  | "post"
                  | "put"
                  | "delete"
                  | "patch",
                action: pendingFormAction
              });

              // Clear the stored form data
              sessionStorage.removeItem("pendingFormData");
              sessionStorage.removeItem("pendingFormMethod");
              sessionStorage.removeItem("pendingFormAction");
            }
          } catch (error) {
            console.error("Error resubmitting form:", error);
          }
        }
      }
    };

    // Add event listener for storage changes
    window.addEventListener("storage", handleStorageChange);

    // Also check on mount in case the login just happened
    handleStorageChange();

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [fetcher]);

  return { location };
}
