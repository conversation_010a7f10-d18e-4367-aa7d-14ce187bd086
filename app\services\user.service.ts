import { ApiResponse } from "~/types/Api";
import { BuyerDetails, UserProfileDetails } from "~/types/user";
import { apiRequest } from "~/utils/api";
import { getDomainFromRequest } from "~/utils/domain";

const API_BASE_URL = process.env.API_BASE_URL;

export async function getUserDetails(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _request: Request
): Promise<ApiResponse<UserProfileDetails>> {
  const { domain } = getDomainFromRequest(_request);

  const url = API_BASE_URL + `/buyer/d/${domain}/sellerrole`;

  try {
    const response = await apiRequest<UserProfileDetails>(
      url,
      "GET",
      null,
      {},
      true,
      _request
    );
    if (response) {
      return response;
    }
    throw new Error("No response received from API");
  } catch (error) {
    console.error("Error fetching user details:", error);
    throw error;
  }
}

export async function updateUserDetails(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _request: Request,
  payload: Partial<UserProfileDetails>
): Promise<ApiResponse<UserProfileDetails>> {
  console.log(payload, "llllllllll");
  const { domain } = getDomainFromRequest(_request);
  const url = API_BASE_URL + `/buyer/d/${domain}/updateuserdetails`;

  try {
    const response = await apiRequest<UserProfileDetails>(
      url,
      "PUT",
      payload,
      {},
      true,
      _request
    );

    if (response.statusCode !== 200) {
      throw new Error("No response received from API");
    }

    return response;
  } catch (error) {
    console.error("Error updating user details:", error);
    throw error;
  }
}

export async function getBuyerUsers(
  request: Request
): Promise<ApiResponse<BuyerDetails[]>> {
  const url = API_BASE_URL + `/buyer/users`;

  try {
    const response = await apiRequest<BuyerDetails[]>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Get Buyer's user api failed");
    }
  } catch (error) {
    console.error("Error Get Buyer's user:", error);
    throw error;
  }
}

export async function createBuyer(
  request: Request,
  payload: {
    firstName: string;
    mobileNumber: string;
  }
): Promise<ApiResponse<BuyerDetails>> {
  const url = API_BASE_URL + `/buyer/user/create`;

  try {
    const response = await apiRequest<BuyerDetails>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Create buyer's user api failed");
    }
  } catch (error) {
    console.error("Error create buyer's user api:", error);
    throw error;
  }
}

export async function toggleSubBuyerStatus(
  request: Request,
  subBuyerId: number
): Promise<ApiResponse<BuyerDetails>> {
  const url = API_BASE_URL + `/buyer/user/${subBuyerId}/toggle-status`;

  try {
    const response = await apiRequest<BuyerDetails>(
      url,
      "PUT",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Create buyer's user api failed");
    }
  } catch (error) {
    console.error("Error create buyer's user api:", error);
    throw error;
  }
}
