import { Phone } from "lucide-react";
import { FC } from "react";
import { Order } from "~/types";
import TruncatedText from "../TruncatedText";
import { useNavigate } from "@remix-run/react";
import SecondaryButton from "../SecondaryButton";
interface OrderAdditionalDetailsProps {
  order: Order;
}

const OrderAdditionalDetails: FC<OrderAdditionalDetailsProps> = ({ order }) => {
  const navigate = useNavigate();
  return (
    <div className="bg-white rounded-lg p-4 space-y-3">
      <div className="text-gray-500 text-sm">Order Details</div>
      {/* Order Details Section */}
      <div className="space-y-2">
        <div className="flex flex-col gap-1">
          <span className="text-gray-500 text-xs">Order id</span>
          <span className="font-normal text-sm">{order.id}</span>
        </div>

        <div className="flex flex-col gap-1">
          <span className="text-gray-500 text-xs">Payment</span>
          <span className="font-normal text-sm">
            {order.balanceTobePaid > 0 ? "Payment Pending" : "Paid"}
          </span>
        </div>

        <div className="flex flex-col gap-1">
          <span className="text-gray-500 text-xs">Deliver to</span>
          <span className="font-normal text-sm">{order.buyerAddress}</span>
        </div>

        <div className="flex flex-col gap-1">
          <span className="text-gray-500 text-xs">Order Placed</span>
          <span className="font-normal text-sm">
            {`placed on ${new Date(order.createdOn).toLocaleString("en-US", {
              weekday: "short",
              day: "numeric",
              month: "short",
              hour: "numeric",
              minute: "numeric",
              hour12: true
            })}`}
          </span>
        </div>
      </div>

      <div className="pt-4">
        <button
          onClick={() =>
            window.open(`tel:${order.farmers[0]?.sellerContactNumber}`, "_self")
          }
          className="w-full flex items-center justify-center gap-2 text-teal-500 py-2 border border-gray-300 rounded-lg px-2"
        >
          <Phone className="w-5 h-5 text-teal-600" />
          <TruncatedText
            text={`Call ${order.sellerName}`}
            className="max-w-80"
          />
        </button>
      </div>
      {/* <SecondaryButton
        className="px-2 w-full  text-md h-12"
        onClick={() =>
          navigate(`/help?action=create&orderId=${order.id}`, {
            state: {
              orderId: order.id
            }
          })
        }
      >
        Help with order
      </SecondaryButton> */}
    </div>
  );
};

export default OrderAdditionalDetails;
