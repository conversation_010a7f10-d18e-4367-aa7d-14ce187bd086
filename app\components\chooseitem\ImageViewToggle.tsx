import Button from "~/components/Button";
import { LayoutGrid, List } from "lucide-react";
import { ImageViewType } from "~/types";

interface ImageViewToggleProps {
  imageViewType: ImageViewType;
  onViewChange: (view: ImageViewType) => void;
}

export const ImageViewToggle = ({
  imageViewType,
  onViewChange
}: ImageViewToggleProps) => {
  return (
    <div className="relative flex flex-row items-center gap-2 rounded-3xl bg-neutral-200 h-full">
      <Button
        className="z-10 rounded-full py-2 px-4"
        onClick={() => onViewChange("GRID")}
      >
        <LayoutGrid size={18} />
      </Button>

      <Button
        className="z-10 rounded-full py-2 px-4"
        onClick={() => onViewChange("LIST")}
      >
        <List size={18} />
      </Button>

      {/* Sliding indicator */}
      <div
        className={`z-0 absolute top-1 bottom-1 left-1 right-[2px] w-[calc(50%-4px)] rounded-full bg-white transition-transform duration-300 ease-in-out z-[0] ${
          imageViewType === "GALLERY" || imageViewType === "LIST"
            ? "translate-x-full"
            : "translate-x-0"
        }`}
      />
    </div>
  );
};
