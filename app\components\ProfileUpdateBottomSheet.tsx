import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { Form, useSubmit } from "@remix-run/react";
import BottomSheet from "./BottmSheet";
import { cn } from "~/utils/cn";

interface ProfileUpdateBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    name: string;
    phone?: string;
    email: string;
    profileImage?: string;
  };
}

const ProfileUpdateBottomSheet: React.FC<ProfileUpdateBottomSheetProps> = ({
  isOpen,
  onClose,
  userData
}) => {
  const [name, setName] = useState(userData.name || "");
  const [phone, setPhone] = useState(userData.phone || "");
  const [email, setEmail] = useState(userData.email || "");
  const [imageError, setImageError] = useState(false);
  const [isDisabled, setDisabled] = useState(false);
  const submit = useSubmit();

  const handleClearName = () => {
    setName("");
  };
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("intent", "update");
    formData.append("businessName", name);
    formData.append("email", email);

    submit(formData, { method: "post", action: "/home/<USER>" });
    onClose();
  };

  useEffect(() => {
    // Enable button if name is not empty AND either name or email has changed from original values
    const nameIsValid = name.trim() !== "";
    const nameHasChanged = name !== (userData.name || "");
    const emailHasChanged = email !== (userData.email || "");
    const hasChanges = nameHasChanged || emailHasChanged;

    setDisabled(!nameIsValid || !hasChanges);
  }, [name, email, userData.name, userData.email]);

  return (
    <BottomSheet isOpen={isOpen} onClose={onClose} className="bg-neutral-100" swipeIndicatorClassName="bg-neutral-100 before:bg-gray-300" sheetType="drawer">
      <div className="flex flex-col items-center justify-center gap-2 pt-3 pb-0 px-3 relative bg-neutral-100 rounded-xl">
        <div className="self-start mb-14 w-full">
          <div className="font-normal text-typography-400 text-sm mb-1">
            Edit Profile
          </div>
          <div className="relative w-full h-px mb-[-0.50px] bg-gray-200" />
        </div>

        <div className="flex flex-col items-center justify-center gap-3 pt-10 pb-2 px-2 relative self-stretch w-full flex-[0_0_auto] bg-white rounded-2xl">
          {/* Profile Image */}
          <div className="absolute w-24 h-24 top-[-53px] left-1/2 transform -translate-x-1/2">
            <div className="p-2 bg-white rounded-full">
              {userData.profileImage && !imageError ? (
                <img
                  className="w-20 h-20 rounded-full object-cover"
                  alt="Profile"
                  src={userData.profileImage}
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center text-white text-3xl font-bold shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                  {name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
          </div>

          <Form method="post" onSubmit={handleSubmit} className="w-full">
            {/* Name field */}
            <div className="inline-flex flex-col items-start gap-2.5 pt-2 pb-0 px-0 relative flex-[0_0_auto] w-full">
              <div className="inline-flex flex-col items-center justify-center gap-px p-2 relative flex-[0_0_auto] rounded-lg border border-solid border-gray-300 w-full">
                <div className="flex items-center justify-center gap-2 relative self-stretch w-full flex-[0_0_auto]">
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="relative flex-1 mt-[-1.00px] font-normal text-gray-700 text-sm tracking-[0] leading-[normal] bg-transparent border-none outline-none w-full"
                    required
                  />

                  {name && (
                    <button
                      type="button"
                      onClick={handleClearName}
                      className="focus:outline-none"
                    >
                      <X className="w-3 h-3 text-gray-400" />
                    </button>
                  )}
                </div>

                <div className="inline-flex items-center justify-center gap-2.5 px-1 py-0.5 absolute top-[-9px] left-2 bg-white">
                  <div className="relative w-fit mt-[-1.00px] font-normal text-gray-700 text-[10px] tracking-[0] leading-[normal]">
                    Name *
                  </div>
                </div>
              </div>
            </div>

            {/* Phone Number field */}
            <div className="inline-flex flex-col items-start gap-2.5 relative flex-[0_0_auto] w-full mt-3">
              <div className="inline-flex flex-col items-center justify-center gap-px p-2 relative flex-[0_0_auto] rounded-lg border border-solid border-gray-300 w-full">
                <div className="flex items-center justify-center gap-2 relative self-stretch w-full flex-[0_0_auto]">
                  <input
                    type="tel"
                    value={phone}
                    disabled={true}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="Phone Number"
                    className="relative flex-1 mt-[-1.00px] font-light text-gray-500 text-xs tracking-[0] leading-[normal] bg-transparent border-none outline-none w-full"
                  />
                </div>
              </div>
            </div>

            {/* Email field */}
            <div className="inline-flex flex-col items-start gap-2.5 relative flex-[0_0_auto] w-full mt-3">
              <div className="inline-flex flex-col items-center justify-center gap-px p-2 relative flex-[0_0_auto] rounded-lg border border-solid border-gray-300 w-full">
                <div className="flex items-center justify-center gap-2 relative self-stretch w-full flex-[0_0_auto]">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Email"
                    className="relative flex-1 mt-[-1.00px] font-light text-gray-500 text-xs tracking-[0] leading-[normal] bg-transparent border-none outline-none w-full"
                  />
                </div>
              </div>
            </div>
          </Form>
        </div>

        <div
          className={cn(
            "flex flex-col w-full items-center justify-center gap-3 px-5 py-3 relative flex-[0_0_auto] bg-white rounded-[8px_8px_0px_0px]"
          )}
        >
          <button
            onClick={handleSubmit}
            className={`flex h-[35px] items-center justify-center gap-2.5 p-2 relative w-full ${
              isDisabled ? "bg-gray-400" : "bg-[#00a38f]"
            } rounded-lg shadow-[-1px_1px_10px_#05052226] text-shadow-[-1px_1px_10px_#ffde6926] font-bold text-white text-sm text-center tracking-[0.14px] leading-[normal]`}
            disabled={isDisabled}
          >
            Update Profile
          </button>
        </div>
      </div>
    </BottomSheet>
  );
};

export default ProfileUpdateBottomSheet;
