// app/components/SelectSellerCard.tsx

import React from "react";
import { SellerOption } from "~/types";
import Button from "@components/Button";

interface SelectSellerCardProps {
  seller: SellerOption;
  onSelect: () => void;
}

const SelectSellerCard: React.FC<SelectSellerCardProps> = ({
  seller,
  onSelect
}) => {
  return (
    <div className="border p-4 rounded-lg shadow mb-4 flex justify-between items-center">
      <div>
        <h2 className="text-md text-gray-700 font-semibold">
          {seller.sellerName}
        </h2>
        <p className="text-xs text-gray-600">
          Delivery Time: {seller.deliveryTime}
        </p>
        {/* <p className="text-xs text-gray-600">Rating: {seller.rating}</p>
        <p className="text-xs text-gray-600">
          Available Items: {seller.availableItems.length}
        </p> */}
      </div>
      <Button
        onClick={onSelect}
        className="bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded"
      >
        Select
      </Button>
    </div>
  );
};

export default SelectSellerCard;
