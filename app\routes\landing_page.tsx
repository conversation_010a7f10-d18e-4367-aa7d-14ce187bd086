import React, { useEffect, useState } from "react";
import Landing_page_data, { StoreData } from "../stores/Landing_page_data";
import parse from 'html-react-parser';


export default function LandingPage() {
  const [store, setStore] = useState({
    name: "",
    logo: "",
    googleMapsPin: "",
    reachUsUrl: "",
    location: "",
    documentName: "",
    gstNumber: "",
    aboutUs: "",
    whyUs:"",
    theme:"",
  })

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;

      // Find store data based on hostname
      const storeInfo = Landing_page_data[hostname] || {
        name: "SLV Provision Store",
        logo: "https://ik.imagekit.io/u7uktwxu0/Business%20Icons/SLV.png",
        googleMapsPin: "https://maps.app.goo.gl/Utj9kgDrxFUXYEEc6",
        reachUsUrl: "https://slv.mnetlive.com/home/<USER>",
        location: "#88, SLV PROVISION & VEGETABLES, Thirumala Settyhalli Cross, Chikka Thirupathi, Road, Thirumalashettyhally, Bengaluru, Karnataka 560067",
        documentName:"Udyam Aadhar",
        gstNumber: "29ECFPK2875E1ZD",
        aboutUs: "SLV Provision Store is your neighborhood's trusted destination for all daily essentials. From fresh produce to household supplies, we offer a wide range of quality products at affordable prices.",
        whyUs:`<ul className="list list-inside text-left text-gray-600 space-y-3">
          <li><span className="font-semibold">🍎 &nbsp; Fresh Fruits & Vegetables: </span> Handpicked daily to ensure top quality for your meals.</li>
          <li><span className="font-semibold">🛒 &nbsp; Groceries & Staples: </span>  A comprehensive range of essentials to stock your kitchen.</li>
          <li><span className="font-semibold">🏠 &nbsp; Household Supplies:</span>  Everything you need to keep your home running smoothly.</li>
          <li><span className="font-semibold">🌟 &nbsp; Trusted Brands: </span> Offering products from the most reliable and loved brands.</li>
          <li><span className="font-semibold">💸 &nbsp; Affordable Pricing: </span> High-quality products at prices that fit your budget.</li>
          <li><span className="font-semibold">😊 &nbsp; Friendly Service: </span> A warm and welcoming shopping experience every time.</li>
          <li><span className="font-semibold">📍 &nbsp; Convenient Location: </span> Easy to find and accessible for your shopping needs.</li>
          </ul>`,
        theme:"#DFF2EF"
      };

      setStore(storeInfo);
    }
  }, []);



  return (
    
    <div className="min-h-screen min-w-screen bg-white flex items-center justify-center p-8">
      <div 
      className="w-full h-full shadow-lg max-w-4xl rounded-lg text-center">
        <div style={{
        backgroundImage:
          `radial-gradient(50% 50% at 50% 50%, #FFF 80%, ${store.theme} 100%)`,
        padding : "2rem",
        height: '100%',
        display: "flex",
        alignItems: "center", // Corrected property name
        justifyContent: "center",
      }}>
        <div className="max-w-2xl flex flex-col gap-6 items-center self-center">
        <img
          src={store.logo}
          alt={store.name}
          className="w-32 h-32 mx-auto mb-6"
        />
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Welcome to {store.name}
        </h1>
        <div className="flex flex-col">
        <h2 className="text-xl font-semibold text-gray-700 mb-2">
            About Us
          </h2>
        <p className="text-gray-600 mb-6">
          {store.aboutUs}
          {/* SLV Provision Store is your neighborhood's trusted destination for all
          daily essentials. From fresh produce to household supplies, we offer
          a wide range of quality products at affordable prices. */}
        </p>
        </div>
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-700 mb-2">
            Why Choose Us?
          </h2>
          {parse(store.whyUs)}
        </div>
        
        <div className="flex flex-col items-center justify-center gap-4 text-gray-700 text-sm">
        <a
          href={store.googleMapsPin}
          target="_blank"
          rel="noopener noreferrer"
          className=" inline-block bg-green-500 text-white px-6 py-3 rounded-lg shadow hover:bg-green-600 transition mb-6"
        >
          Find Us on Google Maps
        </a>
        <ul className="list list-inside text-left text-gray-600 space-y-3">
          <li>
          <div className="flex gap-2">
          <p className="w-fit whitespace-nowrap">📍 &nbsp; Location:</p>
          <p className="text-wrap text-start">{store.location}</p>
          </div>
          </li>
          <li>
          <div className="flex gap-2">
          <p className="w-fit whitespace-nowrap">📜 &nbsp; {store.documentName}:</p>
          <p className="text-wrap text-start">{store.gstNumber}</p>
          </div>
          </li>
          </ul>
          <p className="text-gray-500">
            Visit us to explore a wide variety of groceries, fresh produce, and
            household supplies. We are conveniently located for your daily
            shopping needs.
          </p>
          <a
          href={store.reachUsUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="text-complementary-600 border-b-2 border-complementary-600 border-dashed"
        >
          Try our online platform
        </a>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}