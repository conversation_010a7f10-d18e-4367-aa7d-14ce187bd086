import { useAppConfigStore } from "~/stores/appConfig.store";
import { useCartStore } from "~/stores/cart.store";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import type { ItemOptionsData, PrecheckOrderPayload } from "~/types";

/**
 * Synchronizes cart page after user login by:
 * 1. Calling getItemOptionsAPI to get new cart key for real user
 * 2. If on cart page, calling precheck API and navigating with response
 */
export async function syncCartPageAfterLogin(
  currentPath: string,
  navigate: (path: string, options?: any) => void
) {
  const { cart: existingCart } = useCartStore.getState();
  const { networkConfig } = useAppConfigStore.getState();
  const { itemOptionsData: existingItemOptionsData } = chooseitemsStore.getState();

  if (!currentPath.includes('/r/cart')) {
    throw new Error('Not on cart page');
  }

  try {
    // Call getItemOptionsAPI
    let params = new URLSearchParams();
    if (networkConfig?.multiSeller && networkConfig?.ondcDomain === "RET11") {
      params.append('sellerId', existingItemOptionsData?.sellerId.toString() || "");
      params.append('deliveryDate', existingItemOptionsData?.deliveryDate || "");
    }
    const response = await fetch(`/api/get-item-options?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to get item options');
    }

    const data = await response.json();
    const itemOptionsData: ItemOptionsData = data.itemOptionsData;
    const newCartKey = itemOptionsData.cartKey;

    if (!newCartKey) {
      throw new Error('No cart key received from item options API');
    }

    // Determine final cart: use existing cart if it has items, otherwise empty
    let finalCart;
    if (Object.keys(existingCart).length > 0) {
      finalCart = Object.fromEntries(
        Object.entries(existingCart).map(([key, value]) => [
          key,
          { ...value, cartKey: newCartKey }
        ])
      );
    } else {
      finalCart = {};
    }

    // If cart is empty return
    if (Object.keys(finalCart).length === 0) {
      throw new Error('Cart is empty');
    }

    // Prepare items array for API
    const itemsForAPI: PrecheckOrderPayload["items"] = Object.values(finalCart).map(
      (cartItem) => ({
        inventoryId: itemOptionsData.inventoryId,
        sellerId: itemOptionsData.sellerId,
        sellerItemId: cartItem.itemId,
        pricePerUnit: cartItem.amount / cartItem.qty,
        quantity: cartItem.qty,
        variationId: cartItem.variationId,
        addOnItems: cartItem.flatAddons
          ? cartItem.flatAddons.map((addon) => ({
            id: addon.id,
            sId: addon.sId,
            name: addon.name,
            price: addon.price,
            qty: addon.qty,
            seq: addon.seq || 0,
            diet: addon.diet || null
          }))
          : []
      })
    );

    // Prepare payload
    let precheckPayload: PrecheckOrderPayload = {
      sellerInventoryId: itemOptionsData.inventoryId,
      buyerId: 0,
      deliveryDate: itemOptionsData.deliveryDate,
      sellerId: itemOptionsData.sellerId,
      codOpted: itemOptionsData.codAllowed,
      items: itemsForAPI,
      legacy: true,
      moneyCollectionId: 0,
      existingOrderGroupId: itemOptionsData.existingOrderGroupId,
      cartKey: newCartKey
    };

    if (itemOptionsData.takeAwayEnabled) {
      precheckPayload.fulfillmentType = "TAKE_AWAY";
    } else {
      precheckPayload.fulfillmentType = "DELIVERY";
    }

    // Call precheck API
    const precheckResponse = await fetch('/api/precheck-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(precheckPayload),
      credentials: 'include',
    });

    if (!precheckResponse.ok) {
      throw new Error('Failed to precheck order');
    }

    const precheckData = await precheckResponse.json();

    // navigate to the same path with the precheck response
    navigate(`/r/cart?approxPricing=${itemOptionsData?.approxPricing}`, {
      state: {
        order: precheckData
      }
    });
  } catch (error) {
    navigate(currentPath);
  }
}

