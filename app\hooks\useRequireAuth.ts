import { useEffect } from "react";
import { useLoaderData } from "@remix-run/react";
import { useLoginStore } from "~/stores/login.store";

/**
 * Custom hook to check if authentication is required and open login modal if needed
 * @returns The authRequired value from loader data
 */
export const useRequireAuth = () => {
  const { authRequired } = useLoaderData<{
    authRequired: boolean;
  }>();
  const { openLogin } = useLoginStore();

  useEffect(() => {
    if (authRequired) {
      openLogin();
    }
  }, [authRequired, openLogin]);

  return { authRequired };
};
