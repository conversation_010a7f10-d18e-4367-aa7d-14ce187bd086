// Facebook Conversion API Types and Interfaces
// Based on Meta's Conversion API documentation for Business Messaging


/****************** Server Side ******************/

/**
 * Facebook Conversion API Payload
 * Complete payload structure for API requests
 */
export interface FacebookConversionApiPayload {
  /** Array of events to send */
  data: FacebookConversionEvent[];
  /** Partner agent identifier */
  partner_agent?: string;
  /** Test event code for testing */
  test_event_code?: string;
}


/**
 * Facebook Conversion API Response
 */
export interface FacebookConversionApiResponse {
  /** Number of events received */
  events_received?: number;
  /** Number of events processed */
  events_processed?: number;
  /** Messages about the request */
  messages?: string[];
  /** Facebook trace ID for debugging */
  fbtrace_id?: string;
}


/**
 * Facebook Conversion API Error Response
 */
export interface FacebookConversionApiError {
  /** Error message */
  message: string;
  /** Error type */
  type: string;
  /** Error code */
  code: number;
  /** Error subcode */
  error_subcode?: number;
  /** Facebook trace ID for debugging */
  fbtrace_id?: string;
}


/**
 * Facebook Conversion API Event
 * Represents a single event to be sent to Facebook
 */
export interface FacebookConversionEvent {
  /** Name of the event */
  event_name: FacebookEventName;
  /** Unix timestamp of when the event occurred */
  event_time: number;
  /** Source of the action */
  action_source: FacebookActionSource;
  /** Messaging channel */
  messaging_channel: FacebookMessagingChannel;
  /** URL where the event occurred */
  event_source_url?: string;
  /** User data for attribution */
  user_data: FacebookUserData;
  /** Custom event data */
  custom_data?: FacebookCustomData;
  /** Unique event ID to prevent duplicates */
  event_id?: string;
  /** Opt out flag */
  opt_out?: boolean;
}


/**
 * Standard Facebook Conversion API event names
 */
export type FacebookEventName = 
  | "ViewContent"
  | "AddToCart" 
  | "InitiateCheckout"
  | "Purchase"
  | "Lead"
  | "CompleteRegistration"
  | "Search"
  | "AddPaymentInfo"
  | "AddToWishlist"
  | "Contact"
  | "CustomizeProduct"
  | "Donate"
  | "FindLocation"
  | "Schedule"
  | "StartTrial"
  | "SubmitApplication"
  | "Subscribe";

/**
 * Action source
 */
export type FacebookActionSource = "business_messaging" | "email" | "website" | "app" | "phone_call" | "chat" | "physical_store" | "other";

/**
 * Messaging channel
 */
export type FacebookMessagingChannel = "whatsapp" | "messenger" | "instagram" | "facebook" | "other";

/**
 * User data for Facebook Conversion API
 * Contains personal identifiers for event attribution
 */
export interface FacebookUserData {
  /** Page ID for the Facebook Page */
  page_id?: string;
  /** Whatsapp Business ID (plain text, not hashed) */
  whatsapp_business_account_id?: string;
  /** Instagram Business ID (plain text, not hashed) */
  instagram_business_account_id?: string;
  /** Click-to-WhatsApp Click ID (plain text, not hashed) */
  ctwa_clid?: string;
  /** Phone number (SHA-256 hashed) */
  ph?: string;
  /** External user ID */
  external_id?: string;
  /** Email address (SHA-256 hashed) */
  em?: string;
  /** First name (SHA-256 hashed) */
  fn?: string;
  /** Last name (SHA-256 hashed) */
  ln?: string;
  /** Date of birth (SHA-256 hashed) */
  db?: string;
  /** Gender (SHA-256 hashed) */
  ge?: string;
  /** City (SHA-256 hashed) */
  ct?: string;
  /** State (SHA-256 hashed) */
  st?: string;
  /** Zip code (SHA-256 hashed) */
  zp?: string;
  /** Country (SHA-256 hashed) */
  country?: string;
}

/**
 * Custom data for Facebook Conversion API events
 * Contains business-specific event parameters
 */
export interface FacebookCustomData {
  /** Currency code (ISO 4217) */
  currency?: string;
  /** Value of the event */
  value?: number;
  /** Content type (e.g., "product", "product_group") */
  content_type?: string;
  /** Array of content IDs */
  content_ids?: string[];
  /** Content name */
  content_name?: string;
  /** Content category */
  content_category?: string;
  /** Number of items */
  num_items?: number;
  /** Order ID */
  order_id?: string;
  /** Search string */
  search_string?: string;
  /** Status of the event */
  status?: string;
  /** Custom properties */
  [key: string]: any;
}


/**
 * Facebook Conversion API server configuration
 */
export interface FacebookConversionApiConfig {
  /** Dataset ID (Pixel ID) */
  datasetId: string;
  /** Access token for API authentication */
  accessToken: string;
  /** API version to use */
  apiVersion?: string;
  /** Partner agent identifier */
  partnerAgent?: string;
  /** Test event code for testing */
  testEventCode?: string;
  /** Enable debug mode */
  debug?: boolean;
}


/****************** Client Side ******************/

/**
 * Event data for common events
 */
export interface ViewContentEventData {
  contentType?: string;
  contentIds?: string[];
  contentName?: string;
  contentCategory?: string;
  currency?: string;
  value?: number;
}

export interface AddToCartEventData {
  contentType?: string;
  contentIds?: string[];
  contentName?: string;
  contentCategory?: string;
  currency?: string;
  value?: number;
  numItems?: number;
}

export interface InitiateCheckoutEventData {
  currency?: string;
  value?: number;
  numItems?: number;
  contentIds?: string[];
}

export interface PurchaseEventData {
  currency: string;
  value: number;
  orderId?: string;
  contentIds?: string[];
  numItems?: number;
}
