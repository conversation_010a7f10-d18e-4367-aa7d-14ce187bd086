// app/components/BottomSheet.tsx
import React, {
  useEffect,
  useState,
  useRef,
  ReactNode,
  useImperativeHandle,
  forwardRef
} from "react";
import Button from "./Button";
import { X } from "lucide-react";
import { cn } from "~/utils/cn";
import { Drawer, DrawerContent, DrawerOverlay } from "./DrawerV0";
import { useWindowHeight } from "~/hooks/useWindowHeight";

interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  className?: string;
  backdropClassName?: string;
  transitionDuration?: number; // in milliseconds
  showCloseButton?: boolean;
  showSwipeIndicator?: boolean;
  swipeIndicatorClassName?: string;
  sheetType?: "sheet" | "drawer"; // Added sheetType prop
  dismissible?: boolean; // default true
}

const BottomSheet = forwardRef<HTMLDivElement, BottomSheetProps>(
  (
    {
      isOpen,
      onClose,
      children,
      className = "",
      backdropClassName = "",
      transitionDuration = 300,
      showCloseButton = true,
      showSwipeIndicator = true,
      swipeIndicatorClassName = "",
      sheetType = "sheet",
      dismissible = true
    },
    ref
  ) => {
    const [show, setShow] = useState(isOpen);
    const [translateY, setTranslateY] = useState(0);
    const touchStartY = useRef<number>(0);
    const touchCurrentY = useRef<number>(0);
    const sheetRef = useRef<HTMLDivElement>(null);
    const firstFocusableRef = useRef<HTMLElement | null>(null);
    const lastFocusedElement = useRef<HTMLElement | null>(null);
    const isSwipingRef = useRef<boolean>(false);

    useImperativeHandle(ref, () => sheetRef.current!);

    useEffect(() => {
      if (isOpen) {
        setShow(true);
        document.body.style.overflow = "hidden";
        lastFocusedElement.current = document.activeElement as HTMLElement;
      } else {
        const timer = setTimeout(() => setShow(false), transitionDuration);
        document.body.style.overflow = "";
        return () => clearTimeout(timer);
      }
    }, [isOpen, transitionDuration]);

    useEffect(() => {
      if (isOpen && sheetRef.current) {
        const focusableElements =
          sheetRef.current.querySelectorAll<HTMLElement>(
            'a[href], button, textarea, input, select, [tabindex]:not([tabindex="-1"])'
          );
        if (focusableElements.length) {
          focusableElements[0].focus();
          firstFocusableRef.current = focusableElements[0];
        }
      } else if (!isOpen && lastFocusedElement.current) {
        lastFocusedElement.current.focus();
      }
    }, [isOpen]);

    useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === "Escape" && isOpen) {
          onClose();
        }

        if (isOpen && e.key === "Tab" && sheetRef.current) {
          const focusableElements =
            sheetRef.current.querySelectorAll<HTMLElement>(
              'a[href], button, textarea, input, select, [tabindex]:not([tabindex="-1"])'
            );
          if (focusableElements.length === 0) return;

          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (e.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            }
          } else {
            // Tab
            if (document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }
        }
      };

      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }, [isOpen, onClose]);

    const handleTouchStart = (e: React.TouchEvent) => {
      // Store the initial touch position
      touchStartY.current = e.touches[0].clientY;
      touchCurrentY.current = e.touches[0].clientY;

      // Only enable swiping if dismissible is true and we're at the top of the scroll
      if (dismissible && sheetRef.current && sheetRef.current.scrollTop <= 0) {
        isSwipingRef.current = true;
      } else {
        isSwipingRef.current = false;
      }
    };

    const handleTouchMove = (e: React.TouchEvent) => {
      touchCurrentY.current = e.touches[0].clientY;
      const deltaY = touchCurrentY.current - touchStartY.current;

      // Only handle swipe down and when we're at the top of content and dismissible is true
      if (dismissible && deltaY > 0 && isSwipingRef.current) {
        // Prevent default to stop scrolling
        e.preventDefault();
        setTranslateY(deltaY);
      }
    };

    const handleTouchEnd = () => {
      if (!dismissible || !isSwipingRef.current) return;

      const deltaY = touchCurrentY.current - touchStartY.current;
      if (deltaY > 80) {
        // Reduced threshold for better UX
        onClose();
      }
      setTranslateY(0);
      isSwipingRef.current = false;
    };

    const windowHeight = useWindowHeight();

    const isIOS = () => {
      return /iPad|iPhone|iPod/.test(navigator.userAgent);
    };

    useEffect(() => {
      const handleResize = () => {
        if (!sheetRef.current || !window.visualViewport) return;

        const visualViewportHeight = window.visualViewport.height;
        // Check if the keyboard is likely open (viewport height is significantly less than window height)
        const isKeyboardOpen = visualViewportHeight < windowHeight * 0.9; // Adjust threshold as needed

        if (!isKeyboardOpen) {
          // Reset the drawer's styles when the keyboard is hidden
          sheetRef.current.style.height = ""; // Reset to original height or CSS-defined height
          sheetRef.current.style.bottom = ""; // Reset to original position
          sheetRef.current.style.transform = ""; // Reset any transforms applied by Vaul
        }
      };

      // Add event listener for visualViewport resize
      if (window.visualViewport) {
        window.visualViewport.addEventListener("resize", handleResize);
        // Initial call to handle cases where the keyboard is already open
        handleResize();
      }

      // Cleanup event listener
      return () => {
        if (window.visualViewport) {
          window.visualViewport.removeEventListener("resize", handleResize);
        }
      };
    }, []);

    if (!show) return null;

    if (sheetType === "drawer") {
      return (
        <Drawer open={isOpen} onClose={onClose} dismissible={dismissible}>
          <DrawerOverlay className={backdropClassName}></DrawerOverlay>
          <DrawerContent className={`mx-auto max-w-lg ${isIOS() ? "no-keyboard-adjust" : ""}`} ref={sheetRef}>
            {showCloseButton && isOpen && (
              <Button
                className="absolute -top-12 left-1/2 -translate-x-1/2 text-white bg-black min-w-5 px-2 py-2 rounded-full"
                onClick={onClose}
              >
                <X size={`1.25rem`} />
              </Button>
            )}
            <div
              className={cn(
                "max-h-[80vh] overflow-y-auto no-scrollbar rounded-t-[10px]",
                showSwipeIndicator && "!pt-[20px]",
                className
              )}
            >
              {showSwipeIndicator && (
                <div
                  className={cn(
                    "absolute top-0 left-0 w-full h-[20px] bg-white rounded-t-[10px] before:content-[''] before:absolute before:top-2.5 before:left-1/2 before:-translate-x-1/2 before:h-1.5 before:w-14 before:rounded-full before:bg-muted",
                    swipeIndicatorClassName
                  )}
                />
              )}
              {React.cloneElement(children as React.ReactElement, {
                parentRef: sheetRef
              })}
            </div>
          </DrawerContent>
        </Drawer>
      );
    }

    return (
      <div
        className={cn(
          "fixed inset-0 z-50 flex flex-col items-center justify-end bg-black bg-opacity-50 transition-opacity duration-300 ease-in-out",
          isOpen ? "opacity-100" : "opacity-0",
          backdropClassName
        )}
        aria-hidden="true"
      >
        <div
          className="absolute inset-0"
          onClick={dismissible ? onClose : undefined}
          onKeyDown={(e) => {
            if (dismissible && (e.key === "Enter" || e.key === " ")) {
              onClose();
            }
          }}
          role="button"
          tabIndex={0}
          aria-label="Close bottom sheet"
        />

        {showCloseButton && (
          <Button
            className="text-white bg-black px-2 py-2 rounded-full my-4 z-10"
            onClick={onClose}
          >
            <X size={`1.25rem`} />
          </Button>
        )}

        {/* Bottom Sheet content */}
        <div
          ref={sheetRef}
          className={cn(
            "w-full max-w-lg bg-white rounded-t-lg shadow-lg transform transition-transform duration-300 max-h-[80vh] overflow-y-auto relative z-10",
            isOpen ? "translate-y-0" : "translate-y-full",
            className
          )}
          style={{
            transform: `translateY(${isOpen ? (translateY ? `${translateY}px` : 0) : "100%"
              })`,
            transitionDuration:
              translateY > 0 ? "0ms" : `${transitionDuration}ms`
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          role="dialog"
          aria-modal="true"
          aria-labelledby="bottom-sheet-title"
        >
          {showSwipeIndicator && (
            <div
              className="w-12 h-1.5 bg-gray-300 rounded-full mx-auto my-2"
              aria-hidden="true"
            />
          )}

          {children}
        </div>
      </div>
    );
  }
);

export default BottomSheet;
