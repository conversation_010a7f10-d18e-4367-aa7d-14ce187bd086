import React from "react";
import { roundOff } from "@utils/roundOff";
import { formatCurrency } from "~/utils/format";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { useAppConfigStore } from "~/stores/appConfig.store";

interface PaymentDetailsProps {
  totalOrderAmount: number;
  deliveryCharges: number;
  discountAmount: number;
  totalAmount: number;
  walletAmount: number;
  balancePayableAmount: number;
  approxPricing?: boolean;
  totalTaxAmt?: number;
  codOpted?: boolean;
  codAmount?: number;
  packagingCharges?: number;
  packagingTax?: number;
  itemsTax?: number;
  deliveryTax?: number;
  platformFee?: number;
  platformTax?: number;
}

export const PaymentDetails: React.FC<PaymentDetailsProps> = ({
  totalOrderAmount,
  deliveryCharges,
  discountAmount,
  totalAmount,
  walletAmount,
  balancePayableAmount,
  approxPricing = false,
  totalTaxAmt = 0,
  codOpted = false,
  codAmount = 0,
  packagingCharges = 0,
  packagingTax = 0,
  itemsTax = 0,
  deliveryTax = 0,
  platformFee = 0,
  platformTax = 0
}) => {
  const { networkConfig } = useAppConfigStore();
  return (
    <div className="mx-3 px-3 py-3 flex flex-col bg-white rounded-2xl shadow-lg  gap-2 mb-10">
      <div className="text-xs text-typography-400 tracking-wider">
        PAYMENT DETAILS
      </div>
      <div className="flex flex-col border border-dashed border-neutral-600 rounded-lg px-3 py-2 gap-2">
        <div className="flex items-center justify-between gap-3">
          <span className="text-sm text-typography-700">
            Item Total
            {approxPricing && (
              <span className="text-[10px] font-light text-typography-200">
                (approx)
              </span>
            )}
          </span>
          <span className="text-sm text-typography-700">
            ₹ {roundOff(totalOrderAmount, true)}
          </span>
        </div>
        <div className="border-b border-neutral-200" />

        {discountAmount > 0 && (
          <>
            <div className="flex items-center justify-between gap-3">
              <span className="text-sm text-typography-700">Item Discount</span>
              <span className="text-sm text-typography-700">
                - ₹ {roundOff(discountAmount, true)}
              </span>
            </div>
            <div className="border-b border-neutral-200" />
          </>
        )}

        <div className="flex items-center justify-between gap-3">
          <span className="text-sm text-typography-700">Delivery Charges</span>
          <span className="text-sm text-typography-700">
            ₹ {roundOff(deliveryCharges, true)}
          </span>
        </div>
        <div className="border-b border-neutral-200" />

        {platformFee > 0 && (
          <>
            <div className="flex items-center justify-between gap-3">
              <span className="text-sm text-typography-700">Platform Fee</span>
              <span className="text-sm text-typography-700">
                ₹ {roundOff(platformFee, true)}
              </span>
            </div>
            <div className="border-b border-neutral-200" />
          </>
        )}

        {(totalTaxAmt || packagingCharges > 0) && (
          <Popover>
            <PopoverTrigger>
              <div className="flex items-center justify-between gap-3 mb-[2px]">
                <span className="text-sm text-typography-700 underline underline-offset-[3px] decoration-dashed decoration-[1px]">
                  GST & Other Charges
                </span>
                <span className="text-sm text-typography-700">
                  ₹{" "}
                  {roundOff((totalTaxAmt || 0) + (packagingCharges || 0), true)}
                </span>
              </div>
              <div className="border-b border-neutral-200" />
            </PopoverTrigger>
            <PopoverContent side="top" align="start" className="p-3 rounded-lg">
              <div className="mb-1">
                <span className="font-bold text-typography-800">
                  GST & Other
                </span>
              </div>
              <div className="flex flex-col gap-2">
                {packagingCharges > 0 && (
                  <div className="flex flex-row gap-3 justify-between">
                    <div>
                      <p className="text-sm font-semibold text-typography-600">
                        {`Packaging${networkConfig?.networkId === 73 ? " & Handling" : ""} charges`}
                      </p>
                      <p className="text-xs text-typography-400">
                        Helps cover spill-proof packaging costs.
                      </p>
                    </div>
                    <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                      ₹ {roundOff(packagingCharges, true)}
                    </span>
                  </div>
                )}
                {packagingTax > 0 && (
                  <div className="flex flex-row gap-3 justify-between">
                    <div>
                      <p className="text-sm font-semibold text-typography-600">
                        {`GST on packaging${networkConfig?.networkId === 73 ? " & handling" : ""} charges`}
                      </p>
                      <p className="text-xs text-typography-400">
                        5% Government-mandated tax.
                      </p>
                    </div>
                    <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                      ₹ {roundOff(packagingTax, true)}
                    </span>
                  </div>
                )}
                {itemsTax > 0 && (
                  <div className="flex flex-row gap-3 justify-between">
                    <div>
                      <p className="text-sm font-semibold text-typography-600">
                        GST on item total
                      </p>
                      <p className="text-xs text-typography-400">
                        5% Government-mandated tax.
                      </p>
                    </div>
                    <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                      ₹ {roundOff(itemsTax, true)}
                    </span>
                  </div>
                )}
                {deliveryTax > 0 && (
                  <div className="flex flex-row gap-3 justify-between">
                    <div>
                      <p className="text-sm font-semibold text-typography-600">
                        GST on delivery charges
                      </p>
                      <p className="text-xs text-typography-400">
                        GST on delivery charges
                      </p>
                    </div>
                    <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                      ₹ {roundOff(deliveryTax, true)}
                    </span>
                  </div>
                )}
                {platformTax > 0 && (
                  <div className="flex flex-row gap-3 justify-between">
                    <div>
                      <p className="text-sm font-semibold text-typography-600">
                        GST on platform fee
                      </p>
                    </div>
                    <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                      ₹ {roundOff(platformTax, true)}
                    </span>
                  </div>
                )}
              </div>
            </PopoverContent>
          </Popover>
        )}

        <div className="flex justify-between pt-1 gap-3">
          <span className="font-semibold text-sm text-primary-500">
            Total Amount
            {approxPricing && (
              <span className="text-[10px] font-light text-gray-400">
                (approx)
              </span>
            )}
          </span>
          <span className="font-semibold text-sm text-primary-500">
            ₹ {roundOff(totalAmount, true)}
          </span>
        </div>
      </div>

      {
        <div className="flex flex-col border border-dashed border-neutral-600 rounded-lg px-3 py-2 gap-2 mt-1">
          {walletAmount > 0 && (
            <>
              <div className="flex items-center justify-between gap-3">
                <span className="text-sm text-typography-700">From Wallet</span>
                <span className="text-sm text-typography-700">
                  - ₹ {roundOff(walletAmount, true)}
                </span>
              </div>
              <div className={"border-b border-neutral-200"} />
            </>
          )}

          {
            <div className="flex justify-between pt-1 gap-3">
              <span className="font-semibold text-sm text-typography-600">
                Balance to be paid
                {approxPricing && (
                  <span className="text-[10px] font-light text-gray-400">
                    (approx)
                  </span>
                )}
              </span>
              <span className="font-semibold text-sm text-blue-600">
                {formatCurrency(codOpted ? codAmount : balancePayableAmount)}
              </span>
            </div>
          }
        </div>
      }
    </div>
  );
};
