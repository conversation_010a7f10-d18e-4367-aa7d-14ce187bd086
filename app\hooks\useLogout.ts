import { useFetcher } from "@remix-run/react";
import { useLoginStore } from "~/stores/login.store";

/**
 * Hook to handle user logout
 * @returns A function to trigger logout
 */
export function useLogout() {
  const fetcher = useFetcher();
  const { openLogin } = useLoginStore();

  const logout = () => {
    // Submit a logout action
    fetcher.submit({}, { method: "post", action: "/api/logout" });

    // Show login modal after logout
    openLogin();
  };

  return {
    logout,
    isLoading: fetcher.state === "submitting"
  };
}
