import { getItemOptionsAPI } from "~/services/buyer.service";
import { ItemOptionsData } from "~/types";
import { ApiResponse } from "~/types/Api";
import { AppSource } from "~/types/app";
import { DecodedToken } from "~/types/user";
import { getSession } from "./session.server";
import { parseJWT } from "./token-utils";
import {
  getSelectedSellerCookie,
  getMultiSellerCookie,
  getCoordinatesCookie
} from "./cookie.server";

export const getAppSource = (request: Request): AppSource => {
  const url = new URL(request.url);

  if (url.hostname.includes("wa_")) {
    return "whatsappchat";
  } else {
    return "buyer-web";
  }
};

export const getItemOptions = async (request: Request) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const decoded = parseJWT(access_token || "") as DecodedToken;

  const url = new URL(request.url);
  let sellerId = url.searchParams.get("sellerId");
  let deliveryDate = url.searchParams.get("deliveryDate") || undefined;
  const redirectedFromBuyer =
    (url.searchParams.get("redirected") as unknown as boolean) || false;
  const categoryId = url.searchParams.get("categoryId");
  const matchBy = url.searchParams.get("matchBy")?.trim();
  const parentCategoryId = url.searchParams.get("parentCategoryId");

  let itemOptions = {} as ApiResponse<ItemOptionsData>;
  let lat = undefined;
  let long = undefined;
  let isMultiSeller = false;

  try {
    // get multiSeller from cookie
    const multiSellerValue = getMultiSellerCookie(request);
    isMultiSeller = multiSellerValue === true;

    // get lat, long from cookie
    const coordinates = getCoordinatesCookie(request);
    if (coordinates) {
      lat = Number(process.env?.DEVICE_LAT) || coordinates.latitude;
      long = Number(process.env?.DEVICE_LONG) || coordinates.longitude;
    }
    if ((!lat || !long) && isMultiSeller) {
      return { error: "LOCATION_ACCESS_REQUIRED" };
    }

    // get sellerId, deliveryDate from cookie
    if (!sellerId && !deliveryDate) {
      const selectedSeller = getSelectedSellerCookie(request);
      if (selectedSeller) {
        sellerId = selectedSeller.sellerId;
        deliveryDate = selectedSeller.deliveryDate;
      }
    }
  } catch (error) {
    console.error("Error parsing cookie");
    if ((!lat || !long) && isMultiSeller) {
      return { error: "LOCATION_ACCESS_REQUIRED" };
    }
  }

  itemOptions = await getItemOptionsAPI(
    redirectedFromBuyer ? decoded.userDetails.mobileNumber : null,
    request,
    deliveryDate,
    Number(sellerId),
    categoryId ? Number(categoryId) : undefined,
    matchBy ?? undefined,
    parentCategoryId ? Number(parentCategoryId) : undefined,
    lat,
    long
  );

  return { itemOptions, sellerId, deliveryDate, lat, long, isMultiSeller };
};
