import { Dietary } from "~/types";

interface DietaryImageProps {
  dietary: Dietary;
  className?: string;
}

/**
 * A component that renders a dietary image (veg/non-veg) based on the dietary type
 *
 * @param props.dietary - The dietary type (veg/nonveg/egg)
 * @param props.className - Optional CSS class names
 */
export const DietaryImage = ({ dietary, className }: DietaryImageProps) => {
  let imageSrc = "/veg-icon.svg";
  let altText = "veg";

  if (dietary === "nonveg") {
    imageSrc = "/non-veg-icon.svg";
    altText = "non-veg";
  } else if (dietary === "egg") {
    imageSrc = "/egg-icon.svg";
    altText = "egg";
  }

  return (
    <img
      src={imageSrc}
      alt={altText}
      className={`w-4 h-4 ${className || ""}`}
    />
  );
};
