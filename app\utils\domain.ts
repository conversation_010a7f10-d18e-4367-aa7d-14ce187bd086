// utils/domain.ts
export function getDomainFromRequest(request: Request): {
  domain?: string;
  hasSubdomain: boolean;
} {
  let domain = new URL(request.url).hostname;

  // Handle proxies or load balancers if necessary
  const forwardedHost = request.headers.get("X-Forwarded-Host");
  if (forwardedHost) {
    domain = forwardedHost.split(",")[0].trim();
  }

  // Remove port number if present
  domain = domain.split(":")[0];
  //Remove www if present
  domain = domain.replace("www.", "");

  // const hasSubdomain = domain.split('.').length > 2;

  if (
    process.env.NODE_ENV !== "production" &&
    process.env?.DEBUG_DOMAIN &&
    process.env?.DEBUG_DOMAIN.length > 3
  ) {
    domain = process.env.DEBUG_DOMAIN;
  }
  return { domain, hasSubdomain: true };
}
