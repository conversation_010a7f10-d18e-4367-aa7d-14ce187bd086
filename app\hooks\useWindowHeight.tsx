import React, { useEffect, useState } from "react";

export function useWindowHeight() {
  const [windowHeight, setWindowHeight] = useState(() => {
    if (typeof window !== "undefined") {
      return window.innerHeight;
    }
    return 0;
  });

  useEffect(() => {
    if (typeof window === "undefined") return;
    if (typeof screen === "undefined") return;

    const handleResize = () => {
      const currentHeight = window.innerHeight;
      // If current height is greater than stored height,
      // it's likely due to rotation -> update height.
      if (currentHeight > windowHeight) {
        setWindowHeight(currentHeight);
      }
    };

    const handleOrientationChange = () => {
      // On rotation, reset height directly
      setWindowHeight(window.innerHeight);

    };

    window.addEventListener("resize", handleResize);
    screen.orientation.addEventListener("change", handleOrientationChange);

    return () => {
      window.removeEventListener("resize", handleResize);
      screen.orientation.removeEventListener("change", handleOrientationChange);
    };
  }, [windowHeight]);

  return windowHeight;
}