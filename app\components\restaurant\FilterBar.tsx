import React from "react";
import { DietaryImage } from "../common/DietaryImage";

export interface FilterTag {
  id: string;
  label: string;
  value: string;
}

interface FilterBarProps {
  tags: FilterTag[];
  selectedTags: string[];
  onTagSelect: (tagValue: string) => void;
  className?: string;
}

// Function to get appropriate icon for common tags
// const getTagIcon = (tagValue: string, isSelected: boolean) => {
//   const iconClass = `${
//     isSelected ? "text-white" : "text-gray-600"
//   } h-4 w-4 mr-1`;
//
//   switch (tagValue.toLowerCase()) {
//     case "veg":
//       return <Vegan className={iconClass} />;
//     case "non-veg":
//       return <CircleDot className={`${iconClass} text-red-500`} />;
//     default:
//       return isSelected ? <Check className={iconClass} /> : null;
//   }
// };

const FilterBar: React.FC<FilterBarProps> = ({
  tags,
  selectedTags,
  onTagSelect,
  className = ""
}) => {
  if (!tags || tags.length === 0) return null;

  // Switch based on tag value to render appropriate icon
  const renderIcon = (tag: string) => {
    const iconClass = "w-3 h-3 mr-2";
    switch (tag.toLowerCase()) {
      case "veg":
        return <DietaryImage dietary="veg" className={iconClass} />;
      case "nonveg":
        return <DietaryImage dietary="nonveg" className={iconClass} />;
      case "egg":
        return <DietaryImage dietary="egg" className={iconClass} />;
      default:
        return null;
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="flex items-center overflow-x-scroll no-scrollbar py-2 px-3 gap-2">
        {tags.map((tag) => {
          const isSelected = selectedTags.includes(tag.value);
          return (
            <button
              key={tag.id}
              onClick={() => onTagSelect(tag.value)}
              className={`font px-3 py-1 text-sm whitespace-nowrap rounded-md transition-colors flex items-center border border-gray-300 shadow-sm ${isSelected
                ? "bg-primary-50 text-gray-700 border border-primary"
                : ""
                }`}
              aria-pressed={isSelected}
            >
              {renderIcon(tag.value)}
              <span className="text-sm font-semibold">{tag.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default FilterBar;
