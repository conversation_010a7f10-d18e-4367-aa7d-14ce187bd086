import { ActionFunction, json, redirect } from "@remix-run/node";
import { getSession, destroySession, commitSession } from "~/utils/session.server";
import { verifyOtp } from "~/services/auth.server";
import { User } from "~/types";
import { parseJWT } from "~/utils/token-utils";

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));

  // Destroy the current session
  const headers = new Headers();
  headers.append("Set-Cookie", await destroySession(session));

  // Try to login with anonymous user after logout if network is RET11
  // try {
  //   const verifyResponse = (
  //     await verifyOtp("0000000000", "112233", "anonymousLogin", request)
  //   ).data;

  //   session.set("access_token", verifyResponse.access_token ?? "");
  //   session.set("refresh_token", verifyResponse.refresh_token ?? "");

  //   const tokenData = parseJWT(verifyResponse.access_token ?? "");
  //   const userDetails = tokenData.userDetails;
  //   const user: User = {
  //     userId: userDetails.userId,
  //     userName: userDetails.userName,
  //     businessName: userDetails.businessName,
  //     buyerId: userDetails.buyerId,
  //     isAnonymous: true
  //   };
  //   session.set("user", user);
  //   headers.append("Set-Cookie", await commitSession(session));

  //   return redirect("/home/<USER>", { headers });
  // } catch (error) {
  //   console.error("Anonymous login after logout failed:", error);
  // }

  // Fallback: return success with session destroyed
  return json(
    { success: true },
    { headers }
  );
};
