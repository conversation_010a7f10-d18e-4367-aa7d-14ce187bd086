import { Minus, Plus } from "lucide-react";
import React from "react";

const AddItemButton: React.FC<{
  qty: number;
  onAdd: () => void;
  onRemove: () => void;
  isDisabled: boolean;
  unit: string;
  variations?: number;
  setShowVariants?: (value: boolean) => void;
}> = ({
  qty,
  onAdd,
  onRemove,
  isDisabled,
  unit,
  variations = 0,
  setShowVariants
}) => {
  const handleOnAdd = () => {
    console.log("onAdd");
    if (variations && setShowVariants) {
      setShowVariants(true);
    } else {
      onAdd();
    }
  };
  return (
    <div
      className={`relative border p-2 rounded-md flex flex-row items-center justify-center gap-2 min-w-24 h-10 ${
        qty > 0 || isDisabled ? "bg-white" : "bg-primary-50"
      } ${isDisabled ? "border-neutral-600" : "border-primary"} `}
      onClick={!isDisabled && qty === 0 ? handleOnAdd : () => {}}
      role="button"
      tabIndex={0}
      onKeyDown={() => {}}
    >
      {qty > 0 && (
        <button
          onClick={onRemove}
          className={`text-center  font-bold text-lg ${
            isDisabled ? "text-neutral-600" : "text-primary"
          }`}
          disabled={isDisabled}
        >
          <Minus size={20} />
        </button>
      )}

      {qty > 0 ? (
        <button
          disabled={isDisabled}
          onClick={handleOnAdd}
          className={`text-center  ${
            isDisabled ? "text-neutral-800" : "text-primary "
          } font-bold text-xs`}
        >{`${qty} ${unit}`}</button>
      ) : (
        <button
          disabled={isDisabled}
          // onClick={onAdd}
          className={`text-center ${
            isDisabled ? "text-neutral-800" : "text-primary "
          }  font-bold text-xs  ${
            isDisabled ? "border-neutral-600" : "border-primary"
          }`}
        >
          ADD
        </button>
      )}

      {qty > 0 && (
        <button
          onClick={handleOnAdd}
          className={`text-center  font-bold text-lg ${
            isDisabled ? "text-neutral-600" : "text-primary"
          }`}
          disabled={isDisabled}
        >
          <Plus size={20} />
        </button>
      )}
      {variations ? (
        <span className="absolute -bottom-1 text-[.6rem] text-primary bg-white rounded-md px-1">
          {variations} options
        </span>
      ) : null}
    </div>
  );
};

export default AddItemButton;
