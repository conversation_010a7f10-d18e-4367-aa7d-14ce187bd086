import React from "react";
import { cn } from "~/utils/cn";

interface PrimaryButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
}

export default function SecondaryButton({
  children,
  className = "",
  ...props
}: PrimaryButtonProps) {
  return (
    <button
      {...props}
      className={cn(
        "w-full flex justify-center py-3 px-4 rounded-md shadow-sm text-sm font-medium text-primary bg-white border border-primary hover:bg-primary hover:text-white",
        className
      )}
    >
      {children}
    </button>
  );
}
