import { json, ActionFunction } from "@remix-run/node";
import {
  createRazorpayOrderAPI,
  verifyRazorpayPaymentAPI
} from "~/services/payment.service";
import { getSession } from "~/utils/session.server";

interface ActionData {
  error?: string;
  success?: boolean;
  razorpayKey?: string;
  orderId?: string;
  amount?: number;
  currency?: string;
}

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const formData = await request.formData();
  const intent = formData.get("intent");

  // Get user information from session if needed
  const access_token = session.get("access_token");
  if (!access_token) {
    return json<ActionData>({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Handle order creation
    if (intent === "createOrder") {
      const amount = formData.get("amount");
      const currency = (formData.get("currency") as string) || "INR";
      const receipt =
        (formData.get("receipt") as string) || `order_${Date.now()}`;
      const notes = (formData.get("notes") as string) || "{}";
      const customerName = (formData.get("customerName") as string) || "";
      const customerEmail = (formData.get("customerEmail") as string) || "";
      const customerContact = (formData.get("customerContact") as string) || "";
      const preconfirmUid = (formData.get("preconfirmUid") as string) || "";

      if (!amount) {
        return json<ActionData>(
          { error: "Amount is required" },
          { status: 400 }
        );
      }

      // Add preconfirmUid to notes if provided and notes is empty
      let finalNotes = notes;
      if (preconfirmUid && notes === "{}") {
        finalNotes = `{"preconfirmUid": "${preconfirmUid}"}`;
      }

      const orderPayload = {
        amount: Number(amount),
        currency,
        receipt,
        notes: finalNotes,
        customerName,
        customerEmail,
        customerContact
      };

      try {
        const response = await createRazorpayOrderAPI(orderPayload, request);

        if (!response.data) {
          return json<ActionData>(
            { error: "Failed to create order" },
            { status: 500 }
          );
        }

        const orderData = response.data;

        return json<ActionData>({
          success: true,
          razorpayKey: orderData.razorpayKey,
          orderId: orderData.orderId || orderData.id,
          amount: orderData.amount,
          currency: orderData.currency
        });
      } catch (error) {
        console.error("Error creating Razorpay order:", error);
        return json<ActionData>(
          {
            error:
              error instanceof Error ? error.message : "Failed to create order"
          },
          { status: 500 }
        );
      }
    }

    // Handle payment verification
    if (intent === "verifyPayment") {
      const razorpayOrderId = formData.get("razorpayOrderId") as string;
      const razorpayPaymentId = formData.get("razorpayPaymentId") as string;
      const razorpaySignature = formData.get("razorpaySignature") as string;

      if (!razorpayOrderId || !razorpayPaymentId || !razorpaySignature) {
        return json<ActionData>(
          { error: "Missing required payment verification fields" },
          { status: 400 }
        );
      }

      try {
        const response = await verifyRazorpayPaymentAPI(
          { razorpayOrderId, razorpayPaymentId, razorpaySignature },
          request
        );

        if (!response.data) {
          return json<ActionData>(
            { error: "Payment verification failed" },
            { status: 500 }
          );
        }

        const verificationData = response.data;

        if (verificationData.status === "success") {
          return json<ActionData>({
            success: true
          });
        } else {
          return json<ActionData>(
            {
              error: verificationData.message || "Payment verification failed"
            },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error("Error verifying payment:", error);
        return json<ActionData>(
          {
            error:
              error instanceof Error
                ? error.message
                : "Failed to verify payment"
          },
          { status: 500 }
        );
      }
    }

    return json<ActionData>({ error: "Invalid intent" }, { status: 400 });
  } catch (error) {
    console.error("Error in Razorpay API:", error);
    return json<ActionData>(
      {
        error: error instanceof Error ? error.message : "Internal server error"
      },
      { status: 500 }
    );
  }
};
