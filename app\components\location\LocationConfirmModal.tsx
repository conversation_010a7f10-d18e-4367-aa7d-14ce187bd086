import React from "react";

interface LocationConfirmModalProps {
  /**
   * Current address to display
   */
  address?: string;

  /**
   * Custom title for the modal
   */
  title?: string;

  /**
   * Custom message for the modal
   */
  message?: string;

  /**
   * Additional benefits to display, each with an icon and text
   */
  benefits?: {
    icon: React.ReactNode;
    text: string;
  }[];

  /**
   * Custom button text
   */
  buttonText?: string;

  /**
   * Handler for the update location button click
   */
  onUpdateLocation: () => void;

  /**
   * Optional additional CSS classes for the modal container
   */
  className?: string;
}

/**
 * A reusable location confirmation modal component
 * Used when a user needs to confirm or update their location
 */
const LocationConfirmModal: React.FC<LocationConfirmModalProps> = ({
  address = "Your current address",
  title = "Confirm Your Location",
  message = "We need your accurate location to",
  benefits = defaultBenefits,
  buttonText = "Update My Location",
  onUpdateLocation,
  className = ""
}) => {
  // Location pin icon for the header and button
  const LocationPinIcon = ({
    width = 32,
    height = 32
  }: {
    width?: number;
    height?: number;
  }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  );

  return (
    <>
      {/* Overlay background */}
      <div className="fixed inset-0 bg-black opacity-50 z-40"></div>

      {/* Modal container with full opacity */}
      <div
        className={`fixed inset-0 flex items-center justify-center z-50 p-4 ${className}`}
      >
        <div className="bg-white rounded-xl shadow-lg p-6 max-w-md w-full">
          {/* Header with icon */}
          <div className="flex flex-col items-center mb-4">
            <div className="bg-primary/10 p-4 rounded-full mb-2">
              <LocationPinIcon />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
          </div>

          {/* Address content */}
          <div className="mb-6 text-center">
            <p className="text-gray-600 mb-2">{message}</p>

            {/* Benefits */}
            <div className="flex justify-center space-x-4 mb-2">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  {benefit.icon}
                  <span className="text-sm">{benefit.text}</span>
                </div>
              ))}
            </div>

            {/* Address display */}
            {/* <div className="bg-gray-100 p-2 rounded-lg mb-2">
              <p className="text-sm font-medium text-gray-700">{address}</p>
            </div> */}
            <p className="text-xs text-gray-500">
              Your exact location helps us serve you better
            </p>
          </div>

          {/* Button */}
          <div className="flex flex-col gap-2">
            <button
              className="flex items-center justify-center w-full py-3 px-4 bg-primary hover:bg-primary-600 text-white font-medium rounded-lg"
              onClick={onUpdateLocation}
            >
              <span className="h-5 w-5 mr-2">
                <LocationPinIcon width={20} height={20} />
              </span>
              {buttonText}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

// Default benefits to show if none are provided
const defaultBenefits = [
  {
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 text-green-500 mr-1"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M5 13l4 4L19 7"
        />
      </svg>
    ),
    text: "Fast Delivery"
  },
  {
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 text-green-500 mr-1"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
        />
      </svg>
    ),
    text: "Service Availability"
  }
];

export default LocationConfirmModal;
