import { ActionFunction, json } from "@remix-run/node";
import { getSession } from "~/utils/session.server";
import {
  initiatePayment,
  initiateDeposit,
  getUpiPaymentStatusDetails
} from "~/services/buyer.service";
import { createClientResponse } from "~/utils/clientReponse";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken } from "~/types/user";
import { MoneyCollectionDetails, RPPaymentDetails } from "~/types";
import { MnetCoreResponse } from "~/types/Api";
interface ActionData {
  success?: boolean;
  message?: string;
  errors?: {
    [key: string]: string;
  };
  paymentUrl?: string;
  refId?: number | string;
  paymentStatus?: "SUCCESS" | "FAILED" | "PENDING" | "SUCCESS_NOT_CNF";
  razorpayOptions?: RPPaymentDetails;
}

const getEffectiveInitiatePaymentResponse = (
  response: MoneyCollectionDetails
) => {
  const effectiveResponse: ActionData = {
    success: true,
    message: "Payment initiated successfully"
  };
  if (response?.paymentGateway === "icici_upi") {
    effectiveResponse.paymentUrl =
      response?.iciciUpiPaymentDetails?.queryString;
    effectiveResponse.refId = response?.id;
  } else if (response?.paymentGateway === "rp") {
    effectiveResponse.refId = response?.id;
    effectiveResponse.razorpayOptions = response?.rpPaymentDetails;
    console.log("Razorpay options:", effectiveResponse.razorpayOptions);
  }
  return effectiveResponse;
};

const processInitiatePayment = async (
  request: Request,
  formData: FormData,
  access_token: string
) => {
  const decoded = parseJWT(access_token) as DecodedToken;
  const buyerId = decoded.userDetails.buyerId;
  const amount = parseFloat(formData.get("amount") as string);
  const orderGroupId = parseInt(formData.get("orderGroupId") as string, 10);
  const note = `Initiating Upi payment for Rs ${amount}`;

  const errors: { [key: string]: string } = {};
  if (!buyerId) errors.buyerId = "buyerId is required";
  if (!amount) errors.amount = "amount is required";
  if (!orderGroupId) errors.orderGroupId = "orderGroupId is required";

  // return json<ActionData>(
  //   { success: false, message: "Invalid request", errors },
  //   { status: 400 }
  // );

  if (Object.keys(errors).length > 0) {
    return json<ActionData>(
      { success: false, message: "Invalid request", errors },
      { status: 400 }
    );
  }

  try {
    const response = await initiatePayment(
      orderGroupId,
      {
        initiatedByUserId: buyerId,
        amount,
        note
      },
      request
    );

    if (response) {
      return createClientResponse<ActionData, MoneyCollectionDetails>(
        request,
        getEffectiveInitiatePaymentResponse(response.data),
        response
      );
    } else {
      return json<ActionData>({
        success: false,
        message: "Failed to initiate payment"
      });
    }
  } catch (error) {
    console.error("Error initiating payment:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";
    return json<ActionData>(
      {
        success: false,
        message: `Payment failed: ${errorMessage}`
      },
      { status: 500 }
    );
  }
};

const processInitiateDeposit = async (request: Request, formData: FormData) => {
  const preconfirmUid = formData.get("preconfirmUid") as string;
  const amount = parseFloat(formData.get("amount") as string);

  const errors: { [key: string]: string } = {};
  if (!preconfirmUid) errors.preconfirmUid = "preconfirmUid is required";
  if (!amount) errors.amount = "amount is required";

  if (Object.keys(errors).length > 0) {
    return json<ActionData>(
      { success: false, message: "Invalid request", errors },
      { status: 400 }
    );
  }

  try {
    const response = await initiateDeposit(preconfirmUid, amount, request);

    if (response.data.success && response.data.data) {
      return createClientResponse<
        ActionData,
        MnetCoreResponse<MoneyCollectionDetails>
      >(
        request,
        getEffectiveInitiatePaymentResponse(response.data.data),
        response
      );
    } else {
      return json<ActionData>({
        success: false,
        message: "Failed to initiate deposit"
      });
    }
  } catch (error) {
    console.error("Error initiating deposit:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";
    return json<ActionData>(
      {
        success: false,
        message: `Deposit failed: ${errorMessage}`
      },
      { status: 500 }
    );
  }
};

const processPaymentStatus = async (request: Request, formData: FormData) => {
  const refId = formData.get("refId") as string;
  if (!refId) {
    return json<ActionData>(
      { errors: { refId: "refId is required" } },
      { status: 400 }
    );
  }

  try {
    const response = await getUpiPaymentStatusDetails(refId, request);

    const paymentResponse = response.data;

    const isSuccess =
      !paymentResponse.postActionPending && paymentResponse.ogId;
    const isPending = paymentResponse.postActionPending;
    const isPaymentDoneButOrderNotConfirmed =
      !paymentResponse.postActionPending &&
      paymentResponse.paymentSuccess &&
      !paymentResponse.ogId;

    if (isSuccess) {
      return createClientResponse(
        request,
        { paymentStatus: "SUCCESS" },
        response
      );
    } else if (isPending) {
      return createClientResponse(
        request,
        { paymentStatus: "PENDING" },
        response
      );
    } else if (isPaymentDoneButOrderNotConfirmed) {
      return createClientResponse(
        request,
        {
          paymentStatus: "SUCCESS_NOT_CNF",
          message: "Payment is done, but order is not confirm please try again"
        },
        response
      );
    } else {
      return createClientResponse(
        request,
        {
          paymentStatus: "FAILED",
          message: "Payment verification failed"
        },
        response
      );
    }
  } catch (error) {
    console.error("Error checking payment status:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";
    return json<ActionData>(
      {
        success: false,
        paymentStatus: "FAILED",
        message: `Payment verification failed: ${errorMessage}`
      },
      { status: 500 }
    );
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  if (!access_token) {
    return json<ActionData>(
      { errors: { auth: "Unauthorized" } },
      { status: 401 }
    );
  }

  const formData = await request.formData();
  const requestName = formData.get("requestName");

  if (!requestName) {
    return json(
      { errors: { requestName: "requestName is required" } },
      { status: 400 }
    );
  }

  try {
    if (requestName === "InitiatePayment") {
      return processInitiatePayment(request, formData, access_token);
    } else if (requestName === "InitiateDeposit") {
      return processInitiateDeposit(request, formData);
    } else if (requestName === "PaymentStatus") {
      return processPaymentStatus(request, formData);
    } else {
      return json(
        { errors: { requestName: "Invalid requestName" } },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.error("Error processing payment:", error);
    if (error instanceof Error) {
      return json(
        { message: error.message || "Failed to process payment" },
        { status: 500 }
      );
    }
    return json({ message: "An unknown error occurred" }, { status: 500 });
  }
};
