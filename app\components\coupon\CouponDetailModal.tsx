import { useCouponStore } from "~/stores/coupon.store";
import BottomSheet from "../BottmSheet";

export const CouponDetailModal = () => {
  const { selectedCoupon, showCouponDetails, hideCouponDetails } =
    useCouponStore();

  if (!selectedCoupon) return null;

  const handleCopyCode = () => {
    if (selectedCoupon.code) {
      navigator.clipboard.writeText(selectedCoupon.code);
    }
  };

  return (
    <BottomSheet
      isOpen={showCouponDetails}
      onClose={hideCouponDetails}
      showSwipeIndicator={false}
      sheetType="drawer"
    >
      <div className="bg-neutral-100 rounded-lg w-full max-w-lg overflow-hidden">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-3 text-typography-700">
            Coupon Details
          </h2>
          <div className="border-t border-gray-300 mb-3"></div>

          <div className="bg-white rounded-lg p-3 mb-4 text-typography-800">
            <h3 className="text-lg font-semibold">
              {selectedCoupon.description
                ? selectedCoupon.description
                : `Flat ₹${selectedCoupon.discountValue?.toFixed(0)} OFF`}
            </h3>
            <p className="text-gray-600 text-sm text-typography-500 mb-3">
              Save ₹{selectedCoupon.discountValue?.toFixed(0)} with this code
            </p>

            <div className="border-t border-gray-300 mb-3"></div>

            <div className="flex justify-between items-center">
              <div className="border-2 border-teal-500 border-dashed px-3 py-1 rounded-md text-teal-500 font-medium">
                {selectedCoupon.code}
              </div>
              <button
                onClick={handleCopyCode}
                className="text-teal-500 font-medium"
              >
                TAP TO COPY
              </button>
            </div>

            {selectedCoupon.minOrderValue && (
              <p className="flex flex-row items-start mt-4">
                <div className="mr-2 text-teal-500">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                </div>
                <p className="text-sm">
                  Minimum order value: ₹{selectedCoupon.minOrderValue}
                </p>
              </p>
            )}

            {selectedCoupon.maxDiscountValue && (
              <p className="flex flex-row items-start mt-2">
                <div className="mr-2 text-teal-500">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                </div>
                <p className="text-sm">
                  Maximum discount: ₹{selectedCoupon.maxDiscountValue}
                </p>
              </p>
            )}

            {selectedCoupon.terms && (
              <ul className="space-y-2 mt-4">
                {selectedCoupon.terms.map((term, index) => (
                  <li key={index} className="flex items-start">
                    <div className="mr-2 pt-0.5 text-teal-500">
                      <svg
                        className="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                    <p className="text-sm">{term}</p>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <button
            type="button"
            onClick={hideCouponDetails}
            className={`w-full mt-2 py-2 px-6 text-white font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50 bg-teal-500 hover:bg-teal-600 focus:ring-teal-500
              `}
          >
            Okay
          </button>
        </div>
      </div>
    </BottomSheet>
  );
};
