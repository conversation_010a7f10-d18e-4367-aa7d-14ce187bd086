import React from "react";
import { FileText } from "lucide-react";

interface OrderNoteProps {
  note: string;
  onOpenNotePopup: () => void;
}

export const OrderNote: React.FC<OrderNoteProps> = ({
  note,
  onOpenNotePopup
}) => {
  return (
    <button
      className="flex items-center gap-2 py-1 px-2 text-left w-fit border border-neutral-500 rounded-[4px]"
      onClick={onOpenNotePopup}
      aria-label={
        note ? "Edit note for the restaurant" : "Add a note for the restaurant"
      }
    >
      <FileText className="h-4 w-4 text-typography-400 shrink-0" />
      <div className="text-sm text-typography-500">
        {note ? (
          <div className="flex flex-col">
            <p className="text-typography-700 font-medium text-xs line-clamp-1">{note}</p>
          </div>
        ) : (
          <span>Add a note for the restaurant</span>
        )}
      </div>
    </button>
  );
};
