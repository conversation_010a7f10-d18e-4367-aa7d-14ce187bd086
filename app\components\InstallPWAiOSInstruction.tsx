// app/components/InstallPWAButton.tsx
import React, { useState, useEffect } from "react";
import { Share } from "lucide-react";

// Extend the Window event for TS
declare global {
  interface BeforeInstallPromptEvent extends Event {
    prompt(): Promise<void>;
    userChoice: Promise<{
      outcome: "accepted" | "dismissed";
      platform: string;
    }>;
  }
}

interface InstallPWAiOSInstructionProps {
  /** URL of your PWA icon (e.g. S3 link or local asset) */
  /** Background color for the banner */
  browser?: string;
  themeColor?: string;
  /** Main headline text */
  title?: string;
  /** Sub‑headline text */
  subtitle?: string;
  platform?: string;
}

export default function InstallPWAiOSInstruction({
  themeColor = "Primary",
  title = "Get The App",
  subtitle = "For Better Experience",
  browser = "safari",
  platform = "iOS"
}: InstallPWAiOSInstructionProps) {
  //   const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [visible, setVisible] = useState(false);

  return (
    <div className={`w-full flex flex-col gap-2 p-1 bg-neutral-100`}>
      <div className="text-base font-semibold">Add to Home Screen</div>
      <div className="border border-neutral-300"></div>
      <div className="flex flex-col gap-3">
        <div className="w-full flex flex-col p-2 items-center gap-0 bg-white rounded-xl">
          <div className="w-full flex flex-col gap-0">
            <div className="text-typography-800 font-semibold text-lg">
              Step 1 :
            </div>
            <div className="flex flex-wrap gap-1 items-center text-typography-800 font-normal text-base">
              <p className="w-fit text-nowrap">Tap share button</p>
              <div className="p-1">
                <Share size={20} color={"#222A33"} />
              </div>
              <p>at the</p>
              <p>bottom</p>
              <p>of</p>
              <p>the</p>
              <p>browser.</p>
            </div>
          </div>

          <img
            src="/step_1.svg"
            alt="testing"
            className="w-full h-auto max-w-[22.5rem]"
          />
        </div>

        <div className="w-full flex flex-col gap-2 items-center p-2 bg-white rounded-xl">
          <div className="w-full flex-col gap-2">
            <div className="text-typography-800 font-semibold text-lg">
              Step 2 :
            </div>
            <div className="flex flex-wrap gap-1 items-center text-typography-800 font-normal text-base">
              <p>
                Click
                <span className="font-semibold"> “Add to Home Screen“ </span>
              </p>
            </div>
          </div>

          <img
            src="/step_2.svg"
            alt="testing"
            className="w-full h-auto  max-w-[22.5rem]"
          />
        </div>
      </div>
    </div>
  );
}
