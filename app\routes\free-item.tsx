import { useNavigate, useLoaderData } from "@remix-run/react";
import { LoaderFunction, json } from "@remix-run/node";
import { useAppConfigStore } from "~/stores/appConfig.store";
import {
  FreeItemType,
  getFreeItemDetails,
  redeemFreeItem
} from "~/services/home.service";
import dayjs from "dayjs";
import InfoModel from "~/components/models/InfoModel";

interface LoaderData {
  freeItem: FreeItemType;
  error?: string;
}

const getFreeItemErrorMessage = (code: string | undefined) => {
  switch (code) {
    case "BFI_NOT_FOUND":
      return "Sorry! Item not found.";
    case "BFI_EXPIRED":
      return "Sorry! Your coupon expired.";
    default:
      return null;
  }
};

export const loader: LoaderFunction = async ({ request }) => {
  const searchParams = new URL(request.url).searchParams;
  if (!searchParams.get("id")) {
    return json({ freeItem: {}, error: "Invalid request" });
  }

  try {
    const response = await getFreeItemDetails(
      searchParams.get("id") || "",
      request
    );
    const response2 = await redeemFreeItem(
      searchParams.get("id") || "",
      request
    );

    if (!response2.data.success || !response.data.success) {
      const errorMessage =
        getFreeItemErrorMessage(response2.data.error?.code) ||
        getFreeItemErrorMessage(response.data.error?.message) ||
        "Failed to redeem free item";
      return json({
        freeItem: {},
        error: errorMessage
      });
    }

    return {
      freeItem: response2.data.data,
      error: undefined
    };
  } catch (error) {
    return json({
      freeItem: {},
      error: "Failed to get free item details"
    });
  }
};

export default function FreeItem() {
  const navigate = useNavigate();
  const { networkConfig } = useAppConfigStore();

  // Use loader data
  const { freeItem: freeItemData, error: loaderError } =
    useLoaderData<LoaderData>();

  const handleClose = () => {
    navigate("/home");
  };

  if (loaderError) {
    return (
      <div>
        <InfoModel
          title="Sorry!"
          message={loaderError}
          buttonType="primary"
          buttonText="Explore Menu"
          onClose={handleClose}
          isCountdownRedirectionAllowed={false}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full flex items-center justify-center px-2 py-4 bg-black/70 no-scrollbar">
      <div
        id="free-item"
        className="w-full max-w-xl bg-[#FCF9F4] p-4 rounded-md"
      >
        <div className="w-full flex flex-col items-center rounded-lg p-3 border-2 border-dashed border-neutral-800">
          <div className="mt-4 w-40 h-40 flex items-center justify-center">
            <img
              src={networkConfig?.businessLogo}
              alt="Image"
              width="160px"
              height="160px"
              className="rounded-[50px] object-contain"
            />
          </div>
          <div className="mt-4 w-full px-2 [@media(min-width:500px)]:px-10">
            <button className="w-full mx-auto px-5 py-4 rounded-[50px] text-white text-xl font-bold bg-[linear-gradient(90deg,_#007661_0%,_#00A390_100%)]">
              🎉 Free {freeItemData.sellerItemName} 🎉
            </button>
          </div>
          <div className="mt-2 text-center text-typography-800 font-semibold text-xl px-1 [@media(min-width:500px)]:px-8">
            Show this message to your captain to redeem your{" "}
            {freeItemData.sellerItemName}.
          </div>
          <div className="mt-2 text-typography-800 font-semibold text-lg">
            <ul className="list-disc list-inside text-center">
              <li>
                Expires on {dayjs(freeItemData.expiresAt).format("DD-MM-YYYY")}
              </li>
              <li>Code : {freeItemData.code}</li>
            </ul>
          </div>
          <div className="my-2 text-center text-typography-500 px-2 text-sm">
            One redemption per customer. Cannot be combined with other offers.
          </div>
        </div>
      </div>
    </div>
  );
}
