import { useState, useEffect } from "react";
import { useFetcher, useLocation, useNavigate } from "@remix-run/react";
import { ArrowLeft } from "lucide-react";
import BottomSheet from "./BottmSheet";
import Button from "./Button";
import { useLoginStore } from "~/stores/login.store";
import { syncCartPageAfterLogin } from "~/utils/cartPageSync";
import SpinnerLoader from "./loader/SpinnerLoader";

interface ActionData {
  success?: boolean;
  message?: string;
  intent?: string;
}

export default function LoginBottomSheet() {
  const defaultDelay = 59;
  const { isLoginOpen, closeLogin, redirectPath, dismissible } = useLoginStore();
  const fetcher = useFetcher<ActionData>();
  const navigate = useNavigate();
  const [step, setStep] = useState<"phone" | "otp">("phone");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otp, setOtp] = useState("");
  const [delay, setDelay] = useState(defaultDelay);
  const [loginError, setLoginError] = useState<string>("");
  const location = useLocation();
  const [loadingPage, setLoadingPage] = useState(false);

  // Timer for OTP resend
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (step === "otp" && delay > 0) {
      timer = setTimeout(() => setDelay(delay - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [step, delay]);

  // Reset state when modal opens
  useEffect(() => {
    if (isLoginOpen) {
      setStep("phone");
      setPhoneNumber("");
      setOtp("");
      setLoginError("");
    }
  }, [isLoginOpen]);

  // Effect to handle step transition based on fetcher data
  useEffect(() => {
    if (fetcher.data?.success && fetcher.data.intent === "getOtp") {
      setStep("otp");
      setDelay(defaultDelay);
    } else if (fetcher.data?.success && fetcher.data.intent === "verifyOtp") {
      // Handle successful login
      handleSuccessfulLogin();
    }
  }, [fetcher.data]);

  const handleSuccessfulLogin = async () => {
    try {
      // sync cart page and navigate
      setLoadingPage(true);
      await syncCartPageAfterLogin(location.pathname + location.search, navigate);

      // Close login modal
      setLoadingPage(false);
      closeLogin();
    } catch (error) {
      console.error("Error handling login:", error);
      // Even if cart page sync fails, close login and navigate
      setLoadingPage(false);
      closeLogin();
      navigate(`${location.pathname}${location.search}`);
    }
  };

  useEffect(() => {
    if (fetcher.data && !fetcher.data.success) {
      setLoginError(fetcher.data.message || "");
    }
  }, [fetcher.data]);

  const handleResendOtp = () => {
    setOtp("");
    setDelay(defaultDelay);
    fetcher.submit(
      { intent: "getOtp", phoneNumber },
      { method: "post", action: "/api/user-login" }
    );
  };

  const handleBack = () => {
    setStep("phone");
    setOtp("");
    fetcher.submit(
      { intent: "clearActionData" },
      { method: "post", action: "/api/user-login" }
    );
  };

  const isValidPhoneNo = (phoneNumber: string) => {
    return /^\d{10}$/.test(phoneNumber);
  };

  const isValidOTP = (otp: string) => {
    return /^\d{6}$/.test(otp);
  };

  function isValidNumber(value: string | number): boolean {
    if (value === "") {
      return true;
    }

    if (
      typeof value === "number" ||
      (typeof value === "string" && /^[+-]?\d+(\.\d+)?$/.test(value))
    ) {
      return true;
    }

    return false;
  }

  const handlePhoneSubmit = () => {
    if (isValidPhoneNo(phoneNumber)) {
      fetcher.submit(
        { intent: "getOtp", phoneNumber },
        { method: "post", action: "/api/user-login" }
      );
    }
  };

  const handleOtpSubmit = () => {
    if (isValidOTP(otp)) {
      const formData = new FormData();
      formData.append("intent", "verifyOtp");
      formData.append("phoneNumber", phoneNumber);
      formData.append("otp", otp);

      // Add redirectTo param to the form
      if (redirectPath) {
        formData.append("redirectTo", redirectPath);
      }

      fetcher.submit(formData, {
        method: "post",
        action: "/api/user-login"
      });
    }
  };

  const isSubmitting = fetcher.state === "submitting";
  const handlePhoneKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && isValidPhoneNo(phoneNumber)) {
      handlePhoneSubmit();
    }
  };
  const handleOtpKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && isValidNumber(otp)) {
      handleOtpSubmit();
    }
  };

  return (
    <BottomSheet
      isOpen={isLoginOpen}
      onClose={() => closeLogin()}
      className="bg-white p-4 pt-0"
      showCloseButton={false}
      backdropClassName="bg-black bg-opacity-20"
      sheetType="drawer"
      dismissible={dismissible}
    >
      <div className="max-w-md mx-auto">
        {step === "phone" && (
          <h2 className="text-md font-medium mb-2 text-text-primary mt-2">
            Let&apos;s get you started
          </h2>
        )}
        <div className="">
          {step === "phone" ? (
            <>
              <div className="mb-2">
                <div className="flex rounded-lg border border-gray-300 focus-within:border-teal-600 focus-within:ring-1 focus-within:ring-teal-500 py-2 shadow-lg">
                  <span className="inline-flex items-center rounded-l-md border-r border-gray-300 bg-white px-4 text-md text-grey-900 font-medium">
                    +91
                  </span>
                  <input
                    type="tel"
                    id="phoneNumber"
                    className="block w-full flex-1 rounded-r-md bg-white text-text-primary focus:outline-none text-md px-4 [&:-webkit-autofill]:!text-black [&:-webkit-autofill]:[transition-delay:9999s] [&:-webkit-autofill]:[background-color:white!important]"
                    placeholder="Your phone number"
                    value={phoneNumber}
                    onKeyDown={handlePhoneKeyDown}
                    enterKeyHint="done"
                    onChange={(e) => {
                      if (isValidNumber(e.target.value)) {
                        setPhoneNumber(e.target.value);
                      }
                    }}
                    maxLength={10}
                    minLength={10}
                    required
                  />
                </div>
              </div>
              <p className="text-xs text-text-disabled mb-8">
                We will send you OTP on this number
              </p>
              <Button
                type="button"
                onClick={handlePhoneSubmit}
                className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                  !isValidPhoneNo(phoneNumber)
                    ? "bg-gray-400 hover:bg-gray-500"
                    : "bg-teal-500 hover:bg-teal-600"
                }`}
                disabled={!isValidPhoneNo(phoneNumber) || isSubmitting}
              >
                {isSubmitting ? "Sending..." : "GET OTP"}
              </Button>
            </>
          ) : (
            <>
              <div className="flex items-center mb-4">
                <button
                  type="button"
                  onClick={handleBack}
                  className="mr-4 text-text-primary"
                >
                  <ArrowLeft size={24} />
                </button>
                <label
                  htmlFor="otp"
                  className="block text-md font-medium text-text-primary"
                >
                  Enter 6 Digits OTP
                </label>
              </div>
              <div className="flex flex-col w-full">
                <input
                  id="otp"
                  className="block w-full rounded-lg border border-gray-300 bg-white text-text-primary focus:outline-none text-md py-2 px-4 shadow-lg [&:-webkit-autofill]:!text-black [&:-webkit-autofill]:[transition-delay:9999s] [&:-webkit-autofill]:[background-color:white!important]"
                  placeholder="Enter OTP"
                  value={otp}
                  onKeyDown={handleOtpKeyDown}
                  enterKeyHint="done"
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d{0,6}$/.test(value)) {
                      setOtp(value);
                    }
                  }}
                  maxLength={6}
                  required
                  pattern="\d{6}"
                />
                {loginError.length > 0 && (
                  <span className="mt-1 text-xs text-red-500">Invalid OTP</span>
                )}
              </div>
              <span className="mt-1 text-xs text-text-disabled">
                OTP has been sent to{" "}
                <span className="text-gray-900 font-medium">{phoneNumber}</span>
              </span>
              <span className="mt-2 mb-8 flex flex-row justify-between text-xs text-text-disabled">
                {"Haven't receive the OTP?"}
                <Button
                  type="button"
                  onClick={handleResendOtp}
                  disabled={delay > 0 || isSubmitting}
                  className={`${
                    delay || isSubmitting ? "text-gray-500" : "text-teal-500"
                  } `}
                >
                  RESEND {delay > 0 && `00:${delay}`}
                </Button>
              </span>
              <Button
                className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                  !isValidOTP(otp)
                    ? "bg-gray-400 hover:bg-gray-500"
                    : "bg-teal-500 hover:bg-teal-600"
                }`}
                type="button"
                onClick={handleOtpSubmit}
                disabled={!isValidOTP(otp) || isSubmitting}
              >
                {isSubmitting ? "Verifying..." : "CONTINUE"}
              </Button>
            </>
          )}
        </div>
        {loadingPage && (
          <SpinnerLoader loading={true} size={12} />
        )}
      </div>
    </BottomSheet>
  );
}
