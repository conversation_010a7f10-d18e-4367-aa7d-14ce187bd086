import { ItemOptionsData, SellerInfo } from "~/types";
import BottomSheet from "../BottmSheet";
import { Clock, MapPin, Store } from "lucide-react";
import { cn } from "~/utils/cn";
import { capitalizeSentence, getLastSegment } from "~/utils/string";
import { useState } from "react";

export default function MultiOutletBottomSheet({
  showOutletSheet,
  setShowOutletSheet,
  sellerList,
  itemOptionsData,
  handleOutletSelection
}: {
  showOutletSheet: boolean;
  setShowOutletSheet: (show: boolean) => void;
  sellerList: SellerInfo[];
  itemOptionsData: ItemOptionsData | null;
  handleOutletSelection: (seller: SellerInfo) => void;
}) {
  const [showAllOutlets, setShowAllOutlets] = useState(false);
  const outletsToShow = showAllOutlets ? sellerList : sellerList.slice(0, 5);

  const outletCSS = (seller: SellerInfo) => {
    if (seller.id === itemOptionsData?.sellerId) {
      return "selected";
    } else if (seller.distanceInKm > 15) {
      return "far-away";
    } else {
      return "available";
    }
  };

  return (
    <BottomSheet
      isOpen={showOutletSheet}
      onClose={() => setShowOutletSheet(false)}
      className="bg-white"
      showSwipeIndicator={true}
      sheetType="drawer"
      showCloseButton={false}
    >
      <div className="p-2">
        {/* Header */}
        <div className="pb-2 mb-2 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-900">Choose Outlet</h2>
          <p className="text-sm text-gray-500">
            Select from {sellerList.length} available outlet
            {sellerList.length !== 1 ? "s" : ""}
          </p>
        </div>

        {/* Outlet List */}
        <div className="space-y-2 max-h-[65vh] overflow-y-auto no-scrollbar pb-3">
          {outletsToShow.map((seller, index) => {
            const status = outletCSS(seller);
            const isFarAway = status === "far-away";
            const isSelected = status === "selected";

            return (
              <div
                key={seller.id}
                className={cn(
                  "relative rounded-xl p-3 cursor-pointer transition-all duration-300 border-2",
                  isSelected
                    ? "border-primary bg-gradient-to-r from-primary/5 to-primary/10"
                    : isFarAway
                    ? "border-gray-300 bg-gray-50"
                    : "border-gray-200 bg-white hover:border-primary/30 hover:shadow-md hover:bg-gray-50/50"
                )}
                onClick={() => {
                  setShowOutletSheet(false);
                  handleOutletSelection(seller);
                }}
              >
                <div className="flex items-center gap-2">
                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    {/* Outlet Name & Status */}
                    <div className="flex-1 min-w-0">
                      <h3 className={cn(
                        "font-semibold leading-tight truncate",
                        isSelected
                          ? "text-primary"
                          : isFarAway
                          ? "text-gray-500"
                          : "text-gray-900"
                      )}>
                        {seller.name ? capitalizeSentence(getLastSegment(seller.name)) : `Outlet ${index + 1}`}
                      </h3>
                    </div>

                    {/* Address */}
                    {/* <div className="flex items-center gap-1 py-0.5">
                    <MapPin size={14} className="text-gray-400 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-gray-600 line-clamp-2 leading-4 truncate">
                      {seller.address ? capitalizeSentence(seller.address) : ""}
                    </p>
                  </div> */}

                    {/* Distance Info */}
                    <div className="flex items-center gap-2 mt-0.5">
                      <div
                        className={cn(
                          "flex items-center gap-1.5 p-1 rounded-lg",
                          isFarAway ? "bg-gray-200" : "bg-gray-100"
                        )}
                      >
                        <Clock
                          size={12}
                          className={cn(
                            isFarAway ? "text-gray-400" : "text-gray-500"
                          )}
                        />
                        <span
                          className={cn(
                            "text-xs font-medium",
                            isFarAway ? "text-gray-500" : "text-gray-700"
                          )}
                        >
                          {seller.distanceInKm.toFixed(1)} km
                        </span>
                      </div>
                      <span
                        className={cn(
                          "text-xs",
                          isFarAway ? "text-gray-400" : "text-gray-500"
                        )}
                      >
                        • {Math.ceil(seller.distanceInKm * 5)} mins away
                      </span>
                    </div>
                  </div>
                  {/* Status Badge*/}
                  <div>
                    <div
                      className={cn(
                        "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                        seller.enabled
                          ? isFarAway
                            ? "bg-gray-200 text-gray-600"
                            : "bg-green-100 text-green-700"
                          : "bg-red-100 text-red-700"
                      )}
                    >
                      <div
                        className={cn(
                          "w-1.5 h-1.5 rounded-full",
                          seller.enabled
                            ? isFarAway
                              ? "bg-gray-500"
                              : "bg-green-500"
                            : "bg-red-500"
                        )}
                      ></div>
                      {seller.enabled ? "Open" : "Closed"}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
          {sellerList.length > 5 && (
            <button
              className="w-full py-2 text-center text-sm font-semibold text-primary"
              onClick={() => setShowAllOutlets(!showAllOutlets)}
            >
              {showAllOutlets ? "Show Less" : `Show ${sellerList.length - 5} More`}
            </button>
          )}
        </div>
      </div>
    </BottomSheet>
  );
}
