import dayjs from "dayjs";

export const formatCurrency = (amount: number, decimals = 2): string => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(amount);
};

export const getEstDeliveryTime = (estDeliveryTime: string) => {
  if (!estDeliveryTime) return "0";
  const digit = estDeliveryTime.match(/\d+/);
  return digit ? digit[0] : "0";
};

export const IsValidHour = (time: string): boolean => {
  const hhmm = /^([01]\d|2[0-3]):([0-5]\d)$/;
  const hhmmss = /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/;
  return hhmm.test(time) || hhmmss.test(time);
};