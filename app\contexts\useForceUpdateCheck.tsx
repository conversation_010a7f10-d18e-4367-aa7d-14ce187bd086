import { useNavigation } from "@remix-run/react";
import { useEffect, useState } from "react";

function isForceUpdateRequired(minVersion: number, currentVersion: number) {
  return minVersion > currentVersion;
}

interface AppInfo {
  version?: number;
  packageName?: string;
}

export function useForceUpdateCheck(minVersion: number) {
  const navigation = useNavigation();

  const [forceUpdate, setForceUpdate] = useState(false);
  const [appInfo, setAppInfo] = useState<AppInfo>({});

  useEffect(() => {
    async function checkAppVersion() {
      try {
        // console.log("force update called: ", minVersion);

        if (typeof window?.AndroidBridge === "undefined") {
          // console.error("AndroidBridge is not available");
          setForceUpdate(false);
          return;
        }
        // window.getAppInfo =
        //   window.getAppInfo ||
        //   (() => Promise.resolve({ packageName: "", version: 100 }));
        // const appInfo: AppInfo = (window as any).getAppInfo();

        if (
          typeof window !== undefined &&
          typeof window.getAppInfo !== undefined &&
          window.getAppInfo
        ) {
          const appInfo = await window.getAppInfo();
          setAppInfo(appInfo);
        }

        if (appInfo && appInfo.version) {
          const requiresUpdate = isForceUpdateRequired(
            minVersion,
            appInfo.version
          );
          setForceUpdate(requiresUpdate);
        }
      } catch (error) {
        console.error("Error fetching app info:", error);
      }
    }

    checkAppVersion();
  }, [minVersion, navigation.state]);

  return { forceUpdate, appInfo };
}
