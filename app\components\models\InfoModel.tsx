// app/components/SuccessDialog.tsx
import React, { useEffect, useState } from "react";
import { Transition } from "@headlessui/react";
import PrimaryButton from "../PrimaryButton";
import { useNavigate } from "@remix-run/react";
import SecondaryButton from "../SecondaryButton";
import { Info } from "lucide-react";

interface InfoModelProps {
  title?: string;
  message?: string;
  buttonType?: "primary" | "secondary";
  buttonText?: string;
  onClose?: () => void;
  onRedirect?: () => void;
  countdownStart?: number; // Optional prop to set countdown start (default 10)
  specialCase?: string;
  isCountdownRedirectionAllowed?: boolean;
}

const InfoModel: React.FC<InfoModelProps> = ({
  title,
  message,
  buttonText,
  buttonType = "primary",
  onClose,
  onRedirect,
  countdownStart = 5,
  specialCase,
  isCountdownRedirectionAllowed = true
}) => {
  const navigate = useNavigate();

  const [countdown, setCountdown] = useState<number>(countdownStart);
  const [isVisible, setIsVisible] = useState<boolean>(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else if (onRedirect) {
      onRedirect();
    } else {
      navigate(-1);
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout | undefined = undefined;

    if (countdown <= 0 && isCountdownRedirectionAllowed) {
      console.log(countdown);
      handleClose();
      return () => clearInterval(timer);
    }

    timer = setInterval(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    if (timer) {
      return () => clearInterval(timer);
    }
  }, [countdown]);

  switch (specialCase) {
    case "BookingClosed":
      return (
        <Transition
          show={isVisible}
          enter="transition-opacity duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 flex justify-center items-center p-8 bg-white z-[100]">
            <div className="bg-white rounded-lg transform transition-all">
              <div className="flex flex-col gap-4 items-center">
                <div className="">
                  {/* <img
                src="/success.svg"
                alt="Success"
                className="w-24 h-24 mb-8 animate-bounce"
              /> */}
                  <img
                    src="/booking_closed.svg"
                    className="w-80 h-auto"
                    alt="Booking Closed"
                  />
                </div>
                <div className="flex flex-col gap-1 items-center">
                  {title && (
                    <h2 className="text-xl font-semibold text-gray-700 ">
                      {title}
                    </h2>
                  )}
                  {message && (
                    <p className="text-md text-center font-light text-gray-400">
                      {message}
                    </p>
                  )}
                </div>
                <div className="flex flex-col p-6 gap-4 w-full">
                  {buttonText && (
                    <div className="w-full">
                      {buttonType === "primary" ? (
                        <PrimaryButton
                          onClick={handleClose}
                          className={`w-full`}
                        >
                          {buttonText}
                        </PrimaryButton>
                      ) : (
                        <SecondaryButton
                          onClick={handleClose}
                          className={`w-full`}
                        >
                          {buttonText}
                        </SecondaryButton>
                      )}
                    </div>
                  )}
                </div>
                {isCountdownRedirectionAllowed && (
                  <p className="text-sm text-gray-500 mt-6">
                    Redirecting in {countdown} second
                    {countdown !== 1 ? "s" : ""}...
                  </p>
                )}
              </div>
            </div>
          </div>
        </Transition>
      );
    case "OutofServiceArea":
      return (
        <Transition
          show={isVisible}
          enter="transition-opacity duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 flex justify-center items-center p-8 bg-white z-[100]">
            <div className="bg-white rounded-lg transform transition-all">
              <div className="flex flex-col gap-8 items-center">
                <div className="">
                  {/* <img
                src="/success.svg"
                alt="Success"
                className="w-24 h-24 mb-8 animate-bounce"
              /> */}
                  <img
                    src="/out_of_service_area.png"
                    className="w-48 h-auto animate-[bounce_1s_ease-in-out_2.5]"
                    alt="Out of Service Area"
                  />
                </div>
                <div className="flex flex-col gap-1 items-center">
                  {title && (
                    <h2 className="text-xl font-semibold text-gray-700 ">
                      {title}
                    </h2>
                  )}
                  {message && (
                    <p className="text-md text-center font-light text-gray-400">
                      {message}
                    </p>
                  )}
                </div>
                <div className="flex flex-col p-6 gap-4 w-full">
                  {buttonText && (
                    <div className="w-full">
                      {buttonType === "primary" ? (
                        <PrimaryButton
                          onClick={handleClose}
                          className={`w-full`}
                        >
                          {buttonText}
                        </PrimaryButton>
                      ) : (
                        <SecondaryButton
                          onClick={handleClose}
                          className={`w-full`}
                        >
                          {buttonText}
                        </SecondaryButton>
                      )}
                    </div>
                  )}
                </div>
                {isCountdownRedirectionAllowed && (
                  <p className="text-sm text-gray-500 mt-6">
                    Redirecting in {countdown} second
                    {countdown !== 1 ? "s" : ""}...
                  </p>
                )}
              </div>
            </div>
          </div>
        </Transition>
      );
    case "BackNavigation":
      return (
        <Transition
          show={isVisible}
          enter="transition-opacity duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 flex justify-center items-center p-8 bg-white z-[100]">
            <div className="bg-white rounded-lg transform transition-all max-w-[90%]">
              <div className="flex flex-col items-center p-6">
                <div className="mb-6">
                  <Info className="w-12 h-12 text-primary mb-4" />
                </div>
                {title && (
                  <h2 className="text-xl font-semibold text-gray-700 mb-2">
                    {title}
                  </h2>
                )}
                {message && (
                  <p className="text-md mb-6 text-center font-light text-gray-400">
                    {message}
                  </p>
                )}
                <div className="flex flex-col w-full gap-3">
                  <PrimaryButton onClick={handleClose} className="w-full">
                    {buttonText || "Stay on Page"}
                  </PrimaryButton>
                  <SecondaryButton
                    onClick={onRedirect ? onRedirect : () => navigate(-1)}
                    className="w-full"
                  >
                    Leave Page
                  </SecondaryButton>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      );
    case "AuthRequired":
      return (
        <Transition
          show={isVisible}
          enter="transition-opacity duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 flex justify-center items-center p-8 bg-white z-[100]">
            <div className="bg-white rounded-lg transform transition-all">
              <div className="flex flex-col gap-4 items-center">
                <div className="">
                  {/* <img
                src="/success.svg"
                alt="Success"
                className="w-24 h-24 mb-8 animate-bounce"
              /> */}
                  <img
                    src="/auth-required.svg"
                    className="w-80 h-auto"
                    alt="Auth Required"
                  />
                </div>
                <div className="flex flex-col gap-1 items-center">
                  {title && (
                    <h2 className="text-xl font-semibold text-gray-700 ">
                      {title}
                    </h2>
                  )}
                  {message && (
                    <p className="text-md text-center font-light text-gray-400">
                      {message}
                    </p>
                  )}
                </div>
                <div className="flex flex-col p-6 gap-4 w-full">
                  {buttonText && (
                    <div className="w-full">
                      {buttonType === "primary" ? (
                        <PrimaryButton
                          onClick={handleClose}
                          className={`w-full`}
                        >
                          {buttonText}
                        </PrimaryButton>
                      ) : (
                        <SecondaryButton
                          onClick={handleClose}
                          className={`w-full`}
                        >
                          {buttonText}
                        </SecondaryButton>
                      )}
                    </div>
                  )}
                </div>
                {isCountdownRedirectionAllowed && (
                  <p className="text-sm text-gray-500 mt-6">
                    Redirecting in {countdown} second
                    {countdown !== 1 ? "s" : ""}...
                  </p>
                )}
              </div>
            </div>
          </div>
        </Transition>
      );
    default:
      return (
        <Transition
          show={isVisible}
          enter="transition-opacity duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 flex items-center justify-center bg-white z-[100]">
            <div className="bg-white rounded-lg transform transition-all max-w-[90%]">
              <div className="flex flex-col items-center">
                <div className="">
                  {/* <img
                src="/success.svg"
                alt="Success"
                className="w-24 h-24 mb-8 animate-bounce"
              /> */}
                  <Info className="w-12 h-12 text-primary mb-4 animate-bounce" />
                </div>
                {title && (
                  <h2 className="text-xl font-semibold text-gray-700 mb-2">
                    {title}
                  </h2>
                )}
                {message && (
                  <p className="text-md mb-4 text-center font-light text-gray-400">
                    {message}
                  </p>
                )}
                {buttonText && (
                  <div className="w-full px-8 mt-4">
                    {buttonType === "primary" ? (
                      <PrimaryButton onClick={handleClose} className={`w-full`}>
                        {buttonText}
                      </PrimaryButton>
                    ) : (
                      <SecondaryButton
                        onClick={handleClose}
                        className={`w-full`}
                      >
                        {buttonText}
                      </SecondaryButton>
                    )}
                  </div>
                )}
                {onRedirect && (
                  <p className="text-sm text-gray-500 mt-6">
                    Redirecting in {countdown} second
                    {countdown !== 1 ? "s" : ""}...
                  </p>
                )}
              </div>
            </div>
          </div>
        </Transition>
      );
  }
};

export default InfoModel;
