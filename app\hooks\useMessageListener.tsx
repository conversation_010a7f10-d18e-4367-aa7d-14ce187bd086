import { useEffect } from "react";
import { CommonMessage, Message } from "~/types/iframe";

/**
 * Adds a listener for `message` events with type-safe messages.
 *
 * @template TMessages - The union type of expected messages.
 * @param onMessage - Callback invoked with the message event when a message is received.
 */
const useMessageListener = <TMessages extends Message<string, CommonMessage>>(
  onMessage: (message: TMessages, event: MessageEvent) => void
) => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      const handleMessage = (event: MessageEvent) => {
        try {
          const message = event.data as TMessages;
          onMessage(message, event);
        } catch (error) {
          console.error("Failed to process message", error);
        }
      };

      window.addEventListener("message", handleMessage);

      return () => {
        window.removeEventListener("message", handleMessage);
      };
    }
  }, [onMessage]);
};

export default useMessageListener;
