// app/components/RefreshButton.tsx

import React from "react";
import Button from "@components/Button";

interface RefreshButtonProps {
  onClick: () => void;
  loading?: boolean;
}

const RefreshButton: React.FC<RefreshButtonProps> = ({ onClick, loading }) => {
  return (
    <Button
      onClick={onClick}
      disabled={loading}
      className="flex items-center border border-teal-600 bg-[#F7FFFD] text-teal-500 text-xs font-bold py-1 px-2 rounded"
    >
      {loading ? (
        <svg
          className="animate-spin h-4 w-4 mr-2 text-teal-600"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8v8H4z"
          ></path>
        </svg>
      ) : (
        <>
          <img
            src="/refresh_small.png"
            alt="Refresh"
            className="w-4 h-4 mr-2"
          />
          <span className="text-teal-600">Refresh</span>
        </>
      )}
    </Button>
  );
};

export default RefreshButton;
