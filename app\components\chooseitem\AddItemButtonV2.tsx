import { Minus, Plus } from "lucide-react";
import React from "react";
import { cn } from "~/utils/cn";

const AddItemButton: React.FC<{
  qty: number;
  onAdd: () => void;
  onRemove: () => void;
  isDisabled: boolean;
  unit: string;
  className?: string;
  btnConfig?: {
    showUnit?: boolean;
    iconSize?: number;
    btnType?: "primary" | "secondary";
  };
}> = ({
  qty,
  onAdd,
  onRemove,
  isDisabled,
  unit,
  className,
  btnConfig: userBtnConfig
}) => {
  // Merge default config with user provided config
  const btnConfig = {
    showUnit: true,
    iconSize: 16,
    btnType: "primary",
    ...userBtnConfig
  };

  const isSecondary = btnConfig.btnType === "secondary";

  return (
    <div
      className={cn(
        "border rounded-lg flex flex-row items-center justify-center gap-2 px-3 py-2 text-[.7rem]",
        isDisabled
          ? "bg-white"
          : isSecondary
          ? "bg-primary-50"
          : qty > 0
          ? "bg-primary"
          : "bg-primary-50",
        isDisabled ? "border-gray-400" : "border-primary",
        className
      )}
      onClick={!isDisabled && qty === 0 ? onAdd : () => {}}
      role="button"
      tabIndex={0}
      onKeyDown={() => {}}
    >
      {qty > 0 && (
        <button
          onClick={onRemove}
          className={`text-center font-bold ${
            isDisabled
              ? "text-gray-400"
              : isSecondary
              ? "text-primary"
              : "text-white"
          }`}
          disabled={isDisabled}
        >
          <Minus
            size={btnConfig.iconSize}
            className={isSecondary ? "text-primary" : "text-white"}
          />
        </button>
      )}

      {qty > 0 ? (
        <button
          className={`text-center text-sm w-full ${
            isDisabled
              ? "text-typography-500"
              : isSecondary
              ? "text-primary"
              : "text-white"
          } font-semibold`}
        >
          {`${qty} ${unit?.length && btnConfig.showUnit ? unit : ""}`}
        </button>
      ) : (
        <button
          disabled={isDisabled}
          className={`text-center ${
            isDisabled
              ? "text-gray-400"
              : isSecondary
              ? "text-primary"
              : "text-primary-500"
          } font-semibold text-sm w-full ${
            isDisabled ? "border-gray-400" : "border-primary"
          }`}
        >
          ADD
        </button>
      )}

      {qty > 0 && (
        <button
          onClick={onAdd}
          className={`text-center font-bold ${
            isDisabled
              ? "text-gray-400"
              : isSecondary
              ? "text-primary"
              : "text-white"
          }`}
          disabled={isDisabled}
        >
          <Plus
            size={btnConfig.iconSize}
            className={isSecondary ? "text-primary" : "text-white"}
          />
        </button>
      )}
    </div>
  );
};

export default AddItemButton;
