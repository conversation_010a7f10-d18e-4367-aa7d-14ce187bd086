import { apiRequest, getApiUrl } from "@utils/api";
import { ApiResponse } from "~/types/Api";
import type {
  RazorpayOrder,
  RazorpayOrderPayload,
  RazorpayPaymentVerification,
  RazorpayPaymentVerificationPayload
} from "~/types/payment.types";

/**
 * Creates a new Razorpay order
 * @param payload Order creation payload
 * @param request Request object containing cookies/headers
 */
export async function createRazorpayOrderAPI(
  payload: RazorpayOrderPayload,
  request: Request
): Promise<ApiResponse<RazorpayOrder>> {
  const url = getApiUrl("/api/razorpay/order", undefined, undefined, undefined);

  try {
    const response = await apiRequest<RazorpayOrder>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Create Razorpay order API failed");
    }
  } catch (error) {
    console.error("Error creating Razorpay order:", error);
    throw error;
  }
}

/**
 * Verifies a Razorpay payment
 * @param payload Payment verification payload
 * @param request Request object containing cookies/headers
 */
export async function verifyRazorpayPaymentAPI(
  payload: RazorpayPaymentVerificationPayload,
  request: Request
): Promise<ApiResponse<RazorpayPaymentVerification>> {
  const url = getApiUrl(
    "/api/razorpay/verify",
    undefined,
    undefined,
    undefined
  );

  try {
    const response = await apiRequest<RazorpayPaymentVerification>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Verify Razorpay payment API failed");
    }
  } catch (error) {
    console.error("Error verifying Razorpay payment:", error);
    throw error;
  }
}
