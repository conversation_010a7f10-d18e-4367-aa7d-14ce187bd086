import React from "react";
import { Store } from "lucide-react";
import { FulfillmentType } from "~/types";

interface DeliveryOptionsProps {
  selectedOption: FulfillmentType;
  onOptionChange: (option: FulfillmentType) => void;
}

export const DeliveryOptions: React.FC<DeliveryOptionsProps> = ({
  selectedOption,
  onOptionChange
}) => {
  return (
    <div className="mx-3 px-3 py-3 bg-white rounded-2xl shadow-lg">
      <div className="flex justify-between text-xs text-typography-400 tracking-wider pb-1">
        <span>Delivery Options</span>
      </div>
      <div className="w-full mt-2">
        {/* <div className="flex flex-col gap-3"> */}
        {/* Options */}
        <div className="flex w-full h-10 border border-neutral-200 rounded-full items-center px-2">
          <button
            onClick={() => onOptionChange("DELIVERY")}
            className={`flex-1 py-1 px-4 rounded-full text-xs font-medium transition-all text-center ${
              selectedOption === "DELIVERY"
                ? "bg-primary text-white"
                : "bg-white text-neutral-700"
            }`}
          >
            Door-step delivery
          </button>
          <button
            onClick={() => onOptionChange("TAKE_AWAY")}
            className={`flex-1 py-1 px-4 rounded-full text-xs font-medium transition-all text-center ${
              selectedOption === "TAKE_AWAY"
                ? "bg-primary text-white"
                : "bg-white text-neutral-700"
            }`}
          >
            Pickup from store
          </button>
        </div>

        {/* Store Details
        <div className="flex items-start gap-2 bg-neutral-50 p-3 rounded-lg">
          <Store className="w-5 h-5 text-neutral-600 mt-0.5" />
          <div className="flex flex-col">
            <span className="text-sm font-medium text-neutral-900">
              {storeName}
            </span>
            <span className="text-xs text-neutral-600">{storeAddress}</span>
          </div>
        </div> */}
        {/* </div> */}
      </div>
    </div>
  );
};
