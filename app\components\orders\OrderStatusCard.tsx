import { FC, useRef, useState } from "react";
import { Order, OrderStatus, OrderItem } from "~/types";
import dayjs from "dayjs";
import TimelineHeader from "./TimelineHeader";
import TimelineDeliveryStatus from "./TimelineDeliveryStatus";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { Download, SearchIcon } from "lucide-react";
import InvoicePdf from "~/components/invoice/InvoicePdf";
import { getEstDeliveryTime, IsValidHour } from "~/utils/format";

interface OrderStatusCardProps {
  order: Order;
}

// Simple mapping of status codes to user-friendly messages
export const ORDERSTATUS_MESSAGES = {
  Created: "Order Recieved!",
  Accepted: "Preparing your order",
  Packed: "Order Ready!",
  Assigned: "Waiting for pickup",
  PickedUp: "Out for delivery",
  Dispatched: "Out for delivery",
  Delivered: "Order Delivered!",
  Cancelled: "Order cancelled"
};
export const LOGSTATUS_MESSAGES = {
  LOG_CREATED:
    "We will assign a delivery partner when the order is about ready.",
  LOG_ACCEPTED: "Looking for a delivery partner.",
  LOG_PENDING: "Looking for a delivery partner.",
  LOG_SEARCHING_AGENT: "Looking for Delivery Partner.",
  LOG_AGENT_ASSIGNED: "is on the way to restaurant",
  LOG_AT_PICKUP: "Arrived at restaurant",
  LOG_PICKED_UP: "is on the way to deliver your order",
  LOG_REACHED_LOCATION: "is reaching your doorstep shortly",
  LOG_DELIVERED: "Delivered your order",
  LOG_RTO_INITIATED: "Return initiated",
  LOG_CANCELLED: "Cancelled order"
};

// Define the Order flow
export const ORDER_FLOW = [
  "Created",
  "Accepted",
  "Packed",
  "Assigned",
  "PickedUp",
  "Dispatched",
  "Delivered",
  "Cancelled"
];
// Define the Logistic flow
export const LOGISTIC_FLOW = [
  "LOG_CREATED",
  "LOG_ACCEPTED",
  "LOG_PENDING",
  "LOG_SEARCHING_AGENT",
  "LOG_AGENT_ASSIGNED",
  "LOG_AT_PICKUP",
  "LOG_PICKED_UP",
  "LOG_REACHED_LOCATION",
  "LOG_DELIVERED",
  "LOG_RTO_INITIATED",
  "LOG_CANCELLED"
];

const OrderStatusCard: FC<OrderStatusCardProps> = ({ order }) => {
  const status = order.status as OrderStatus;
  const isDelivered = status === "Delivered";
  const isCancelled = status === "Cancelled";

  // Add state to control rendering the invoice for PDF generation
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  // Add loading state for better UX
  const [isLoading, setIsLoading] = useState(false);

  // Use useRef instead of the hook
  const pdfRef = useRef<HTMLDivElement>(null);

  const getTimeDisplay = () => {
    const time = order.deliveryTime
      ? `${order.deliveryDate} ${order.deliveryTime}`
      : `${order.deliveryDate} ${order.estDeliveryTime}`;

    if (isDelivered) {
      return {
        label: "Delivered on",
        time:
          order.deliveryTime || IsValidHour(order.estDeliveryTime)
            ? dayjs(time).format("DD MMM, h:mm A")
            : `${order.estDeliveryTime}`
      };
    }

    if (isCancelled) {
      return {
        label: "Cancelled ",
        time: ""
      };
    }

    return {
      label: "Arriving by",
      time: order.estDeliveryTime
    };
  };

  const timeDisplay = getTimeDisplay();
  const { networkConfig } = useAppConfigStore();

  const handleDownloadInvoice = async () => {
    try {
      setIsLoading(true);
      // Set the state to render the invoice
      setIsGeneratingPdf(true);

      // Wait a moment for the component to render
      setTimeout(async () => {
        try {
          // Dynamically import the generatePDF function only when needed
          const { default: generatePDF } = await import("react-to-pdf");

          // Use generatePDF directly with the ref
          await generatePDF(pdfRef, {
            filename: `invoice-${order.id}.pdf`,
            page: {
              margin: 10,
              format: "a4",
              orientation: "portrait"
            }
          });
        } catch (error) {
          console.error("Error generating PDF:", error);
        } finally {
          // Hide the invoice again
          setIsGeneratingPdf(false);
          setIsLoading(false);
        }
      }, 100);
    } catch (error) {
      setIsLoading(false);
      setIsGeneratingPdf(false);
      console.error("Error loading PDF generator:", error);
    }
  };

  if (isDelivered || isCancelled) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4 flex flex-col gap-2">
        <TimelineHeader
          sellerLogo={networkConfig?.businessLogo}
          sellerName={order.sellerName}
          sellerAddress={order.farmers[0]?.sellerAddress}
        />
        <div className="border-b border-neutral-200"></div>
        <TimelineDeliveryStatus
          label={timeDisplay.label}
          time={timeDisplay.time}
          status={status}
        />
        {status === "Delivered" && (
          <div className="flex justify-end -mb-1">
            <button
              className="text-teal-600 text-xs flex items-center space-x-1 border border-neutral-400 rounded-[.25rem] p-1"
              onClick={handleDownloadInvoice}
              disabled={isLoading}
            >
              <Download size={12} />
              <span>{isLoading ? "Generating..." : "Download Invoice"}</span>
            </button>
          </div>
        )}

        {/* Render the invoice component in a way that's conditionally visible but out of the normal flow */}
        {isGeneratingPdf && (
          <div
            style={{
              position: "fixed",
              left: "-9999px",
              top: 0,
              width: "800px",
              height: "auto",
              backgroundColor: "white",
              zIndex: -1000
            }}
          >
            <InvoicePdf
              ref={pdfRef}
              orderDetails={order}
              items={
                order.farmers
                  .flatMap((farmer) => farmer.items || [])
                  .map((item) => ({
                    itemName: item.itemName,
                    itemUrl: item.itemUrl,
                    qty: item.qty,
                    price: item.price,
                    amount: item.amount,
                    status: item.status,
                    unit: item.unit,
                    cancelledQty: item.cancelledQty,
                    returnedQty: item.returnedQty,
                    diet: item.diet,
                    strikeOffAmount: item.strikeOffAmount,
                    itemTaxAmount: item.itemTaxAmount
                  })) as Partial<OrderItem>[]
              }
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <>
      {/* Map */}
      {/* <div className="relative w-full overflow-visible">
        <div className="mx-[-1rem] mt-[-1rem] bg-white h-[260px]">
          <iframe
            className="w-full h-full border-0"
            loading="lazy"
            allowFullScreen
            referrerPolicy="no-referrer-when-downgrade"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.0198803440084!2d-122.42177858467916!3d37.77492977975943!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8085809c5d736345%3A0x2ef6d0fa434eef5e!2sTwitter%20HQ!5e0!3m2!1sen!2sus!4v1681853141290!5m2!1sen!2sus"
          ></iframe>
        </div>
      </div> */}

      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-row justify-between gap-4">
          <div>
            <h2 className="text-xl font-bold tracking-wide">
              {ORDER_FLOW.indexOf(status) >= ORDER_FLOW.indexOf("PickedUp") &&
              LOGISTIC_FLOW.indexOf(order.logStatus) >=
                LOGISTIC_FLOW.indexOf("LOG_REACHED_LOCATION")
                ? "Arrived at location"
                : ORDERSTATUS_MESSAGES[status] || "Order recieved!"}
            </h2>

            {ORDER_FLOW.indexOf(status) >= ORDER_FLOW.indexOf("PickedUp") &&
            LOGISTIC_FLOW.indexOf(order.logStatus) >=
              LOGISTIC_FLOW.indexOf("LOG_REACHED_LOCATION") ? (
              <div className="mt-1">
                <p className="text-typography-500 text-sm line-clamp-2 break-all before:content-[''] before:absolute before:-left-5 before:top-2 before:w-1.5 before:h-1.5 before:rounded-full before:bg-neutral-700">
                  {order.buyerAddress}
                </p>
              </div>
            ) : (
              <div className="relative pl-5 mt-1">
                <div className="relative">
                  <p className=" text-typography-500 text-sm before:content-[''] before:absolute before:-left-5 before:top-2 before:w-1.5 before:h-1.5 before:rounded-full before:bg-neutral-700">
                    {order.sellerName}
                  </p>
                </div>
                <div className="relative h-4">
                  <div className="before:content-[''] before:absolute before:-left-[1.14rem] before:-top-1 before:w-0.5 before:-bottom-1.5 before:rounded-full before:bg-neutral-500"></div>
                </div>
                <div className="relative">
                  <p className="text-typography-500 text-sm line-clamp-2 break-all before:content-[''] before:absolute before:-left-5 before:top-2 before:w-1.5 before:h-1.5 before:rounded-full before:bg-neutral-700">
                    {order.buyerAddress}
                  </p>
                </div>
              </div>
            )}
          </div>
          <div>
            <div className="mt-1 px-5 py-2.5 rounded-lg flex flex-col items-center justify-center text-white bg-gradient-to-b from-[#00A38F] to-[#007661]">
              <p className="text-[1.625rem] font-extrabold leading-8">
                {getEstDeliveryTime(order.estDeliveryTime)}
              </p>
              <p className="leading-4">mins</p>
            </div>
          </div>
        </div>

        <div>
          {LOGISTIC_FLOW.indexOf(order.logStatus) <
          LOGISTIC_FLOW.indexOf("LOG_AGENT_ASSIGNED") ? (
            <div className="flex flex-row items-center gap-3 mt-6 pt-4 border-t border-gray-100">
              <div className="p-3 bg-gray-100 rounded-md">
                <SearchIcon className="w-5 h-5 text-primary" />
              </div>
              <div>
                <p className="text-sm font-semibold text-typography-600">
                  Looking for a delivery partner.
                </p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-100 overflow-hidden mr-3 flex items-center justify-center">
                  <img
                    src="/delivery_boy.svg"
                    alt={order.delPartnerName}
                    className="w-10 h-10 object-contain"
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {order.delPartnerName || "Delivery Partner"}
                  </p>
                  <p className="text-xs text-gray-500">
                    {LOGSTATUS_MESSAGES[order.logStatus]}
                  </p>
                </div>
              </div>
              {order.delPartnerNumber && (
                <button
                  onClick={() =>
                    window.open(`tel:${order.delPartnerNumber}`, "_self")
                  }
                  className="w-10 h-10 rounded-full flex items-center justify-center text-teal-600 bg-teal-50"
                >
                  <svg
                    className="w-5 h-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default OrderStatusCard;
