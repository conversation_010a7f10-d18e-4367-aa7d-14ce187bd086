import React, { useState } from "react";
import clsx from "clsx";

interface SupplierBadgeProps {
  supplier: string;
  className?: string;
}

const SupplierBadge: React.FC<SupplierBadgeProps> = ({
  supplier,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);

  if (!supplier) return null;

  const handleClick = () => {
    setIsOpen((prev) => !prev);
  };

  return (
    <div className={clsx("relative inline-block", className)}>
      <div
        className={clsx(
          "inline-flex items-center px-3 py-1 rounded-full",
          "bg-purple-100 text-purple-700 font-medium text-sm",
          "hover:bg-purple-200 transition-colors duration-200 cursor-pointer",
          "max-w-xs truncate"
        )}
        title={supplier}
        onClick={handleClick}
      >
        <span className="truncate max-w-[200px]">by {supplier}</span>
      </div>

      {isOpen && (
        <div
          className={clsx(
            "absolute left-1/2 -translate-x-1/2 mt-2 z-30",
            "bg-white text-purple-700 text-sm",
            "px-3 py-1.5 rounded-md shadow-md whitespace-nowrap"
          )}
        >
          {supplier}
        </div>
      )}
    </div>
  );
};

export default SupplierBadge;
