import dayjs from "dayjs";
import { SupportTicket } from "~/types/user";

interface SupportTicketProps {
  ticketDetails: SupportTicket;
}

export default function SupportTicketCard({
  ticketDetails
}: SupportTicketProps) {
  return (
    <div className="flex flex-row justify-between bg-white w-full p-3 mt-4 rounded-md">
      <div className="flex flex-col w-full">
        <span className="text-sm font-medium">{`${ticketDetails.ticketId} - ${
          ticketDetails.orderGroupId
            ? `Order #${ticketDetails.orderGroupId}`
            : ticketDetails.ticketType.toUpperCase()
        }`}</span>
        {ticketDetails.description && (
          <span className="font-light text-xs text-gray-500 mt-1">
            {ticketDetails.description}
          </span>
        )}
        <span className="font-light text-xs text-gray-500 mt-1">{`${dayjs(
          ticketDetails.createdDate
        ).format("hh:mm A | DD MMM")}`}</span>
      </div>
      <div>
        <span
          className={` ${
            ticketDetails.status === "CLOSED"
              ? "bg-gray-100 text-gray-400"
              : "bg-green-200 text-green-500"
          } px-2 py-1 text-xs font-normal rounded-md tracking-wider`}
        >
          {ticketDetails.status}
        </span>
      </div>
    </div>
  );
}
