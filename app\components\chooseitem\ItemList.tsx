import React, { useCallback } from "react";

import { AvailableItem, Cart, ImageViewType } from "~/types";
import ItemCard from "../ItemCard";
import ItemCardV2 from "../ItemCardV2";
import RestaurantItemCard from "../restaurant/RestaurantItemCard";
interface ItemListProps {
  approxPricing: boolean;
  items: AvailableItem[];
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  imageViewType: ImageViewType;
}
export const ItemList: React.FC<ItemListProps> = ({
  approxPricing,
  items,
  cart,
  onAddItem,
  onRemoveItem,
  imageViewType
}) => {
  const getItemList = useCallback(() => {
    const itemMap = new Map<string, AvailableItem[]>();
    items.forEach((item) => {
      if (itemMap.has(item.varGroup)) {
        itemMap.get(item.varGroup)?.push(item);
      } else {
        itemMap.set(item.varGroup, [item]);
      }
    });
    return Array.from(itemMap.values()) || [];
  }, [items]);

  // const [itemsList, setItemsList] = useState<AvailableItem[][]>(getItemList());

  if (imageViewType == "GRID") {
    return (
      <div
        // className={`grid gap-2 px-2 ${
        //   imageViewType === "GRID" ? "grid-cols-2" : "grid-cols-1"
        // }`}
        className={`grid gap-2 px-2 grid-cols-2`}
      >
        {getItemList().map((item, index) => (
          <ItemCardV2
            key={index}
            itemDetailsList={item}
            cart={cart}
            amount={item.reduce(
              (acc, it) => acc + cart[it.sellerItemId]?.amount || 0,
              0
            )}
            approxPricing={approxPricing}
            onAdd={onAddItem}
            onRemove={onRemoveItem}
          />
        ))}
      </div>
    );
  } else if (imageViewType === "GALLERY") {
    return (
      <div className={`grid gap-2 px-2 grid-cols-1`}>
        {getItemList().map((item, index) => (
          <ItemCardV2
            key={index}
            itemDetailsList={item}
            cart={cart}
            amount={item.reduce((acc, it) => {
              const cartItem = cart[it.sellerItemId];
              return (
                acc +
                ((it.orderedAmount || 0) +
                  (it.pricePerUnit || 0) *
                    ((cartItem?.qty || 0) - (it?.orderedQty || 0)))
              );
            }, 0)}
            approxPricing={approxPricing}
            onAdd={onAddItem}
            onRemove={onRemoveItem}
          />
        ))}
      </div>
    );
  } else if (imageViewType === "LIST") {
    return (
      <div className={`flex flex-col w-full gap-2 px-3`}>
        {getItemList().map((item, index) => (
          <ItemCard
            key={index}
            itemDetailsList={item}
            cart={cart}
            amount={item.reduce((acc, it) => {
              const cartItem = cart[it.sellerItemId];
              return (
                acc +
                ((it.orderedAmount || 0) +
                  (it.pricePerUnit || 0) *
                    ((cartItem?.qty || 0) - (it?.orderedQty || 0)))
              );
            }, 0)}
            approxPricing={approxPricing}
            onAdd={onAddItem}
            onRemove={onRemoveItem}
          />
        ))}
      </div>
    );
  } else if (imageViewType === "R-VIEW") {
    return (
      <div className={`flex flex-col w-full gap-2 p-1`}>
        {getItemList().map((item, index) => (
          <>
          <RestaurantItemCard
            key={index}
            itemDetailsList={item}
            cart={cart}
            amount={item.reduce((acc, it) => {
              const cartItem = cart[it.sellerItemId];
              return (
                acc +
                ((it.orderedAmount || 0) +
                  (it.pricePerUnit || 0) *
                    ((cartItem?.qty || 0) - (it?.orderedQty || 0)))
              );
            }, 0)}
            approxPricing={approxPricing}
            onAdd={onAddItem}
            onRemove={onRemoveItem}
            isLast={index === getItemList().length - 1}
          />
          {!(index === getItemList().length - 1) ? (<div className="border border-dashed border-neutral-300"></div>):(<div className="border border-dotted border-white"></div>) } </>
        ))}
      </div>
    );
  }
};
