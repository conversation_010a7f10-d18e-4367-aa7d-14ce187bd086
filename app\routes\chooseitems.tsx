import DeliveryInfo from "./../components/chooseitem/DeliveryInfo";
import React, {
  useState,
  useMemo,
  useEffect,
  useCallback,
  useRef
} from "react";
import {
  LoaderFunction,
  json,
  redirect,
  ActionFunction
} from "@remix-run/node";
import {
  useLoaderData,
  useNavigate,
  useFetcher,
  useRouteError,
  isRouteErrorResponse,
  ShouldRevalidateFunction
} from "@remix-run/react";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);

import MoQPopup from "@components/MoQPopup";
import RefreshButton from "@components/RefreshButton";
import Button from "@components/Button";

import { getItemOptionsAPI, precheckOrderAPI } from "@services/buyer.service";
import {
  getSession,
  destroySession,
  commitSession
} from "@utils/session.server";
import {
  User,
  ItemOptionsData,
  AvailableItem,
  PrecheckOrderPayload,
  PrecheckOrderResponse,
  Cart,
  ImageViewType
} from "~/types";
// import ScrollViewWithRefresh from "@components/ScrollViewWithRefresh";
import { BackNavHeader } from "~/components/BackNavHeader";
// import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken } from "~/types/user";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { useUser } from "~/contexts/userContext";
import { removeAllInvalidCarts, removeItem } from "~/utils/localStorage";
// import CategoryItemList from "~/components/chooseitem/CategoryItemList";
import InfoModel from "~/components/models/InfoModel";
import { useAppConfigStore } from "~/stores/appConfig.store";
import WhatsappCTA, { handleWhatsappClick } from "~/components/WhatsappCTA";
import CategoryItemListL3 from "~/components/chooseitem/CategoryItemListL3";
import { chooseitemsStore } from "~/stores/chooseitems.store";
// import useDebounce from "~/hooks/useDebounce";
import Banners from "~/components/Banners";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { ChevronRight } from "lucide-react";
import SearchWithCardView from "~/components/chooseitem/SearchWithCardView";
import { useCartStore } from "~/stores/cart.store";
import { useRequireAuth } from "~/hooks/useRequireAuth";
import { NetworkAsset } from "~/components/NetworkAssests";
import InstallPWAButton from "~/components/InstallPWAButton";
interface LoaderData {
  itemOptionsData: ItemOptionsData;
  user: User;
  mobileNumber: string;
}

interface LoaderErrorData {
  error: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  console.log("Loader called for chooseitems.tsx");

  const url = new URL(request.url);
  const deliveryDate = url.searchParams.get("deliveryDate") || undefined;
  const sellerId = url.searchParams.get("sellerId");
  const redirectedFromBuyer =
    (url.searchParams.get("redirected") as unknown as boolean) || false;

  const categoryId = url.searchParams.get("categoryId");
  const matchBy = url.searchParams.get("matchBy")?.trim();
  const parentCategoryId = url.searchParams.get("parentCategoryId");

  // console.log("DeliveryDate:", deliveryDate, sellerId, categoryId);

  if (!access_token || !user) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/login?redirectTo=${request.url}`, { headers });
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      const headers = new Headers();
      headers.append("Set-Cookie", await destroySession(session));
      session = await getSession();
      session.set("appConfig", { appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return redirect(`/login?redirectTo=${request.url}`, { headers });
    }

    const itemOptions = await getItemOptionsAPI(
      redirectedFromBuyer ? decoded.userDetails.mobileNumber : null,
      request,
      deliveryDate,
      Number(sellerId),
      categoryId ? Number(categoryId) : undefined,
      matchBy ?? undefined,
      parentCategoryId ? Number(parentCategoryId) : undefined
    );

    const itemOptionsData = itemOptions.data;

    if (!itemOptionsData) {
      throw json({}, { status: 400, statusText: "NO_ITEM" });
    }

    return createClientResponse<LoaderData, ItemOptionsData>(
      request,
      {
        itemOptionsData,
        user,
        mobileNumber: decoded.userDetails.mobileNumber
      },
      itemOptions
    );
  } catch (error) {
    console.error("Error fetching item options:", error);
    if (error instanceof Response && error.statusText === "NO_ITEM") {
      throw json({}, { status: 400, statusText: "NO_ITEM" });
    }
    return json({}, { status: 400, statusText: "Items are not available" });
  }
};

// Updated Action Function to handle CONFIRM button submission via Fetcher
export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;
  // const url = new URL(request.url);

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token || !user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const cartData = formData.get("cart");
  const deliveryDate = formData.get("deliveryDate") as string;
  const sellerId = formData.get("sellerId") as string;
  const codAllowed = formData.get("codAllowed") === "true";
  const sellerDataId = formData.get("sellerDataId") as string;
  const existingOrderGroupId =
    (formData.get("existingOrderGroupId") !== null &&
      Number(formData.get("existingOrderGroupId"))) ||
    undefined;
  const cartKey = formData.get("cartKey") as string;
  const preconfirmUid = formData.get("preconfirmUid") as string;

  if (!cartData || typeof cartData !== "string") {
    return json({ error: "Invalid cart data" }, { status: 400 });
  }

  let cart: Cart;
  try {
    cart = JSON.parse(cartData);
  } catch (error) {
    console.error("Error parsing cart data:", error);
    return json({ error: "Invalid cart data format" }, { status: 400 });
  }

  // Prepare items array for API
  const itemsForAPI: PrecheckOrderPayload["items"] = Object.values(cart).map(
    (cartItem) => ({
      inventoryId: Number(sellerDataId),
      sellerId: Number(sellerId),
      sellerItemId: cartItem.itemId, // Adjust if sellerItemId is different
      pricePerUnit: cartItem.amount / cartItem.qty,
      quantity: cartItem.qty
    })
  );

  // Prepare payload
  const payload: PrecheckOrderPayload = {
    sellerInventoryId: Number(sellerDataId),
    buyerId: user.buyerId,
    deliveryDate,
    sellerId: Number(sellerId),
    codOpted: codAllowed,
    items: itemsForAPI,
    legacy: true,
    moneyCollectionId: 0,
    existingOrderGroupId,
    cartKey,
    fulfillmentType: "DELIVERY"
  };

  if (preconfirmUid) {
    payload.preconfirmUid = preconfirmUid;
  }

  try {
    // console.log("Prechecking order with payload:", JSON.stringify(payload));
    const response = await precheckOrderAPI(payload, request);
    // console.log("Precheck Order Response:", JSON.stringify(response));

    // Return the order data as JSON
    return createClientResponse<PrecheckOrderResponse, PrecheckOrderResponse>(
      request,
      response.data,
      response
    );
  } catch (error) {
    console.error("Error prechecking order:", error);
    return json({ error: "Failed to confirm order" }, { status: 500 });
  }
};

export const shouldRevalidate: ShouldRevalidateFunction = ({
  actionResult,
  defaultShouldRevalidate
}) => {
  // console.log("actionResult", actionResult);
  if (actionResult && actionResult.buyerId) {
    console.log("actionResult is not null");
    return false;
  }
  return defaultShouldRevalidate;
};

const ChooseItems: React.FC = () => {
  const navigate = useNavigate();

  const loader = useLoaderData<LoaderData & LoaderErrorData>();
  const { error } = loader;

  const {
    itemOptionsData,
    setItemOptionsData,
    searchPage,
    setSearchPage,
    categoryType,
    setCategoryType,
    setLoading,
    selectedParentCategory,
    categoryTypeList,
    isScrolled,
    setIsScrolled,
    imageViewType,
    setImageViewType
  } = chooseitemsStore((state) => state);

  const {
    cart,
    addItem: addToCart,
    removeItem: removeFromCart,
    syncCart,
    clearCart
  } = useCartStore((state) => state);

  const { authRequired } = useRequireAuth();

  // Ensure cart is initialized only when cartKey changes
  useEffect(() => {
    if (
      loader.itemOptionsData?.availableItems &&
      loader.itemOptionsData?.cartKey
    ) {
      syncCart(
        loader.itemOptionsData.availableItems,
        loader.itemOptionsData.cartKey
      );
      removeItem("currentOrder");
    }

    if (loader.itemOptionsData) {
      setItemOptionsData(loader.itemOptionsData);
      removeItem("imageViewType");
    }

    if (loader.itemOptionsData?.cartKey) {
      removeAllInvalidCarts(loader.itemOptionsData?.cartKey);
      removeItem("currentOrder");
    }
    if (!imageViewType) {
      setImageViewType(
        loader?.itemOptionsData?.networkType === "B2B" ? "LIST" : "GRID"
      );
    }
  }, [loader.itemOptionsData?.availableItems, loader.itemOptionsData?.cartKey]); // Only depend on cartKey changes

  const [isSearchSticky, setIsSearchSticky] = useState(false);
  const [isSRPSticky, setIsSRPSticky] = useState(false);
  const [showMoq, setShowMoq] = useState(false);
  const [showMov, setShowMov] = useState(false);
  const [showBackConfirmation, setShowBackConfirmation] = useState(false);
  const { user, setUser } = useUser();
  const [searchStr, setSearchStr] = useState("");

  // The entire page container
  const pageContainerRef = useRef<HTMLDivElement>(null);

  // The sentinel (a small, invisible div) placed **just above** your item list
  // so we can observe when the search bar hits the top.
  const searchBarSentinelRef = useRef<HTMLDivElement>(null);
  const itemSRPSentinelRef = useRef<HTMLDivElement>(null);

  // Example: IntersectionObserver to detect when the search bar crosses the header
  useEffect(() => {
    const sentinel = searchBarSentinelRef.current;
    console.log(sentinel);
    if (!sentinel || !pageContainerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        // If NOT intersecting => search bar is above => stick it
        console.log(entries);
        setIsSearchSticky(!entry.isIntersecting);
      },
      {
        threshold: 0,
        root: pageContainerRef.current, // measure intersection inside the scrolling div
        rootMargin: `-8px`
      }
    );

    observer.observe(sentinel);
    return () => observer.disconnect();
  }, [searchBarSentinelRef.current]);

  /** 2) For Nested Item List Scroll (SRP) */
  useEffect(() => {
    const sentinel = itemSRPSentinelRef.current;
    if (!sentinel || !pageContainerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        // If NOT intersecting => we've scrolled past the top of the item listing => nest scroll
        setIsSRPSticky(!entry.isIntersecting);
      },
      {
        threshold: 0,
        root: pageContainerRef.current,
        rootMargin: `-72px`
      }
    );

    observer.observe(sentinel);
    return () => observer.disconnect();
  }, [itemSRPSentinelRef.current]);

  const { appSource, networkConfig } = useAppConfigStore((state) => state);
  useEffect(() => {
    if (itemOptionsData?.existingOrderGroupId) {
      setUser({ existingOrderGroupId: itemOptionsData?.existingOrderGroupId });
    }
  }, [itemOptionsData?.existingOrderGroupId, setUser]);

  const itemMap = new Map<number, AvailableItem>();
  itemOptionsData?.availableItems?.forEach((item) => {
    itemMap.set(item.sellerItemId, item);
  });

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fetcher = useFetcher<PrecheckOrderResponse | { error: string }>();

  // Handle the fetcher response
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if ("error" in fetcher.data) {
        setErrorMessage(fetcher.data.error);
      } else {
        // Navigate to /cart
        navigate(`/cart?approxPricing=${itemOptionsData?.approxPricing}`, {
          state: {
            order: fetcher.data
          }
        });
      }
    }
  }, [fetcher.state, fetcher.data, navigate, itemOptionsData?.approxPricing]);

  const showBanner = useCallback(() => {
    let showBanner = false;
    if (
      (categoryType === "L0" || categoryType === "L1") &&
      !(categoryTypeList.includes("L2") || categoryTypeList.includes("L3"))
    ) {
      showBanner = true;
    }

    if (categoryType === "L2" || categoryType === "L3") {
      showBanner = true;
    }

    return showBanner;
  }, [categoryType]);

  const handleSelectImageView = (value: ImageViewType) => {
    setImageViewType(value);
  };

  const handleBack = () => {
    setIsScrolled(false);

    if (searchStr.length > 0) {
      handleSearchClear();
      return;
    }

    // if (appSource === "whatsappchat") {
    //   setShowBackConfirmation(true);
    //   return;
    // }

    performBackNavigation(false);
  };

  const performBackNavigation = (goToWA: boolean = false) => {
    if (
      categoryType === "L1" &&
      (categoryTypeList.includes("L2") || categoryTypeList.includes("L3"))
    ) {
      navigate("/chooseitems");
    } else if (appSource === "whatsappchat") {
      if (goToWA) {
        handleWhatsappClick(networkConfig?.wabMobileNumber || "");
      } else {
        navigate("/chooseitems");
      }
      console.log("whatsapp back nav called");
    // } else if (networkConfig?.multiSeller && itemOptionsData?.deliveryDate) {
    //   navigate(
    //     `/selectseller?deliveryDate=${encodeURIComponent(
    //       itemOptionsData?.deliveryDate
    //     )}`
    //   );
    } else {
      navigate("/home/<USER>");
    }
  };

  const handleSelectL1 = (categoryId: number) => {
    setCategoryType("L1");
    setLoading(true);

    const queryParams = new URLSearchParams();
    queryParams.append("intent", "L1");
    queryParams.append("categoryId", categoryId.toString());

    if (itemOptionsData?.deliveryDate) {
      queryParams.append("deliveryDate", itemOptionsData.deliveryDate);
    }

    if (itemOptionsData?.sellerId) {
      queryParams.append("sellerId", itemOptionsData.sellerId.toString());
    }

    if (selectedParentCategory?.id) {
      queryParams.append(
        "parentCategoryId",
        selectedParentCategory.id.toString()
      );
    }

    navigate(`/chooseitems?${queryParams.toString()}`);
  };

  const getButtonText = () => {
    if (categoryType === "L1" && selectedParentCategory?.name) {
      return selectedParentCategory.name;
    } else {
      return "Add Items";
    }
  };

  const handleSearchClear = () => {
    const queryParams = new URLSearchParams();
    queryParams.append("intent", "L1");

    if (itemOptionsData?.deliveryDate) {
      queryParams.append("deliveryDate", itemOptionsData.deliveryDate);
    }

    if (itemOptionsData?.sellerId) {
      queryParams.append("sellerId", itemOptionsData.sellerId.toString());
    }

    if (searchPage === "L1" && selectedParentCategory?.id) {
      queryParams.append("categoryId", selectedParentCategory.id.toString());
    }

    navigate(`/chooseitems?${queryParams.toString()}`, { replace: true });
    setSearchStr("");
  };

  const showConfirmButton = useMemo(() => {
    return (
      Object.values(cart).length > 0 &&
      itemOptionsData &&
      itemOptionsData?.availableItems &&
      itemOptionsData?.availableItems?.length > 0
    );
  }, [cart, itemOptionsData]);

  const handleRefresh = async () => {
    if (itemOptionsData?.cartKey) {
      clearCart(itemOptionsData.cartKey);
    }
    setIsRefreshing(true);
    try {
      navigate(0);
    } catch (err) {
      console.error("Error refreshing item options:", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleAddItem = (item: AvailableItem) => {
    if (itemOptionsData?.cartKey) {
      addToCart(item, itemOptionsData.cartKey);
    }
  };

  const handleRemoveItem = (item: AvailableItem) => {
    if (itemOptionsData?.cartKey) {
      removeFromCart(item, itemOptionsData.cartKey);
    }
  };

  const handleMinQuantityAndPrice = () => {
    const minQty: boolean =
      Object.values(cart).reduce((acc, item) => {
        const itemDetails = itemMap.get(item.itemId);
        if (itemDetails && itemDetails.unitWtFactor) {
          return acc + item.qty * itemDetails.unitWtFactor;
        }
        return acc + item.qty;
      }, 0) < itemOptionsData!.minOrderQty;
    const minPrice: boolean =
      Object.values(cart).reduce((acc, item) => acc + item.amount, 0) <
      itemOptionsData!.minOrderValue;

    if (minQty) {
      setShowMoq(true);
      return true;
    }
    if (minPrice) {
      setShowMov(true);
      return true;
    }

    return minQty || minPrice;
  };

  // Add event listener for browser back button
  useEffect(() => {
    if (appSource !== "whatsappchat") return;

    // Flag to track if this is the initial page load
    let isInitialLoad = true;

    // Handle the browser's back button press
    const handlePopState = () => {
      // Skip on initial page load to avoid false positives
      if (isInitialLoad) {
        isInitialLoad = false;
        return;
      }

      console.log(categoryTypeList);

      const shouldBackNavigation =
        appSource === "whatsappchat" &&
        (((categoryTypeList.includes("L2") ||
          categoryTypeList.includes("L3")) &&
          (categoryType === "L2" || categoryType === "L3")) ||
          (!categoryTypeList.includes("L3") &&
            (categoryType === "L0" || categoryType === "L1")));

      if (shouldBackNavigation) {
        // Push state again to prevent actual navigation
        window.history.pushState(
          { page: "chooseitems" },
          document.title,
          window.location.href
        );

        // Show confirmation dialog
        setShowBackConfirmation(true);
      }
    };

    // Add the event listener for popstate (browser back button)
    window.addEventListener("popstate", handlePopState);

    // Push a state into the history so popstate can be triggered when back is pressed
    setTimeout(() => {
      window.history.pushState(
        { page: "chooseitems" },
        document.title,
        window.location.href
      );
      isInitialLoad = false;
    }, 0);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [appSource, categoryType]);

  if (authRequired) {
    return (
      <div className="flex justify-center h-screen">
        <NetworkAsset assetName="loginBanner" />
      </div>
    );
  }

  if (itemOptionsData?.avStatus !== "ok") {
    return (
      // <InfoModel
      //   title="Sorry!"
      //   message={
      //     itemOptionsData?.avStatus == "bookingClosed"
      //       ? "We're currently not accepting orders. Please check back later."
      //       : "Your address is currently outside our delivery area. You can update your address"
      //   }
      //   buttonType="primary"
      //   buttonText={
      //     itemOptionsData?.avStatus === "bookingClosed"
      //       ? "Go Back"
      //       : "Change Address"
      //   }
      //   specialCase={itemOptionsData?.avStatus == "bookingClosed" ? "BookingClosed":""}
      //   countdownStart={30}
      //   onRedirect={
      //     itemOptionsData?.avStatus === "bookingClosed"
      //       ? handleBack
      //       : () => navigate("/changeaddress")
      //   }
      // />

      itemOptionsData?.avStatus === "bookingClosed" ? (
        <InfoModel
          title="Sorry, we're closed!"
          message="We're currently not accepting orders online. We'll be back soon."
          buttonType="secondary"
          buttonText="Go Back"
          specialCase="BookingClosed"
          onClose={handleBack}
          isCountdownRedirectionAllowed={false}
        />
      ) : itemOptionsData?.avStatus === "notInServiceArea" ? (
        <InfoModel
          title="Sorry, we're not there yet!"
          message="We're not available at your location at the moment. Please try a different location."
          buttonType="primary"
          buttonText="Try Changing Location"
          specialCase="OutofServiceArea"
          onClose={() => navigate("/changeaddress")}
          isCountdownRedirectionAllowed={false}
        />
      ) : (
        <InfoModel
          title="Sorry!"
          message="Something went wrong. We will be back soon."
          buttonType="primary"
          buttonText="Go Back"
          onRedirect={handleBack}
        />
      )
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-white">
        <span className="text-red-500 text-lg">{error}</span>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-screen w-full bg-gray-100`}>
      {fetcher.state !== "idle" && <SpinnerLoader loading={true} size={12} />}
      {/* Back Confirmation Modal */}
      {showBackConfirmation && (
        <InfoModel
          title="Leave this page?"
          message="Are you sure you want to leave this page? Your progress will not be saved."
          buttonType="primary"
          buttonText="Stay on Page"
          onClose={() => {
            setShowBackConfirmation(false);
          }}
          onRedirect={() => {
            setShowBackConfirmation(false);
            performBackNavigation(true);
          }}
          countdownStart={5}
          isCountdownRedirectionAllowed={false}
          specialCase="BackNavigation"
        />
      )}
      <div className="transition-all duration-500">
      <InstallPWAButton 
         themeColor={isSearchSticky ? "Primary":"non-primary"}
         title="Get The App"
         subtitle="For Better Experience"
         />
      </div>
      {/* Header Section */}
      <div
        className={` sticky
          flex flex-col gap-0 top-0  transition-colors duration-1000
          ${isSearchSticky ? "bg-primary-50" : "bg-primary"}
        `}
      >
        <BackNavHeader
          backButton={(categoryTypeList.includes("L2") || categoryTypeList.includes("L3") || networkConfig?.ondcDomain === "RET10") ? true : false}
          buttonText={getButtonText()}
          handleBack={handleBack}
          className={`transition-colors duration-1000 ease-in-out pb-1 min-h-fit
              ${
                isSearchSticky
                  ? "bg-primary-50 text-primary shadow-none"
                  : "bg-primary text-white shadow-none"
              }`}
          pageName="SRP"
        />
      </div>
      <div
        ref={pageContainerRef}
        className={`overflow-auto h-fit no-scrollbar`}
      >
        <div className="flex flex-col">
          {/* Seller Information */}
          <div
            className={`transition-colors duration-1000 ease-in-out ${
              isSearchSticky
                ? "bg-primary-50 text-primary"
                : "bg-primary text-white "
            }`}
          >
            <DeliveryInfo
              deliveryDate={itemOptionsData?.deliveryDate || ""}
              deliveryTime={itemOptionsData?.deliveryTime || ""}
              address={itemOptionsData?.buyerAddress}
              defaultAddress={itemOptionsData?.defaultAddress}
              sellerLogo={itemOptionsData?.sellerLogo}
              sellerName={itemOptionsData?.sellerName}
              estDeliveryTime={itemOptionsData?.estDeliveryTime}
              displayDeliveryDate={itemOptionsData?.displayDeliveryDate}
              onAddressClick={() =>
                navigate(
                  "/select-address?flowType=select-address&returnTo=/chooseitems",
                  {
                    state: {
                      from: "chooseitems",
                      returnTo: "/chooseitems",
                      flowType: "select-address"
                    }
                  }
                )
              }
            />
          </div>
        </div>

        {/* Place a sentinel that we observe with IntersectionObserver.
                        It sits just above the search bar. */}
        <div
          ref={searchBarSentinelRef}
          className={`h-0 w-full transition-colors duration-1000 ${
            isSearchSticky ? "bg-primary-50" : "bg-primary "
          }`}
        ></div>
        {/* Search and Categories */}
        <div
          className={`sticky z-10 top-0 left-0 right-0 transition-colors duration-1000 ease-in-out
                        ${isSearchSticky ? " bg-primary-50" : "bg-primary"}`}
        >
          <SearchWithCardView
            isScrolled={isScrolled}
            categoryType={categoryType}
            imageViewType={imageViewType}
            searchStr={searchStr}
            setSearchPage={setSearchPage}
            onSearchSelect={(str: string) => {
              const queryParams = new URLSearchParams();
              queryParams.append("intent", "L1");
              if (itemOptionsData?.deliveryDate) {
                queryParams.append(
                  "deliveryDate",
                  itemOptionsData.deliveryDate
                );
              }
              if (itemOptionsData?.sellerId) {
                queryParams.append(
                  "sellerId",
                  itemOptionsData.sellerId.toString()
                );
              }
              queryParams.append("matchBy", str);

              navigate(`/chooseitems?${queryParams.toString()}`);
              setSearchStr(str);
            }}
            onSearchClear={handleSearchClear}
            onViewChange={handleSelectImageView}
          />
        </div>

        {/* Error message from order confirmation */}
        {errorMessage && (
          <div className="p-4 bg-red-100 text-red-700 text-center">
            {errorMessage}
          </div>
        )}
        {/* Banner */}
        {itemOptionsData?.sellerBanners?.length && showBanner() && (
          <div className={`transition-transform duration-1000`}>
            <Banners
              images={itemOptionsData.sellerBanners}
              className="px-4 py-2  bg-gray-100 shadow-lg"
            />
          </div>
        )}
        {/* Item Listing */}
        <div ref={itemSRPSentinelRef} className="w-full h-0"></div>
        <div
          className={`no-scrollbar`}
        >
          <CategoryItemListL3
            cart={cart}
            data={{ ...itemOptionsData }}
            onAddItem={handleAddItem}
            onRemoveItem={handleRemoveItem}
            searchStr={searchStr}
            imageViewType={imageViewType}
            handleSelectL1={handleSelectL1}
            allowScrolling={isSRPSticky && isSearchSticky ? true : false}
          />
        </div>
      </div>
      {/* Form to handle Confirm Order via Fetcher */}
      <div className={`${showConfirmButton ? "" : ""}`}>
        {showConfirmButton && (
          <div className="fixed inset-0 bg-transparent pointer-events-none">
            <fetcher.Form
              method="post"
              className="fixed bottom-0 left-0 right-0 p-4 w-100%  flex flex-col justify-center items-center"
              onSubmit={(event) => {
                if (handleMinQuantityAndPrice()) {
                  event.preventDefault();
                }
              }}
            >
              {/* item total and total items */}
              <Button className="pointer-events-auto flex self-center items-center w-[80%] justify-between rounded-2xl shadow-2xl bg-primary p-3">
                <div className="flex flex-col items-start">
                  <div
                    className={`flex gap-1 ${
                      itemOptionsData.displayPrices !== false ? "" : "my-2"
                    }`}
                  >
                    <span className="text-xs text-white">
                      {`${Object.values(cart).reduce((acc) => acc + 1, 0)} ${
                        Object.values(cart).reduce((acc) => acc + 1, 0) <= 1
                          ? "Item"
                          : "Items"
                      }`}
                    </span>
                    {itemOptionsData?.networkType === "B2B" && (
                      <>
                        <span className="border-r border-primary-100"></span>
                        <span className="text-xs text-white">{`${Object.values(
                          cart
                        )
                          .reduce((acc, item) => {
                            const itemDetails = itemMap.get(item.itemId);
                            if (itemDetails && itemDetails.unitWtFactor) {
                              return acc + item.qty * itemDetails.unitWtFactor;
                            }
                            return acc + item.qty;
                          }, 0)
                          .toFixed(0)} kg`}</span>
                      </>
                    )}
                  </div>
                  {itemOptionsData.displayPrices !== false && (
                    <span className="text-sm text-white">
                      {`₹ ${Object.values(cart)
                        .reduce((acc, item) => {
                          const itemDetails = itemMap.get(item.itemId);
                          if (itemDetails) {
                            return (
                              acc +
                              ((itemDetails?.orderedAmount || 0) +
                                (itemDetails?.pricePerUnit || 0) *
                                  (item.qty - (itemDetails?.orderedQty || 0)))
                            );
                          }
                          return acc + item.amount;
                        }, 0)
                        .toFixed(2)}`}
                    </span>
                  )}
                </div>
                {/* Hidden Inputs to Pass Necessary Data */}
                <input type="hidden" name="cart" value={JSON.stringify(cart)} />
                <input
                  type="hidden"
                  name="deliveryDate"
                  value={itemOptionsData?.deliveryDate}
                />
                <input
                  type="hidden"
                  name="sellerId"
                  value={itemOptionsData?.sellerId}
                />
                <input
                  type="hidden"
                  name="sellerDataId"
                  value={itemOptionsData?.inventoryId}
                />
                <input
                  type="hidden"
                  name="codAllowed"
                  value={itemOptionsData?.codAllowed ? "true" : "false"}
                />
                <input
                  type="hidden"
                  name="existingOrderGroupId"
                  value={user?.existingOrderGroupId}
                />
                <input
                  type="hidden"
                  name="cartKey"
                  value={itemOptionsData?.cartKey}
                />

                {/* confirm btn */}
                <div
                  // disabled={Object.values(cart).length === 0}
                  className={`flex text-white tracking-wider ${
                    Object.values(cart).length === 0
                      ? "cursor-not-allowed"
                      : " text-white"
                  }`}
                >
                  {fetcher.state === "submitting" ? "PROCEED..." : "PROCEED"}
                  <ChevronRight />
                </div>
              </Button>
            </fetcher.Form>
          </div>
        )}
      </div>
      {/* MoQ Popup */}
      <MoQPopup
        visible={showMoq || showMov}
        onClose={() => {
          setShowMoq(false);
          setShowMov(false);
        }}
        qty={itemOptionsData?.minOrderQty || 0}
        currentQty={Object.values(cart).reduce((acc, item) => {
          const itemDetails = itemMap.get(item.itemId);
          if (itemDetails && itemDetails.unitWtFactor) {
            return acc + item.qty * itemDetails.unitWtFactor;
          }
          return acc + item.qty;
        }, 0)}
        value={itemOptionsData?.minOrderValue || 0}
        showMoq={showMoq}
        showMov={showMov}
      />
    </div>
  );
};

export default ChooseItems;

export function ErrorBoundary() {
  const navigate = useNavigate();
  const error = useRouteError();
  console.log("error", isRouteErrorResponse(error));

  const { appSource, networkConfig } = useAppConfigStore((state) => state);

  const handleClose = () => {
    if (appSource === "whatsappchat") {
      handleWhatsappClick(networkConfig?.wabMobileNumber || "");
    } else {
      navigate("/home/<USER>");
    }
  };

  if (
    isRouteErrorResponse(error) &&
    error.statusText === "NO_ITEM" &&
    appSource === "whatsappchat"
  ) {
    return (
      <InfoModel
        title="Sorry, we're closed!"
        message="We're currently not accepting orders online. We'll be back soon."
        buttonType="secondary"
        buttonText="Go Back"
        specialCase="BookingClosed"
        onClose={handleClose}
        isCountdownRedirectionAllowed={false}
      />
    );
  }

  if (isRouteErrorResponse(error) && error.statusText === "NO_ITEM") {
    return (
      <InfoModel
        title="Sorry, we're closed!"
        message="We're currently not accepting orders online. We'll be back soon."
        buttonType="secondary"
        buttonText="Go Back"
        specialCase="BookingClosed"
        onClose={handleClose}
        isCountdownRedirectionAllowed={true}
      />
    );
  }

  return (
    <InfoModel
      title="Sorry!"
      message="Something went wrong. We will be back soon."
      buttonType="primary"
      buttonText="Go Back"
      onRedirect={handleClose}
    />
  );

  // return <ErrorBoundaryComponent onClose={handleClose} />;
}
