import { useMemo } from "react";
import { useCouponStore } from "~/stores/coupon.store";
import { CouponItem } from "./CouponItem";

export const CouponList = () => {
  const { coupons, searchText } = useCouponStore();

  const filteredCoupons = useMemo(() => {
    if (!searchText.trim()) {
      return coupons;
    }

    const searchLower = searchText.toLowerCase().trim();
    return coupons.filter(
      (coupon) =>
        coupon.code?.toLowerCase().includes(searchLower) ||
        coupon.title?.toLowerCase().includes(searchLower) ||
        coupon.description?.toLowerCase().includes(searchLower)
    );
  }, [coupons, searchText]);

  if (filteredCoupons.length === 0) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">
          No coupons found matching &ldquo;{searchText}&rdquo;
        </p>
      </div>
    );
  }

  return (
    <div className="py-2">
      {filteredCoupons.map((coupon) => (
        <CouponItem key={coupon.id} coupon={coupon} />
      ))}
    </div>
  );
};
