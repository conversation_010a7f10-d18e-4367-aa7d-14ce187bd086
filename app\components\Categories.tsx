// app/components/Categories.tsx

import React from 'react';

interface CategoriesProps {
    categories: string[];
    chooseCategory: (categoryIndex: number) => void;
    chosenCategory: number;
}

const Categories: React.FC<CategoriesProps> = ({ categories, chooseCategory, chosenCategory }) => {
    return (
        <div className="flex space-x-4 overflow-x-auto px-4">
            <button
                onClick={() => chooseCategory(-1)}
                className={`px-4 py-2 rounded-full ${
                    chosenCategory === -1
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-700'
                }`}
            >
                All
            </button>
            {categories.map((category, index) => (
                <button
                    key={index}
                    onClick={() => chooseCategory(index)}
                    className={`px-4 py-2 rounded-full ${
                        chosenCategory === index
                            ? 'bg-green-500 text-white'
                            : 'bg-gray-200 text-gray-700'
                    }`}
                >
                    {category}
                </button>
            ))}
        </div>
    );
};

export default Categories;
