import { Order, PrecheckOrderResponse } from "~/types";

// Define an interface for objects compatible with Order (partial)
export interface OrderCompatible {
  id: number;
  balanceTobePaid: number;
  status: string;
  [key: string]: any; // Allow any other properties
}

/**
 * Transforms a PrecheckOrderResponse to an Order object
 * for use with the PayNow component
 */
export const precheckResponseToOrder = (
  response: PrecheckOrderResponse
): Order => {
  return {
    id: response.existingOrderGroupId || 0,
    sellerName: response.sellerName || "",
    sellerContactNumber: "",
    deliveryCode: "",
    deliveryDate: response.deliveryDate,
    deliveryTime: response.deliveryTime || "",
    estDeliveryTime: "",
    status: "Created",
    totalItemCount: response.items.length,
    deliveredItemCount: 0,
    cancelledItemCount: 0,
    totalWeight: 0,
    totalOrderAmount: response.totalOrderAmount,
    deliveryCharges: response.deliveryCharges,
    codAmount: response.codAmount,
    discountAmount: response.discountAmount,
    totalAmount: response.totalAmount,
    isPending: true,
    farmers: [],
    delayPaymentPendingAmount: 0,
    sellerId: response.sellerId,
    paidCodAmount: 0,
    paidOnlineAndDirectAmount: 0,
    creditPaidAmount: 0,
    createdOn: new Date().toISOString(),
    buyerAddress: response.defaultAddress?.address || "",
    delPartnerName: "",
    delPartnerNumber: "",
    walletAmount: response.walletAmount,
    cashPaid: 0,
    balanceTobePaid: response.totalAmount
  };
};
