import { AvailableItem } from "~/types";
import React from "react";
import Badge from "./Badge";
import { cn } from "~/utils/cn";
import { useAppConfigStore } from "~/stores/appConfig.store";

interface GetBadgeProps {
  itemDetails: AvailableItem;
  className?: string;
}
const GetBadge: React.FC<GetBadgeProps> = ({ itemDetails, className }) => {
  const { appDomain } = useAppConfigStore();

  if (appDomain === "RET11" && (itemDetails.closed || itemDetails.soldout)) {
    return (
      <Badge
        color="orange"
        text={`${itemDetails.closed ? "Closed" : "Sold Out"}`}
        className={cn(
          "absolute right-0 top-0 rounded-tr-md bg-secondary-50 text-secondary-500 shadow-sm ",
          className
        )}
      />
    );
  }

  if (itemDetails.discPerc > 0) {
    return (
      <Badge
        color="blue"
        text={`${itemDetails.discPerc}% OFF`}
        className={cn(
          "absolute text-white right-0 top-0 rounded-tr-md bg-gradient-to-r from-[#48A2D7] to-[#0065A3] shadow",
          className
        )}
      />
    );
  }

  if (itemDetails.newlyAdded) {
    return (
      <Badge
        color="orange"
        text="Newly Added"
        className={cn(
          "absolute text-white right-0 top-0 rounded-tr-md bg-gradient-to-r from-[#F8961E] to-[#E76C18] shadow",
          className
        )}
      />
    );
  }
};

export default GetBadge;
