import { useState, useEffect } from "react";
import {
  Form,
  useActionData,
  useLoaderData,
  useNavigate,
  useNavigation,
  useSearchParams,
  useSubmit
} from "@remix-run/react";
// import PrimaryButton from "~/components/PrimaryButton";
import {
  json,
  ActionFunction,
  redirect,
  LoaderFunction
} from "@remix-run/node";
import {
  requestOtp,
  verifyOtp,
  getDeviceInfo,
  getNetworkConfig
} from "~/services/auth.server";
import { getSession, commitSession } from "~/utils/session.server";
import { parseJWT } from "@utils/token-utils";
import { ArrowLeft } from "lucide-react";
import { NetworkConfig, User } from "~/types";
import { NetworkAsset } from "~/components/NetworkAssests";
import { setItem } from "~/utils/localStorage";
import { getAppSource } from "~/utils/loader";
import { AppSource } from "~/types/app";
import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import Button from "~/components/Button";
import { useAppConfigStore } from "~/stores/appConfig.store";

interface LoaderData {
  networkConfig: NetworkConfig;
  token?: string;
  mobileNumber?: string;
  appSource?: AppSource;
}

interface LoaderErrorData {
  error: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  const networkConfig = (await getNetworkConfig(request)).data;
  const url = new URL(request.url);
  const token = url.searchParams.get("token");
  const mobileNumber = url.searchParams.get("mobileNumber");
  const appSource = getAppSource(request);

  // const session = await getSession(request.headers.get("Cookie"));
  // const appConfig: { appSource: "whatsappchat"; appStartRoute: string } =
  //   session.get("appConfig");
  // console.log("appConfig123", appConfig, session, request.url);

  // if access token is present and valid, redirect to home
  const session = await getSession(request.headers.get("Cookie"));
  const accessToken = session.get("access_token");
  if (accessToken) {
    const decodedToken = parseJWT(accessToken);
    if (decodedToken.exp && decodedToken.exp > Date.now() / 1000) {
      return redirect("/home");
    }
  }

  try {
    return json<LoaderData>({
      networkConfig,
      token: token || undefined,
      mobileNumber: mobileNumber || undefined,
      appSource
    });
  } catch (error) {
    console.error("Error fetching seller options:", error);
    return json<LoaderErrorData>(
      { error: "Failed to fetch seller options" },
      { status: 500 }
    );
  }
};

interface ActionData {
  success?: boolean;
  message?: string;
  user?: User;
  intent?: string;
}

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent");
  const phoneNumber = formData.get("phoneNumber") as string;
  const otp = formData.get("otp") as string;
  const appSource = getAppSource(request);

  const deviceInfo = getDeviceInfo();

  try {
    if (intent === "getOtp") {
      const payload = {
        app: "seller_app",
        mobileNumber: phoneNumber,
        password: "",
        admin: false,
        deviceInfo
      };

      await requestOtp(payload);

      return json<ActionData>({
        success: true,
        message: "OTP sent successfully",
        intent: "getOtp"
      });
    } else if (intent === "verifyOtp") {
      if (!phoneNumber) {
        return json<ActionData>(
          {
            success: false,
            message: "Phone number is required for OTP verification",
            intent: "verifyOtp"
          },
          { status: 400 }
        );
      }

      const verifyResponse = (await verifyOtp(phoneNumber, otp, "", request))
        .data;

      const session = await getSession(request.headers.get("Cookie"));
      session.set("access_token", verifyResponse.access_token ?? "");
      session.set("refresh_token", verifyResponse.refresh_token ?? "");
      const appConfig = session.get("appConfig");

      if (
        verifyResponse.roles &&
        verifyResponse.roles.includes("USER_REGISTRATION")
      ) {
        // Redirect to registration page with necessary information
        return redirect(
          `/registration?phoneNumber=${phoneNumber}&token=${verifyResponse.access_token}`,
          {
            headers: {
              "Set-Cookie": await commitSession(session)
            }
          }
        );
      }

      const tokenData = parseJWT(verifyResponse.access_token ?? "");
      const userDetails = tokenData.userDetails;

      const user: User = {
        userId: userDetails.userId,
        userName: userDetails.userName,
        businessName: userDetails.businessName,
        buyerId: userDetails.buyerId
      };
      session.set("user", user);

      if (appSource === "whatsappchat") {
        return redirect(appConfig?.appStartRoute || "/help", {
          headers: {
            "Set-Cookie": await commitSession(session)
          }
        });
      }

      return redirect("/home", {
        headers: {
          "Set-Cookie": await commitSession(session)
        }
      });
    } else if (intent === "clearActionData") {
      return json<ActionData>({
        success: true,
        message: "",
        intent: "clearActionData"
      });
    } else {
      return json<ActionData>(
        { success: false, message: "Invalid intent" },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.log("Request error", error);
    return json<ActionData>({
      success: false,
      message: error instanceof Error ? error.message : "Something went wrong"
    });
  }
};

export default function Login() {
  const defaultDelay = 59;
  const [searchParams] = useSearchParams();
  const loader = useLoaderData<LoaderData>();
  const submit = useSubmit();

  const [step, setStep] = useState<"phone" | "otp">("phone");
  const [phoneNumber, setPhoneNumber] = useState(
    searchParams.get("phoneNumber") || ""
  );
  const [otp, setOtp] = useState("");
  const [delay, setDelay] = useState(defaultDelay);
  const [loginError, setLoginError] = useState<string>("");
  const [favIcon, setFavIcon] = useState("");

  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();

  // load network config
  const { networkConfig } = loader;
  useEffect(() => {
    setItem<NetworkConfig>("networkConfig", networkConfig);
    setFavIcon(networkConfig?.pwaAppIcon || "");
  }, [networkConfig]);

  useEffect(() => {
    // update favicon
    const link = document.querySelector(
      "link[rel~='icon']"
    ) as HTMLLinkElement | null;
    if (link) {
      link.href = favIcon;
    } else {
      const newLink = document.createElement("link") as HTMLLinkElement;
      newLink.rel = "icon";
      newLink.href = favIcon;
      document.head.appendChild(newLink);
    }
  }, [favIcon]);

  // Timer for OTP resend
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (step === "otp" && delay > 0) {
      timer = setTimeout(() => setDelay(delay - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [step, delay]);

  const handleResendOtp = () => {
    setOtp("");
    setDelay(defaultDelay);
    setStep("otp"); // Ensure the step remains on 'otp' when resending
    submit(
      { intent: "getOtp", phoneNumber },
      { method: "post", replace: true }
    );
  };

  // Effect to handle step transition based on actionData
  useEffect(() => {
    if (actionData?.success && actionData.intent === "getOtp") {
      setStep("otp");
      setDelay(defaultDelay);
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData && !actionData.success) {
      setLoginError(actionData.message || "");
    }
  }, [actionData]);

  const handleBack = () => {
    setStep("phone");
    setOtp("");
    submit({ intent: "clearActionData" }, { method: "post", replace: true });
  };

  const isValidPhoneNo = (phoneNumber: string) => {
    return /^\d{10}$/.test(phoneNumber);
  };

  const isValidOTP = (otp: string) => {
    return /^\d{6}$/.test(otp);
  };

  function isValidNumber(value: string | number): boolean {
    if (value === "") {
      return true;
    }

    if (
      typeof value === "number" ||
      (typeof value === "string" && /^[+-]?\d+(\.\d+)?$/.test(value))
    ) {
      return true;
    }

    return false;
  }

  return (
    <div className="fixed inset-0 flex flex-col bg-white pb-2">
      <div className="flex-1 overflow-hidden flex flex-col items-center justify-center p-4">
        {/* {networkConfig.domain === "smartbasket.mnetlive.com" && ( */}
        <h1 className=" text-gray-700 text-3xl mb-16">Welcome to</h1>
        {/* )} */}

        {/* {networkConfig.domain !== "smartbasket.mnetlive.com" ? (
          networkConfig.domain !== "dmc.mnetlive.com" && ( */}
        <div className="h-64 w-auto mb-6">
          <NetworkAsset assetName="businessLogo" />
        </div>
        {/* )
        ) : (
          <div className="h-64 w-auto mb-6">
            <NetworkAsset assetName="businessLogo" />
          </div>
        )} */}

        {/* {networkConfig.domain !== "smartbasket.mnetlive.com" && (
          <div className="h-40 w-auto mb-6">
            <NetworkAsset assetName="banner" />
          </div>
        )} */}

        {/* <p className="text-lg mb-8 text-text-secondary">
          Your fruit & vegetable Mandi online
        </p> */}
      </div>

      <div className="w-full bg-white p-4">
        <div className="max-w-md mx-auto">
          {step === "phone" && (
            <h2 className="text-md font-medium mb-2 text-text-primary">
              {`Let's get you started`}
            </h2>
          )}
          <Form method="post" className="">
            {step === "phone" ? (
              <>
                <div className="mb-2">
                  {/* <label
                    htmlFor="phoneNumber"
                    className="block text-lg font-medium text-text-secondary mb-2"
                  >
                    Phone Number
                  </label> */}
                  <div className="flex rounded-lg border border-gray-300 focus-within:border-teal-600 focus-within:ring-1 focus-within:ring-teal-500 py-2 shadow-lg">
                    <span className="inline-flex items-center rounded-l-md border-r border-gray-300 bg-white px-4 text-md text-grey-900 font-medium">
                      +91
                    </span>
                    <input
                      type="tel"
                      name="phoneNumber"
                      id="phoneNumber"
                      className="block w-full flex-1 rounded-r-md bg-white text-text-primary focus:outline-none text-md  px-4"
                      placeholder="Your phone number"
                      value={phoneNumber}
                      onChange={(e) => {
                        if (isValidNumber(e.target.value)) {
                          setPhoneNumber(e.target.value);
                        }
                      }}
                      maxLength={10}
                      minLength={10}
                      required
                    />
                  </div>
                  {/* {!isValidPhoneNo(phoneNumber) && (
                    <span className="mt-1 text-xs text-red-500">
                      Please enter correct mobile no
                    </span>
                  )} */}
                </div>
                <p className="text-xs text-text-disabled mb-8">
                  We will send you OTP on this number
                </p>
                {/* TODO use primary button */}
                <Button
                  type="submit"
                  name="intent"
                  value="getOtp"
                  className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                    !isValidPhoneNo(phoneNumber)
                      ? "bg-gray-400 hover:bg-gray-500"
                      : "bg-teal-500 hover:bg-teal-600"
                  }`}
                  disabled={
                    !isValidPhoneNo(phoneNumber) ||
                    navigation.state === "submitting"
                  }
                >
                  {navigation.state === "submitting" ? "Sending..." : "GET OTP"}
                </Button>
              </>
            ) : (
              <>
                <input type="hidden" name="phoneNumber" value={phoneNumber} />
                <div className="flex items-center mb-4">
                  <button
                    type="button"
                    onClick={handleBack}
                    className="mr-4 text-text-primary"
                  >
                    <ArrowLeft size={24} />
                  </button>
                  <label
                    htmlFor="otp"
                    className="block text-md font-medium text-text-primary"
                  >
                    Enter 6 Digits OTP
                  </label>
                </div>
                <div className="flex flex-col w-full">
                  <input
                    type="number"
                    name="otp"
                    id="otp"
                    className="block w-full rounded-lg border border-gray-300 bg-white text-text-primary focus:outline-none text-md py-2 px-4 shadow-lg"
                    placeholder="Enter OTP"
                    value={otp}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d{0,6}$/.test(value)) {
                        // Allow only up to 6 digits
                        setOtp(value);
                      }
                    }}
                    maxLength={6}
                    required
                    pattern="\d{6}"
                  />
                  {/* {!isValidOTP(otp) && (
                    <span className="mt-1 text-xs text-red-500">
                      Please enter correct OTP
                    </span>
                  )} */}

                  {loginError.length > 0 && (
                    <span className="mt-1 text-xs text-red-500">
                      Invalid OTP
                    </span>
                  )}
                </div>
                <span className="mt-1 text-xs text-text-disabled">
                  OTP has been sent to{" "}
                  <span className="text-gray-900 font-medium">
                    {phoneNumber}
                  </span>
                </span>
                <span className="mt-2 mb-8 flex flex-row justify-between text-xs text-text-disabled">
                  {`Haven't receive the OTP?`}
                  <Button
                    type="button"
                    onClick={handleResendOtp}
                    disabled={delay > 0 || navigation.state === "submitting"}
                    className={`${
                      delay || navigation.state === "submitting"
                        ? "text-gray-500"
                        : "text-teal-500"
                    } `}
                  >
                    RESEND {delay > 0 && `00:${delay}`}
                  </Button>
                </span>
                <Button
                  className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                    !isValidOTP(otp)
                      ? "bg-gray-400 hover:bg-gray-500"
                      : "bg-teal-500 hover:bg-teal-600"
                  }`}
                  type="submit"
                  name="intent"
                  value="verifyOtp"
                  disabled={
                    !isValidOTP(otp) || navigation.state === "submitting"
                  }
                >
                  {navigation.state === "submitting"
                    ? "Verifying..."
                    : "CONTINUE"}
                </Button>
              </>
            )}
          </Form>
          {/* {actionData?.message && actionData.intent !== "clearActionData" && (
            <p
              className={`mt-4 text-sm ${
                actionData.success ? "text-teal-500" : "text-red-600"
              }`}
            >
              {actionData.message}
            </p>
          )} */}
        </div>
      </div>
    </div>
  );
}

export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(0)} />;
}
