import { json } from "@remix-run/node";
import { getSession, commitSession } from "~/utils/session.server";

/**
 * Authentication bridge that sets a header to trigger client-side login modal
 * instead of redirecting to login page
 * @param request - The incoming request object
 * @param redirectTo - Optional custom redirect path after login
 * @param forceLogin - Whether to force login even if user has a token (for re-authentication)
 * @returns null if authenticated, or a response object that triggers the login modal
 */
export async function requireAuth(
  request: Request,
  redirectTo?: string,
  forceLogin?: boolean
) {
  const session = await getSession(request.headers.get("Cookie"));
  const accessToken = session.get("access_token");

  // If user is already authenticated and not forcing login, continue
  if (accessToken && !forceLogin) {
    return null;
  }

  // If redirectTo is provided, store it in the session
  if (redirectTo) {
    session.set("redirectPath", redirectTo);
  } else {
    // Use the current URL as the redirect path
    const url = new URL(request.url);
    session.set("redirectPath", url.pathname + url.search);
  }

  // Set a special flag in the session to trigger the login modal
  session.set("requireAuth", true);

  // Return JSON with authentication required flag and set the cookie
  return json(
    {
      authRequired: true,
      message: "Authentication required",
      forceLogin: forceLogin || false
    },
    {
      headers: {
        "Set-Cookie": await commitSession(session),
        "X-Auth-Required": "true" // Custom header to detect on client
      }
    }
  );
}

/**
 * Helper for action functions that require authentication
 * This handles form submissions and returns appropriate responses
 * @param request - The incoming request object
 * @param action - The action function to execute if authenticated
 * @param forceLogin - Whether to force login even if user has a token
 */
export async function withAuth<T>(
  request: Request,
  action: (request: Request) => Promise<T>,
  forceLogin?: boolean
) {
  const session = await getSession(request.headers.get("Cookie"));
  const accessToken = session.get("access_token");

  // Check if user is authenticated or force login is requested
  if (!accessToken || forceLogin) {
    // For actions, store the form data so it can be resubmitted after login
    const formData = await request.clone().formData();
    const formEntries = Array.from(formData.entries()).map(([key, value]) => ({
      key,
      value
    }));

    // Store form data in session for potential resubmission
    session.set("pendingFormData", formEntries);
    session.set("pendingFormMethod", request.method);
    session.set("pendingFormAction", request.url);

    // Get redirect path from the current URL
    const url = new URL(request.url);
    session.set("redirectPath", url.pathname + url.search);
    session.set("requireAuth", true);

    return json(
      {
        authRequired: true,
        message: "Authentication required for this action",
        forceLogin: forceLogin || false
      },
      {
        headers: {
          "Set-Cookie": await commitSession(session),
          "X-Auth-Required": "true"
        }
      }
    );
  }

  // User is authenticated, proceed with the action
  return action(request);
}
