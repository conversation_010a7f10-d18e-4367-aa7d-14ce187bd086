// Facebook Conversion API Server Service
// Handles server-side communication with Facebook Conversion API

import {
  FacebookConversionEvent,
  FacebookConversionApiPayload,
  FacebookConversionApiResponse,
  FacebookConversionApiError,
  FacebookConversionApiConfig,
} from "~/types/capi-fb";
import { CLIDData } from "~/utils/capi-clid";
import {
  logDebug,
  logError,
  logApiRequest,
  withErrorHandling,
  withPerformanceMonitoring,
} from "~/utils/capi-logger";

/**
 * Default configuration for Facebook Conversion API
 */
const DEFAULT_CONFIG = {
  apiVersion: "v23.0",
  baseUrl: "https://graph.facebook.com",
  partnerAgent: "mnet-web-app-v1.0",
  timeout: 10000,
  maxRetries: 2,
  retryDelay: 1000,
} as const;

/**
 * Facebook Conversion API Client
 * Handles all communication with Facebook's Conversion API
 */
export class FacebookConversionApiClient {
  private config: FacebookConversionApiConfig & typeof DEFAULT_CONFIG;

  constructor(config: FacebookConversionApiConfig) {
    this.config = {
      ...DEFAULT_CONFIG,
      ...config,
    };

    // Validate required configuration
    if (!this.config.datasetId) {
      const error = new Error("Facebook Conversion API: datasetId is required");
      logError("Client initialization failed", error, { config });
      throw error;
    }
    if (!this.config.accessToken) {
      const error = new Error("Facebook Conversion API: accessToken is required");
      logError("Client initialization failed", error, { config });
      throw error;
    }

    logDebug("Facebook Conversion API client initialized", {
      datasetId: this.config.datasetId,
      accessToken: this.config.accessToken,
      debug: this.config.debug,
    });
  }

  /**
   * Send events to Facebook Conversion API
   * @param events - Array of events to send
   * @param testEventCode - Optional test event code for testing
   * @returns Promise resolving to API response
   */
  async sendEvents(
    events: FacebookConversionEvent[],
    testEventCode?: string
  ): Promise<FacebookConversionApiResponse> {
    return withPerformanceMonitoring(
      withErrorHandling(async () => {
        if (!events || events.length === 0) {
          const error = new Error("No events to send");
          logError("Send events failed", error);
          throw error;
        }

        const payload: FacebookConversionApiPayload = {
          data: events,
          partner_agent: this.config.partnerAgent,
        };

        // Add test event code if provided
        if (testEventCode || this.config.testEventCode) {
          payload.test_event_code = testEventCode || this.config.testEventCode;
        }

        const url = this.buildApiUrl();
        const response = await this.makeRequest(url, payload);

        logDebug("Events sent successfully", {
          eventsReceived: response.events_received,
          eventsProcessed: response.events_processed,
          facebookTraceId: response.fbtrace_id,
        });

        return response;
      }, "sendEvents"),
      "sendEvents"
    )();
  }

  /**
   * Send a single event to Facebook Conversion API
   * @param event - Event to send
   * @param testEventCode - Optional test event code for testing
   * @returns Promise resolving to API response
   */
  async sendEvent(
    event: FacebookConversionEvent,
    testEventCode?: string
  ): Promise<FacebookConversionApiResponse> {
    return this.sendEvents([event], testEventCode);
  }

  /**
   * Build the API URL for sending events
   * @returns Complete API URL
   */
  private buildApiUrl(): string {
    return `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.datasetId}/events`;
  }

  /**
   * Make HTTP request to Facebook Conversion API
   * @param url - API endpoint URL
   * @param payload - Request payload
   * @returns Promise resolving to API response
   */
  private async makeRequest(
    url: string,
    payload: FacebookConversionApiPayload
  ): Promise<FacebookConversionApiResponse> {
    const startTime = Date.now();

    const requestOptions: RequestInit = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.config.accessToken}`,
      },
      body: JSON.stringify(payload),
    };

    logDebug("Making API request", {
      url,
      body: JSON.stringify(payload),
      hasTestCode: !!payload.test_event_code,
    });

    try {
      let response: Response;

      // Add timeout if supported
      if (this.config.timeout) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
          logError("Request timeout", new Error(`Request timed out after ${this.config.timeout}ms`));
        }, this.config.timeout);

        requestOptions.signal = controller.signal;

        try {
          response = await fetch(url, requestOptions);
          clearTimeout(timeoutId);
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      } else {
        response = await fetch(url, requestOptions);
      }

      return await this.handleResponse(response);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      logApiRequest("POST", url, undefined, duration, { error: errorMessage });
      throw error;
    }
  }

  /**
   * Handle API response
   * @param response - Fetch response object
   * @returns Parsed response data
   */
  private async handleResponse(response: Response): Promise<FacebookConversionApiResponse> {
    const responseText = await response.text();

    logDebug("Received API response", {
      status: response.status,
      statusText: response.statusText,
      responseLength: responseText.length,
    });

    if (!response.ok) {
      let errorData: FacebookConversionApiError;

      try {
        const parsedError = JSON.parse(responseText);
        errorData = parsedError.error || parsedError;
      } catch {
        errorData = {
          message: responseText || `HTTP ${response.status}: ${response.statusText}`,
          type: "http_error",
          code: response.status,
        };
      }

      logError("API request failed", undefined, {
        status: response.status,
        errorData,
        responseText: responseText.substring(0, 500), // Log first 500 chars
      });

      throw new CustomFacebookConversionApiError(
        `Facebook Conversion API Error: ${errorData.message}`,
        errorData
      );
    }

    try {
      const parsedResponse = JSON.parse(responseText) as FacebookConversionApiResponse;

      logDebug("API response parsed successfully", {
        eventsReceived: parsedResponse.events_received,
        eventsProcessed: parsedResponse.events_processed,
        hasMessages: !!parsedResponse.messages?.length,
        facebookTraceId: parsedResponse.fbtrace_id,
      });

      return parsedResponse;
    } catch (error) {
      logError("Failed to parse API response", error as Error, {
        responseText: responseText.substring(0, 500),
      });
      throw new Error(`Failed to parse Facebook Conversion API response: ${responseText}`);
    }
  }
}

/**
 * Custom error class for Facebook Conversion API errors
 */
export class CustomFacebookConversionApiError extends Error {
  public readonly errorData: FacebookConversionApiError;

  constructor(message: string, errorData: FacebookConversionApiError) {
    super(message);
    this.name = "FacebookConversionApiError";
    this.errorData = errorData;
  }
}

/**
 * Create Facebook Conversion API client
 * @param clidData - CLID data
 * @returns Configured Facebook Conversion API client or null if not configured
 */
export function createFacebookConversionApiClient(clidData: CLIDData): FacebookConversionApiClient | null {
  // Check if Facebook Conversion API is enabled
  if (!clidData.ctwa_token || !clidData.wabDatasetId) {
    return null;
  }

  try {
    const config: FacebookConversionApiConfig = {
      datasetId: clidData.wabDatasetId,
      accessToken: clidData.ctwa_token,
      debug: false,
    };

    return new FacebookConversionApiClient(config);
  } catch (error) {
    console.error("Failed to create Facebook Conversion API client:", error);
    return null;
  }
}

/**
 * Send events with retry logic
 * @param client - Facebook Conversion API client
 * @param events - Events to send
 * @param maxRetries - Maximum number of retry attempts
 * @param retryDelay - Delay between retries in milliseconds
 * @returns Promise resolving to API response
 */
export async function sendEventsWithRetry(
  client: FacebookConversionApiClient,
  events: FacebookConversionEvent[],
  maxRetries: number = 2,
  retryDelay: number = 1000
): Promise<FacebookConversionApiResponse> {
  return withPerformanceMonitoring(
    withErrorHandling(async () => {
      let lastError: Error;
      const eventNames = events.map(e => e.event_name).join(", ");

      logDebug("Starting retry logic", {
        eventCount: events.length,
        eventNames,
        maxRetries,
        retryDelay,
      });

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const response = await client.sendEvents(events);

          if (attempt > 1) {
            logDebug("Retry successful", {
              attempt,
              eventNames,
              eventsProcessed: response.events_processed,
            });
          }

          return response;
        } catch (error) {
          lastError = error as Error;

          logError(`Attempt ${attempt} failed`, undefined, {
            eventNames,
            attempt,
            maxRetries,
          });

          // Don't retry on certain error types
          if (error instanceof CustomFacebookConversionApiError) {
            const errorCode = error.errorData.code;
            // Don't retry on authentication or validation errors
            if (errorCode === 401 || errorCode === 403 || errorCode === 400) {
              logError("Non-retryable error encountered", undefined, {
                errorData: error.errorData,
              });
              throw error;
            }
          }

          // If this is the last attempt, throw the error
          if (attempt === maxRetries) {
            logError("All retry attempts exhausted", undefined, {
              totalAttempts: attempt,
              eventNames,
            });
            break;
          }

          // Calculate exponential backoff delay
          const delay = retryDelay * Math.pow(2, attempt);
          logDebug(`Retrying in ${delay}ms`, {
            attempt: attempt,
            maxRetries,
            delay,
          });

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      throw lastError!;
    }, "sendEventsWithRetry"), "sendEventsWithRetry"
  )();
}
