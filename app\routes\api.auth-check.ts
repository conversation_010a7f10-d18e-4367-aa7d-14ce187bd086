import { json, LoaderFunction } from "@remix-run/node";
import { getSession } from "~/utils/session.server";
import { parseJWT } from "@utils/token-utils";

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const accessToken = session.get("access_token");
  const user = session.get("user");

  if (!accessToken) {
    return json({ isAuthenticated: false });
  }

  // Check if token is valid
  try {
    const decodedToken = parseJWT(accessToken);

    if (!decodedToken.exp || decodedToken.exp < Date.now() / 1000) {
      return json({ isAuthenticated: false });
    }

    return json({
      isAuthenticated: true,
      user
    });
  } catch (error) {
    return json({ isAuthenticated: false });
  }
};
