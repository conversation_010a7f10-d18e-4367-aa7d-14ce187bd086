import type { PrecheckOrderResponse } from "~/types";
import type { CouponDTO } from "~/types/coupon.types";
import type { Cart } from "~/types";

interface CreateItemUpdatePayloadParams {
  order: PrecheckOrderResponse | null;
  orderNote: string;
  action?: "add" | "remove";
  couponData?: CouponDTO;
  appliedCouponData?: {
    coupon?: {
      id: number;
    };
  } | null;
  cart: Cart;
}

type ItemUpdatePayload = {
  intent: string;
  cart: string;
  deliveryDate: string;
  sellerId: number;
  sellerDataId: number;
  codAllowed: boolean;
  existingOrderGroupId: number;
  cartKey: string;
  preconfirmUid: string;
  sellerMessage: string;
  couponId: number | null;
};

export const createItemUpdatePayload = ({
  order,
  orderNote,
  action,
  couponData,
  appliedCouponData,
  cart
}: CreateItemUpdatePayloadParams): ItemUpdatePayload | null => {
  if (!order?.items || !order.cartKey) {
    console.error("Order or items not available");
    return null;
  }

  try {
    // If coupon Update is true, then use the couponId from the couponData
    // Otherwise, use the couponId from the order.appliedCoupon
    let couponId =
      action === "add" && couponData
        ? couponData?.id
        : appliedCouponData?.coupon?.id || null;

    if (action === "remove") {
      couponId = null;
    }

    return {
      intent: "precheck",
      cart: JSON.stringify(cart),
      deliveryDate: order.deliveryDate,
      sellerId: order.sellerId,
      sellerDataId: order.sellerInventoryId,
      codAllowed: order.codSelected,
      existingOrderGroupId: order.existingOrderGroupId,
      cartKey: order.cartKey,
      preconfirmUid: order.preconfirmUid,
      sellerMessage: orderNote,
      couponId: couponId
    };
  } catch (error) {
    console.error("Error creating update payload:", error);
    throw new Error("Failed to create update payload. Please try again.");
  }
};
