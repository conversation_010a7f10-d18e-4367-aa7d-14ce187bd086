import { Order } from "~/types";

export function ItemTotal({ orderDetails }: { orderDetails: Order }) {
  const codAmount = (orderDetails?.isPending ? 0 : orderDetails?.codAmount || 0)
  const onlineAmount = (orderDetails?.totalAmount -
    orderDetails?.codAmount -
    orderDetails?.delayPaymentPendingAmount)
  return (
    <div className="flex flex-col border border-dashed rounded-lg px-3 py-1">
      <div className="flex items-center justify-between py-2 border-b">
        <span className="font-light text-xs text-gray-600">Item Total</span>
        <span className="text-xs text-gray-900">
          ₹ {orderDetails.totalOrderAmount}
        </span>
      </div>
      <div className="flex items-center justify-between py-2">
        <span className="font-light text-xs text-gray-600">
          Delivery Charges
        </span>
        <span className="text-xs text-gray-900">
          ₹ {orderDetails.deliveryCharges}
        </span>
      </div>
      {orderDetails.discountAmount > 0 ? (
        <div className="flex items-center justify-between py-2 border-t">
          <span className="font-light text-xs text-gray-600">
            Discount Amount
          </span>
          <span className="text-xs text-gray-900">
            {`-₹ ${orderDetails.discountAmount}`}
          </span>
        </div>
      ) : null}
      <div className="flex items-center justify-between py-2 border-t border-gray-400">
        <span className="font text-xs text-gray-900">Total Amount</span>
        <span className="text-xs text-blue-500">
          ₹ {orderDetails.totalAmount}
        </span>
      </div>
      {(codAmount + onlineAmount) > 0 && <div className="flex items-center justify-between py-2 border-t border-gray-400">
        <span className="font text-xs text-gray-900">Paid</span>
        <div className="flex flex-col items-center">
          <span className="text-xs text-gray-900">CoD</span>
          <span className="text-xs text-blue-500">
            ₹ {codAmount}
          </span>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-xs text-gray-900">Direct / Online</span>
          <span className="text-xs text-blue-500">
            ₹{" "}
            {onlineAmount}
          </span>
        </div>
      </div>}
    </div>
  );
}
