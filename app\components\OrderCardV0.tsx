import CustomImage from "./CustomImage";
import React, { useEffect, useState } from "react";
import { Order } from "~/types";
import dayjs from "dayjs";
import { useFetcher, useNavigate } from "@remix-run/react";
import PaymentStatusModel from "~/components/PaymentStatusModel";
import { ItemTotal } from "./ItemTotal";

interface OrderCardProps {
  orderDetails: Order;
  expanded: boolean;
  onPress: () => void;
}

interface OrderPayment {
  paymentUrl: string | null;
  paymentStatus: "SUCCESS" | "FAILED" | "PENDING";
  refId: number;
}

const OrderCard: React.FC<OrderCardProps> = ({
  orderDetails,
  expanded,
  onPress
}) => {
  const fetcher = useFetcher<OrderPayment>();
  const [paymentStatus, setPaymentStatus] = useState("PENDING");
  const [isPolling, setIsPolling] = useState(false);
  const [refId, setRefId] = useState<number | null>(null);
  const navigate = useNavigate();

  // Start polling when UPI payment is initiated
  useEffect(() => {
    if (fetcher.data?.paymentUrl) {
      // Start polling the payment status
      setIsPolling(true);
      setRefId(fetcher.data.refId);
      window.location.href = fetcher.data.paymentUrl;
    }
  }, [fetcher.data?.paymentUrl, fetcher.data?.refId]);

  // Polling logic to check payment status using PUT
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (isPolling) {
      intervalId = setInterval(() => {
        const formData = new FormData();
        formData.append("requestName", "PaymentStatus");
        formData.append("refId", refId?.toString() || "");

        fetcher.submit(formData, {
          method: "put",
          action: "/home/<USER>"
        });
      }, 3000);
    }

    // Clean up the interval on component unmount
    return () => clearInterval(intervalId);
  }, [isPolling, fetcher.data?.paymentStatus, refId, fetcher]);

  // Update payment status based on API response
  useEffect(() => {
    if (fetcher.data?.paymentStatus) {
      setPaymentStatus(fetcher.data.paymentStatus);

      if (
        fetcher.data.paymentStatus === "SUCCESS" ||
        fetcher.data.paymentStatus === "FAILED"
      ) {
        setIsPolling(false);
        setRefId(null);
        location.reload();
      }
    }
  }, [fetcher.data?.paymentStatus]);

  const handlePayNow = () => {
    const formData = new FormData();
    formData.append("requestName", "InitiatePayment");
    formData.append(
      "amount",
      orderDetails.isPending
        ? orderDetails.codAmount.toFixed(2)
        : orderDetails.delayPaymentPendingAmount.toFixed(2)
    );
    formData.append("orderGroupId", orderDetails.id.toString());

    // Submit the form data using fetcher's submit method
    fetcher.submit(formData, {
      method: "post",
      action: "/home/<USER>"
    });
  };

  return (
    <div className="mb-4 bg-white rounded-lg shadow p-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <span className="text-sm font-semibold text-gray-800">
            {orderDetails.sellerName}
          </span>
          {orderDetails.sellerContactNumber && (
            <button
              onClick={() =>
                window.open(
                  `tel:+91${orderDetails.sellerContactNumber}`,
                  "_self"
                )
              }
              className="ml-2"
            >
              <img src="/call_circle.png" alt="Call" className="w-5 h-5" />
            </button>
          )}
        </div>
        <div>
          {orderDetails.status === "Delivered" && (
            <span className="text-sm text-teal-600 font-semibold">
              Delivered
            </span>
          )}
          {orderDetails.status === "Cancelled" && (
            <span className="text-sm text-red-600 font-semibold">
              Cancelled
            </span>
          )}
          {/* Commented the Delivery Module */}
          {/* {orderDetails.deliveryCode && (
            <div className="text-right">
              <span className="text-gray-700 font-semibold">
                {orderDetails.deliveryCode}
              </span>
              <div className="flex items-center justify-end">
                <img src="/truck.png" alt="Code" className="w-3 h-3 mr-1" />
                <span className="text-sm text-gray-500">CODE</span>
              </div>
            </div>
          )} */}
        </div>
      </div>

      {/* Info */}
      <div className="flex justify-between items-center mt-2">
        <span className="text-xs text-teal-600">
          {!orderDetails.deliveryTime
            ? dayjs(
                `${orderDetails.deliveryDate} ${orderDetails.estDeliveryTime}`
              )
                .format("dddd, D MMM, hh:mm A")
                .toUpperCase()
            : dayjs(`${orderDetails.deliveryDate} ${orderDetails.deliveryTime}`)
                .format("dddd, D MMM, hh:mm A")
                .toUpperCase()}
        </span>
        <span className="text-sm font-medium text-gray-800">
          ₹ {orderDetails.totalAmount.toFixed(2)}
        </span>
      </div>

      {/* Items Count */}
      <div className="flex items-center mt-2">
        <img src="/item_count.png" alt="Items" className="w-4 h-4 mr-2" />
        <span className="text-sm text-gray-600">
          {orderDetails.totalItemCount} Total Items
        </span>
      </div>

      {/* Show more and Pay Now */}
      <div className="flex justify-between">
        {/* Toggle Details */}
        <button onClick={onPress} className="text-sm mt-2 text-blue-500">
          {expanded ? "Hide Details" : "Show Details"}
        </button>
        {orderDetails.delayPaymentPendingAmount > 0 ||
        (orderDetails.codAmount > 0 && orderDetails.isPending) ? (
          <button
            onClick={handlePayNow}
            className="text-sm bg-primary hover:bg-primary-700 text-white px-4 py-2 rounded"
          >
            Pay now
          </button>
        ) : null}
      </div>
      {paymentStatus === "PENDING" ? (
        <>
          {isPolling && (
            <>
              <PaymentStatusModel
                paymentStatus={paymentStatus}
                onClose={() => {
                  setIsPolling(false);
                  navigate(0);
                }}
              />
            </>
          )}
        </>
      ) : (
        <p>Payment Status: {paymentStatus}</p>
      )}

      {/* Details */}
      {expanded && (
        <div className="mt-4 border rounded-md p-2">
          {orderDetails.farmers.map((farmer) => (
            <div key={farmer.farmerId} className="">
              {/* Each item details */}
              {farmer.items.map((item, index) => (
                <div
                  key={item.orderId}
                  className={`flex items-center justify-between mb-2 ${
                    index !== 0 ? "border-t" : null
                  } py-21`}
                >
                  {/* item details left*/}
                  <div className="flex flex-row items-center ">
                    <CustomImage
                      src={item.itemUrl}
                      alt={""}
                      className="w-9 h-9 rounded mr-2 object-cover"
                    />
                    <div>
                      <div className="flex flex-col">
                        <span
                          className={
                            item.status === "Cancelled" || item.qty == 0
                              ? "text-xs  text-gray-400 mb-1"
                              : "text-xs font-semibold text-gray-800 mb-1"
                          }
                        >
                          {item.itemName}
                        </span>
                        <span
                          className={
                            item.status === "Cancelled" || item.qty == 0
                              ? "text-xs font-medium text-gray-400"
                              : "text-xs font-medium text-gray-700"
                          }
                        >
                          {item.itemRegionalLanguageName}
                        </span>
                      </div>
                      <div
                        className={
                          item.status === "Cancelled"
                            ? " text-xs text-gray-400  line-through "
                            : ""
                        }
                      >
                        <span
                          className={
                            item.status === "Cancelled" || item.qty == 0
                              ? "text-xs text-gray-400"
                              : "font-semibold text-xs text-teal-600 "
                          }
                        >
                          {item.qty} {item.unit}
                        </span>
                        <span className="text-xs  text-gray-600"> x </span>
                        <span
                          className={
                            item.status === "Cancelled" || item.qty == 0
                              ? " text-xs font-light text-gray-600 line-through"
                              : "text-xs font-light text-gray-600"
                          }
                        >
                          ₹{item.price.toFixed(2)}/{item.unit}
                        </span>
                      </div>

                      {/* <p
                        className={`text-sm ${
                          item.status === "Delivered"
                            ? "text-teal-600"
                            : item.status === "Cancelled"
                            ? "text-red-600"
                            : "text-yellow-600"
                        }`}
                      >
                        Status: {item.status}
                      </p> */}
                    </div>
                  </div>
                  {/* item details right*/}
                  <div className="flex flex-row">
                    <span
                      className={
                        item.status === "Cancelled" || item.qty == 0
                          ? "text-xs text-gray-600 line-through"
                          : "text-xs text-gray-600"
                      }
                    >
                      ₹{item.amount.toFixed(2)}
                    </span>
                    <span>
                      {item.status === "Completed" && (
                        <img
                          src="/order_success.png"
                          alt="sucess"
                          className="h-4 w-4 m1-2"
                        />
                      )}

                      {(item.status === "Cancelled" || item.qty == 0) && (
                        <img
                          src="/order_fail.png"
                          alt="failed"
                          className="h-4 w-4 ml-2"
                        />
                      )}
                    </span>
                  </div>
                </div>
              ))}

              {/* Total card */}
              <ItemTotal orderDetails={orderDetails} />
            </div>
          ))}

          {/* Payment Section - Removed "Pay Now" Button as per instruction */}
        </div>
      )}
    </div>
  );
};

// function Spinner() {
//   return (
//     <div className="w-16 h-16 border-4 border-gray-200 border-t-4 border-t-blue-500 rounded-full animate-spin mx-auto mt-5"></div>
//   );
// }

export default OrderCard;
