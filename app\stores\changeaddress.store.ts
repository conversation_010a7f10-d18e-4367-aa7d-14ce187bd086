import { create } from "zustand";
import { ItemCategoryDtos, ItemOptionsData } from "~/types";
import { CategoryType, SearchPage } from "~/types/chooseitems.types";

interface ChooseitemsStore {
  loading: boolean;
  categoryType: CategoryType;
  categoryTypeList: CategoryType[];
  selectedCategory?: number;
  selectedParentCategory?: ItemCategoryDtos;
  searchPage: SearchPage;
  itemOptionsData: ItemOptionsData | null;
  isScrolled: boolean;
  setItemOptionsData: (itemOptionsData: ItemOptionsData) => void;
  setCategoryType: (categoryType: CategoryType) => void;
  setCategoryTypeList: (categoryType: CategoryType) => void;
  setSelectedCategory: (selectedCategory: number) => void;
  setSelectedParentCategory: (selectedCategory?: ItemCategoryDtos) => void;
  setSearchPage: (searchPage: SearchPage) => void;
  setLoading: (loading: boolean) => void;
  setIsScrolled: (isScrolled: boolean) => void;
  resetChooseItemsStore: () => void;
}

export const chooseitemsStore = create<ChooseitemsStore>((set) => ({
  loading: false,
  categoryType: null,
  selectedCategory: undefined,
  categoryTypeList: [],
  searchPage: null,
  itemOptionsData: null,
  isScrolled: false,
  setItemOptionsData: (itemOptionsData) =>
    set(() => ({ itemOptionsData: { ...itemOptionsData } })),
  setCategoryType: (categoryType) => set(() => ({ categoryType })),
  setCategoryTypeList: (categoryType) =>
    set((state) => ({
      categoryTypeList: [...state.categoryTypeList, categoryType]
    })),
  setSelectedCategory: (selectedCategory) => set(() => ({ selectedCategory })),
  setSelectedParentCategory: (newCat) =>
    set(() => ({ selectedParentCategory: newCat })),
  setSearchPage: (searchPage) => set(() => ({ searchPage })),
  setLoading: (loading) => set(() => ({ loading })),
  setIsScrolled: (isScrolled) => set(() => ({ isScrolled })),
  resetChooseItemsStore: () =>
    set(() => ({
      loading: false,
      categoryType: null,
      selectedCategory: undefined,
      categoryTypeList: [],
      searchPage: null,
      itemOptionsData: null,
      isScrolled: false
    }))
}));
