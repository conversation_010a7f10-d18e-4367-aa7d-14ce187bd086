// app/components/InstallPWAButton.tsx
import { useAppConfigStore } from "~/stores/appConfig.store";
import BottomSheet from "./BottmSheet";
import InstallPWAiOSInstruction from "./InstallPWAiOSInstruction";
import { X } from "lucide-react";
import { useInstallPWA } from "~/hooks/useInstallPWA";

interface InstallPWAButtonProps {
  themeColor?: string;
  title?: string;
  subtitle?: string;
}

export default function InstallPWAButton({
  themeColor = "Primary",
  title = "Get The App",
  subtitle = "For Better Experience"
}: InstallPWAButtonProps) {
  const DISMISS_KEY = "installPWAButtonDismissedAt";
  const DISMISS_DURATION = 2 * 60 * 60 * 1000; // 2Hr in ms

  const { networkConfig } = useAppConfigStore();

  const {
    visible,
    deferredPrompt,
    isFirefox,
    isS<PERSON><PERSON>,
    showInstructionSheet,
    setShowInstructionSheet,
    handleInstall,
    handleDismiss
  } = useInstallPWA({
    dismissKey: DISMISS_KEY,
    dismissDuration: DISMISS_DURATION
  });

  if (networkConfig?.enableInstallPwa !== true) {
    return null;
  }
  if (!visible) return null;

  return (
    <>
      <div
        className={`
          w-full flex items-center justify-between pl-3 pr-4 py-2
          ${
            themeColor === "Primary"
              ? "bg-primary text-white"
              : "bg-primary-50 text-primary-600"
          }
          transition duration-1000
        `}
      >
        {/* Left: dismiss + icon + text */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <button onClick={handleDismiss}>
              <X
                size={20}
                className={`${
                  themeColor === "Primary"
                    ? "text-primary-50"
                    : "text-neutral-800"
                }`}
              />
            </button>
            <img
              src={networkConfig?.footerAppIcon}
              alt="App Icon"
              className="w-10 h-10 rounded-lg object-cover"
            />
          </div>
          <div className="flex flex-col">
            <span className="font-semibold leading-tight">{title}</span>
            <span className="text-sm leading-tight">{subtitle}</span>
          </div>
        </div>

        {/* Right: install button */}
        <button
          onClick={handleInstall}
          className={`
            px-3 py-1 rounded-lg text-sm font-semibold transition duration-500
            ${
              deferredPrompt
                ? themeColor === "Primary"
                  ? "bg-white text-primary hover:bg-gray-100"
                  : "bg-primary text-white hover:bg-primary-700"
                : themeColor === "Primary"
                ? "bg-white text-primary hover:bg-gray-100"
                : "bg-primary text-white hover:bg-primary-700"
            }
          `}
        >
          {deferredPrompt ? "Use App" : "Install App"}
        </button>
      </div>

      {/* Fallback sheet for in‑depth instructions */}
      <BottomSheet
        isOpen={showInstructionSheet}
        onClose={() => setShowInstructionSheet(false)}
        className="w-full p-2 bg-neutral-100"
      >
        <InstallPWAiOSInstruction
          browser={isFirefox ? "firefox" : isSafari ? "safari" : "browser"}
          platform={isSafari ? "iOS" : "desktop"}
        />
      </BottomSheet>
    </>
  );
}
