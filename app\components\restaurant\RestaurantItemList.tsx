import React, { useCallback } from "react";
import { AvailableItem, Cart } from "~/types";
import RestaurantItemCard from "./RestaurantItemCard";

interface RestaurantItemListProps {
  items: AvailableItem[];
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  approxPricing?: boolean;
}

const RestaurantItemList: React.FC<RestaurantItemListProps> = ({
  items,
  cart,
  onAddItem,
  onRemoveItem,
  approxPricing = false
}) => {
  // Group items by their variant group
  const getItemGroups = useCallback(() => {
    const itemMap = new Map<string, AvailableItem[]>();

    items.forEach((item) => {
      if (itemMap.has(item.varGroup)) {
        itemMap.get(item.varGroup)?.push(item);
      } else {
        itemMap.set(item.varGroup, [item]);
      }
    });

    return Array.from(itemMap.values()) || [];
  }, [items]);

  const itemGroups = getItemGroups();

  return (
    <div className="flex flex-col w-full">
      <div className="flex flex-col w-full gap-4 px-3">
        {itemGroups.map((group, index) => {
          // Use the first non-sold out item as representative, or the first item if all are sold out
          const displayItem =
            group.find((item) => !(item.soldout || item.closed)) || group[0];

          return (
            <RestaurantItemCard
              key={`${displayItem.sellerItemId}-${index}`}
              itemDetailsList={group}
              cart={cart}
              onAdd={onAddItem}
              onRemove={onRemoveItem}
              approxPricing={approxPricing}
              amount={0}
              isLast={index === itemGroups.length - 1}
            />
          );
        })}
      </div>
    </div>
  );
};

export default RestaurantItemList;
