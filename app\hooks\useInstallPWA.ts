// app/hooks/useInstallPWA.ts
import { useState, useEffect } from "react";
import { UAParser } from "ua-parser-js";

// Define the BeforeInstallPromptEvent interface
declare global {
  interface BeforeInstallPromptEvent extends Event {
    prompt(): Promise<void>;
    userChoice: Promise<{
      outcome: "accepted" | "dismissed";
      platform: string;
    }>;
  }
}

interface UseInstallPWAOptions {
  dismissKey?: string;
  dismissDuration?: number;
}

export function useInstallPWA({
  dismissKey = "installPWADismissedAt",
  dismissDuration = 48 * 60 * 60 * 1000 // 48Hr in ms
}: UseInstallPWAOptions = {}) {
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [visible, setVisible] = useState(false);
  const [showInstructionSheet, setShowInstructionSheet] = useState(false);

  // Browser detection states
  const [isChromeBased, setIsChromeBased] = useState(false);
  const [isSafari, setIsSafari] = useState(false);
  const [isFirefox, setIsFirefox] = useState(false);
  const [inIframe, setInIframe] = useState(false);
  const [inWebView, setInWebView] = useState(false);

  // —————————————————————————————————————
  // 1. Figure out browser + embed context
  // —————————————————————————————————————
  useEffect(() => {
    const parser = new UAParser();
    const ua = parser.getUA();
    const { browser, os } = parser.getResult();

    // Chromium‑based UA‑token
    const isChromiumTag = /Chrome|Chromium|Edg|OPR/.test(ua);
    setIsChromeBased(isChromiumTag);

    // exact Safari / Firefox names
    setIsSafari(browser.name === "Safari");
    setIsFirefox(browser.name === "Firefox");

    // iframe?
    let iframe = false;
    try {
      iframe = window.self !== window.top;
    } catch {
      iframe = true;
    }
    setInIframe(iframe);

    // WebView?
    const isAndroidWV =
      os.name === "Android" &&
      (/\bwv\b/.test(ua) ||
        (!ua.includes("Chrome/") && ua.includes("Android")));
    const isIosWV = os.name === "iOS" && !ua.includes("Safari/");
    const hasRNBridge =
      typeof (window as any).ReactNativeWebView !== "undefined" ||
      typeof (window as any).webkit?.messageHandlers?.ReactNativeWebView !==
        "undefined";
    setInWebView(isAndroidWV || isIosWV || hasRNBridge);
  }, []);

  // —————————————————————————————————————
  // 2. Listen for native Chrome install prompt
  // —————————————————————————————————————
  useEffect(() => {
    const handler = (e: BeforeInstallPromptEvent) => {
      // don't show if just dismissed
      const ts = localStorage.getItem(dismissKey);
      if (ts && Date.now() - parseInt(ts, 10) < dismissDuration) return;

      e.preventDefault();
      setDeferredPrompt(e);
      setVisible(true);
    };

    // this event is automatically triggered if browser detects
    // that the app is installable (e.g. manifest + service worker)
    // it will not fire if it is already installed
    window.addEventListener("beforeinstallprompt", handler as EventListener);

    return () =>
      window.removeEventListener(
        "beforeinstallprompt",
        handler as EventListener
      );
  }, [dismissKey, dismissDuration]);

  // —————————————————————————————————————
  // 3. Auto‑trigger your "manual" sheet on Safari/Firefox
  //    only if not in iframe/WebView and no native prompt
  // —————————————————————————————————————
  useEffect(() => {
    // run only once parser+embed check are done
    if (deferredPrompt !== null) return;
    if (isChromeBased) return;
    if (!(isSafari || isFirefox)) return;
    if (inIframe || inWebView) return;

    // dismissed recently?
    const ts = localStorage.getItem(dismissKey);
    if (ts && Date.now() - parseInt(ts, 10) < dismissDuration) return;

    setVisible(true);
  }, [
    dismissKey,
    dismissDuration,
    deferredPrompt,
    isChromeBased,
    isSafari,
    isFirefox,
    inIframe,
    inWebView
  ]);

  const handleInstall = async () => {
    if (deferredPrompt) {
      // Chrome native
      await deferredPrompt.prompt();
      const choice = await deferredPrompt.userChoice;
      console.log("PWA install choice:", choice.outcome);
      setVisible(false);
      setDeferredPrompt(null);
    } else {
      // our fallback instructions
      setShowInstructionSheet(true);
    }
  };

  const handleDismiss = () => {
    localStorage.setItem(dismissKey, Date.now().toString());
    setVisible(false);
    setDeferredPrompt(null);
  };

  return {
    // State
    visible,
    deferredPrompt,
    isChromeBased,
    isSafari,
    isFirefox,
    inIframe,
    inWebView,
    showInstructionSheet,

    // Actions
    setShowInstructionSheet,
    handleInstall,
    handleDismiss
  };
}
