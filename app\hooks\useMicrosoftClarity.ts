import { useEffect } from "react";

// Extend the Window interface to include clarity
declare global {
  interface Window {
    clarity?: (...args: unknown[]) => void;
  }
}

/**
 * Custom hook to initialize Microsoft Clarity after page render
 */
export const useMicrosoftClarity = (): void => {
  useEffect(() => {
    // Prevent initialization in non-browser environments
    if (typeof window === "undefined") return;

    // Prevent duplicate initialization
    if (window.clarity) return;

    // Create and append Clarity script
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = `
      (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "pvp2c4ub4k");
    `;

    document.head.appendChild(script);
  }, []);
};
