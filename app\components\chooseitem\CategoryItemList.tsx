import React, { useEffect, useState } from "react";
import {
  AvailableItem,
  Cart,
  ImageViewType,
  ItemCategoryDtos,
  ItemOptionsData
} from "~/types";
import { CategoryList } from "./CategoryList";
import { ItemList } from "./ItemList";
import EmptySearchResult from "./EmptySearchResult";

interface CategoryItemListProps {
  data: ItemOptionsData;
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  searchStr: string;
  imageViewType: ImageViewType;
}

const CategoryItemList: React.FC<CategoryItemListProps> = ({
  data,
  cart,
  onAddItem,
  onRemoveItem,
  searchStr,
  imageViewType
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number>(); // Stores the selected category ID
  const [filteredItems, setFilteredItems] = useState<AvailableItem[]>(
    data?.availableItems || []
  );
  const [availableItems, setAvailableItems] = useState<AvailableItem[]>(
    data?.availableItems || []
  );
  const [categories, setCategories] = useState<ItemCategoryDtos[]>(
    data?.itemCategoryDtos || []
  );

  // add all categories and favItem category
  // -1 -> All items, -2 -> Favorite
  useEffect(() => {
    if (
      data.categoriesEnabled &&
      data.itemCategoryDtos?.length &&
      data.availableItems?.length
    ) {
      availableItems.map((item) => {
        item?.itemCategories?.push(-1);
        // Add only if have freqScore > 0
        if (data.favItemsEnabled && item.freqScore > 0) {
          item?.itemCategories?.push(-2);
        }
        return item;
      });
      setAvailableItems(availableItems);
      const allCategories = categories.find((cat) => cat.id === -1);
      if (!allCategories) {
        categories.unshift({
          id: -1,
          name: "All Items",
          picture: "/all_items.png"
        });
        setCategories(categories);
      }

      // Add fav item category if enabled
      if (data.favItemsEnabled) {
        const favCategory = categories.find((cat) => cat.id === -2);
        if (!favCategory) {
          categories.unshift({
            id: -2,
            name: "Favourites",
            picture: "/fav_items.png"
          });
          setCategories(categories);
        }
      }

      setSelectedCategoryId(-1);
    }
  }, [availableItems, categories]);

  // Search items
  useEffect(() => {
    if (searchStr.length) {
      setFilteredItems(
        availableItems.filter((item) =>
          item.itemName.toLowerCase().includes(searchStr.toLowerCase())
        )
      );
      setSelectedCategoryId(-1);
    }
  }, [searchStr, availableItems]);

  useEffect(() => {
    if (selectedCategoryId) {
      const filteredItems = availableItems.filter((item) =>
        item?.itemCategories?.includes(selectedCategoryId)
      );

      // Sort the fav items in descending order
      if (data.favItemsEnabled && selectedCategoryId == -2) {
        filteredItems.sort((itemA, itemB) => itemB.freqScore - itemA.freqScore);
      }

      setFilteredItems(filteredItems);
    }
  }, [selectedCategoryId]);
  // Handler for selecting a category
  const handleSelectCategory = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  return (
    <div className="flex bg-gray-100 pt-2 h-full w-full">
      {data?.categoriesEnabled && categories.length ? (
        <CategoryList
          categories={categories}
          selectedCategoryId={selectedCategoryId}
          onSelectCategory={handleSelectCategory}
        />
      ) : (
        ""
      )}
      <div className="overflow-y-scroll overflow-x-hidden max-h-full w-full">
        {filteredItems.length > 0 ? (
          <ItemList
            approxPricing={data.approxPricing}
            items={filteredItems}
            cart={cart}
            onAddItem={onAddItem}
            onRemoveItem={onRemoveItem}
            imageViewType={imageViewType}
          />
        ) : (
          <EmptySearchResult searchStr={searchStr} />
        )}
      </div>
    </div>
  );
};

export default CategoryItemList;
