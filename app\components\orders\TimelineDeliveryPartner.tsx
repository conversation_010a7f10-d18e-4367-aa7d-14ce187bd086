import { FC } from "react";

interface TimelineDeliveryPartnerProps {
  name: string;
  phone?: string;
}

const TimelineDeliveryPartner: FC<TimelineDeliveryPartnerProps> = ({
  name,
  phone
}) => {
  return (
    <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-full bg-gray-100 overflow-hidden mr-3 flex items-center justify-center">
          <img
            src="/delivery_boy.svg"
            alt={name}
            className="w-10 h-10 object-contain"
          />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">{name}</p>
          <p className="text-xs text-gray-500">Delivery Partner</p>
        </div>
      </div>
      {phone && (
        <button
          onClick={() => window.open(`tel:${phone}`, "_self")}
          className="w-10 h-10 rounded-full flex items-center justify-center text-teal-600 bg-teal-50"
        >
          <svg
            className="w-5 h-5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        </button>
      )}
    </div>
  );
};

export default TimelineDeliveryPartner;
