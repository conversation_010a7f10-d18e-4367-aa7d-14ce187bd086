import { CouponDTO } from "~/types/coupon.types";

export const DefaultCouponModal = ({
  couponData,
  open,
  onClose
}: {
  couponData: CouponDTO;
  open: boolean;
  onClose: () => void;
}) => {
  if (!open) return null;

  return (
    // <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-end justify-center">
    <div className="bg-gradient-to-b from-[#DFF9F8] to-white to-40% rounded-lg w-full max-w-lg overflow-hidden relative">
      <div className="p-6 text-center">
        <div className="flex justify-center mb-6">
          <div className="flex items-center justify-center">
            <img src="/coupon-icon.svg" alt="Coupon" className="h-44 w-44 " />
          </div>
        </div>

        <p className="text-gray-700 mb-2 text-xs flex items-center justify-center gap-2">
          <svg
            width="8"
            height="8"
            viewBox="0 0 8 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_2222_2330)">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1.79996 1C1.96565 1 2.09996 1.13431 2.09996 1.3V1.6H2.39996C2.56565 1.6 2.69996 1.73431 2.69996 1.9C2.69996 2.06569 2.56565 2.2 2.39996 2.2H2.09996V2.5C2.09996 2.66569 1.96565 2.8 1.79996 2.8C1.63428 2.8 1.49996 2.66569 1.49996 2.5V2.2H1.19996C1.03428 2.2 0.899963 2.06569 0.899963 1.9C0.899963 1.73431 1.03428 1.6 1.19996 1.6H1.49996V1.3C1.49996 1.13431 1.63428 1 1.79996 1ZM4.19996 1C4.32909 1 4.44373 1.08262 4.48456 1.20512L5.1251 3.12646L6.70531 3.71911C6.8224 3.76302 6.89996 3.87495 6.89996 4C6.89996 4.12505 6.8224 4.23698 6.70531 4.28089L5.1251 4.87354L4.48456 6.79488C4.44373 6.91738 4.32909 7 4.19996 7C4.07084 7 3.9562 6.91738 3.91536 6.79488L3.27482 4.87354L1.69462 4.28089C1.57753 4.23698 1.49996 4.12505 1.49996 4C1.49996 3.87495 1.57753 3.76302 1.69462 3.71911L3.27482 3.12646L3.91536 1.20512C3.9562 1.08262 4.07084 1 4.19996 1ZM2.09996 5.2C2.26565 5.2 2.39996 5.33431 2.39996 5.5V5.8H2.69996C2.86565 5.8 2.99996 5.93431 2.99996 6.1C2.99996 6.26569 2.86565 6.4 2.69996 6.4H2.39996V6.7C2.39996 6.86569 2.26565 7 2.09996 7C1.93428 7 1.79996 6.86569 1.79996 6.7V6.4H1.49996C1.33428 6.4 1.19996 6.26569 1.19996 6.1C1.19996 5.93431 1.33428 5.8 1.49996 5.8H1.79996V5.5C1.79996 5.33431 1.93428 5.2 2.09996 5.2Z"
                fill="#3D4D5D"
              />
            </g>
            <defs>
              <clipPath id="clip0_2222_2330">
                <rect
                  width="7.2"
                  height="7.2"
                  fill="white"
                  transform="translate(0.299988 0.400024)"
                />
              </clipPath>
            </defs>
          </svg>
          EXCLUSIVELY FOR YOU
          <svg
            width="8"
            height="8"
            viewBox="0 0 8 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_2222_2330)">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1.79996 1C1.96565 1 2.09996 1.13431 2.09996 1.3V1.6H2.39996C2.56565 1.6 2.69996 1.73431 2.69996 1.9C2.69996 2.06569 2.56565 2.2 2.39996 2.2H2.09996V2.5C2.09996 2.66569 1.96565 2.8 1.79996 2.8C1.63428 2.8 1.49996 2.66569 1.49996 2.5V2.2H1.19996C1.03428 2.2 0.899963 2.06569 0.899963 1.9C0.899963 1.73431 1.03428 1.6 1.19996 1.6H1.49996V1.3C1.49996 1.13431 1.63428 1 1.79996 1ZM4.19996 1C4.32909 1 4.44373 1.08262 4.48456 1.20512L5.1251 3.12646L6.70531 3.71911C6.8224 3.76302 6.89996 3.87495 6.89996 4C6.89996 4.12505 6.8224 4.23698 6.70531 4.28089L5.1251 4.87354L4.48456 6.79488C4.44373 6.91738 4.32909 7 4.19996 7C4.07084 7 3.9562 6.91738 3.91536 6.79488L3.27482 4.87354L1.69462 4.28089C1.57753 4.23698 1.49996 4.12505 1.49996 4C1.49996 3.87495 1.57753 3.76302 1.69462 3.71911L3.27482 3.12646L3.91536 1.20512C3.9562 1.08262 4.07084 1 4.19996 1ZM2.09996 5.2C2.26565 5.2 2.39996 5.33431 2.39996 5.5V5.8H2.69996C2.86565 5.8 2.99996 5.93431 2.99996 6.1C2.99996 6.26569 2.86565 6.4 2.69996 6.4H2.39996V6.7C2.39996 6.86569 2.26565 7 2.09996 7C1.93428 7 1.79996 6.86569 1.79996 6.7V6.4H1.49996C1.33428 6.4 1.19996 6.26569 1.19996 6.1C1.19996 5.93431 1.33428 5.8 1.49996 5.8H1.79996V5.5C1.79996 5.33431 1.93428 5.2 2.09996 5.2Z"
                fill="#3D4D5D"
              />
            </g>
            <defs>
              <clipPath id="clip0_2222_2330">
                <rect
                  width="7.2"
                  height="7.2"
                  fill="white"
                  transform="translate(0.299988 0.400024)"
                />
              </clipPath>
            </defs>
          </svg>
        </p>

        <h2 className="text-md font-semibold text-typography-800">
          Save{" "}
          <span className="text-[#00C6D1]">
            ₹{couponData?.discountValue.toFixed(2)}
          </span>{" "}
          on this order
        </h2>

        <p className="text-typography-500 text-xs font-medium mb-2">
          with coupon <span className="font-bold">{couponData.code}</span>
        </p>

        <p className="text-typography-500 text-xs font-medium">
          Tap on ‘APPLY’ to avail this
        </p>
      </div>

      <div className="px-6 pb-4">
        <button
          onClick={onClose}
          className="w-full py-2 px-6 text-white font-medium bg-teal-500 rounded-md hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50"
        >
          Apply
        </button>
      </div>
    </div>
    // </div>
  );
};
