import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON>ila<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  AogList,
  AddonItem,
  Dietary,
  ItemVariationDto
} from "~/types";
import { cn } from "~/utils/cn";
import { MinusIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules";
import { aogListToItemAddonGroup } from "~/utils/menuUtils";
import { useCartStore } from "~/stores/cart.store";
import AddItemButtonV2 from "~/components/chooseitem/AddItemButtonV2";
import { formatCurrency } from "~/utils/format";
import { DietaryImage } from "~/components/common/DietaryImage";

export interface ItemAddonGroup {
  id: number;
  name: string;
  type: "variant" | "addOn" | "customization" | "extra";
  isMandatory: boolean;
  selectionType: "single" | "multiple" | "item-qty";
  minSelection: number;
  maxSelection: number;
  description?: string;
  items: ItemAddonOption[];
}

export interface ItemAddonOption {
  id: string;
  name: string;
  price: number;
  inStock: boolean;
  isSelected?: boolean;
  isDefault?: boolean;
  quantity?: number;
  dietary?: Dietary;
}

interface ItemCustomizationProps {
  /** The item to be customized */
  item?: AvailableItem;

  /** Current cart state */
  cart: Cart;
  /** Handler for adding an item to cart with optional selected addons */
  onAdd?: (
    item: AvailableItem,
    selectedAddons?: Record<string, ItemAddonOption[]>
  ) => void;
  /** Handler for removing an item from cart */
  onRemove?: (item: AvailableItem) => void;
  /** Whether pricing is approximate */
  approxPricing?: boolean;
  /** Optional callback to set visibility of the item details modal */
  setShowItemDetails?: (value: boolean) => void;
  /** Callback function to trigger precheck API when addons update */
  onAddonsUpdate?: () => void;
}

/**
 * Displays a gallery of item images in a swiper carousel
 * @param props - Component props
 * @param props.item - The item containing images to display
 */
const ItemImageGallery: React.FC<{ item: AvailableItem }> = ({ item }) => {
  const imageUrls = item?.itemUrl?.split(",") || [];
  if (imageUrls.length === 1) {
    imageUrls.push(imageUrls[0]);
  }

  return (
    <div className="w-full flex overflow-hidden bg-white rounded-md px-4">
      <Swiper
        pagination={{
          clickable: true
        }}
        className="mySwiper"
        modules={[Autoplay, Pagination]}
        loop={true}
        spaceBetween={10}
        slidesPerView={1}
      >
        {imageUrls.map((image, index) => (
          <SwiperSlide key={index}>
            <img
              className="rounded-lg w-full max-h-80 mb-8 object-contain"
              src={image}
              alt={`${item.itemName || "Product"} - ${index + 1}`}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

/**
 * Displays basic item information including name, price, and other details
 * @param props - Component props
 * @param props.item - The item to display details for
 * @param props.totalPrice - The calculated total price including any selected addons
 * @param props.hasCustomizations - Whether the item has customization options
 * @param props.approxPricing - Whether pricing is approximate
 */
const ItemBasicDetails: React.FC<{
  item: AvailableItem;
  totalPrice: number;
  hasCustomizations: boolean;
  approxPricing: boolean;
}> = ({ item, totalPrice, hasCustomizations, approxPricing }) => {
  return (
    <div className="rounded-md bg-white p-2 px-4">
      {item.diet && <DietaryImage dietary={item.diet} className="my-2" />}
      <div className="flex-1">
        <h2 className="text-md font-bold text-gray-800">{item.itemName}</h2>
        <p className="text-xs text-gray-500 mt-1">
          {item.itemRegionalLanguageName}
        </p>
        <p className="text-xs text-gray-500 mt-1">{item.description}</p>
      </div>
    </div>
  );
};

/**
 * A loading skeleton UI shown while item data is being fetched
 */
const LoadingSkeleton: React.FC = () => {
  return (
    <div className="flex flex-col p-4 gap-4 animate-pulse">
      <div className="h-48 bg-gray-200 rounded-md"></div>
      <div className="h-6 bg-gray-200 rounded-md w-2/3"></div>
      <div className="h-4 bg-gray-200 rounded-md w-1/2"></div>
      <div className="h-20 bg-gray-200 rounded-md"></div>
      <div className="flex justify-between">
        <div className="h-10 bg-gray-200 rounded-md w-1/3"></div>
        <div className="h-10 bg-gray-200 rounded-md w-1/3"></div>
      </div>
    </div>
  );
};

/**
 * Renders a single-selection option UI with radio button styling
 * @param props - Component props
 * @param props.option - The addon option to render
 * @param props.isSelected - Whether the option is currently selected
 * @param props.inStock - Whether the option is in stock
 * @param props.onSelect - Handler called when the option is selected
 */
const SingleSelectionOption: React.FC<{
  option: ItemAddonOption;
  isSelected: boolean;
  inStock: boolean;
  onSelect: () => void;
  dietary: Dietary | null;
}> = ({ option, isSelected, inStock, onSelect, dietary = null }) => {
  return (
    <div
      className="flex justify-between items-center py-2"
      onClick={() => inStock && onSelect()}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          inStock && onSelect();
        }
      }}
      role="button"
      tabIndex={0}
      aria-pressed={isSelected}
      aria-disabled={!inStock}
    >
      <div className="flex w-full justify-between items-center">
        <div className="flex items-center gap-2">
          {dietary && <DietaryImage dietary={dietary} />}
          <span
            className={cn(
              "text-sm",
              !inStock ? "text-gray-400" : "text-gray-800"
            )}
          >
            {option.name}
          </span>
        </div>
        <div className="flex items-center gap-3">
          <div
            className={cn(
              "w-4 h-4 border rounded-full flex items-center justify-center",
              isSelected ? "border-primary" : "border-gray-300"
            )}
            aria-hidden="true"
          >
            {isSelected && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
          <span className="text-sm font-medium">₹{option.price}</span>
        </div>
      </div>
    </div>
  );
};

/**
 * Renders a multiple-selection option UI with checkbox styling
 * @param props - Component props
 * @param props.option - The addon option to render
 * @param props.isSelected - Whether the option is currently selected
 * @param props.inStock - Whether the option is in stock
 * @param props.onSelect - Handler called when the option is selected
 */
const MultipleSelectionOption: React.FC<{
  option: ItemAddonOption;
  isSelected: boolean;
  inStock: boolean;
  onSelect: () => void;
  dietary: Dietary;
}> = ({ option, isSelected, inStock, onSelect, dietary = null }) => {
  return (
    <div
      className="flex justify-between items-center py-2"
      onClick={() => inStock && onSelect()}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          inStock && onSelect();
        }
      }}
      role="button"
      tabIndex={0}
      aria-pressed={isSelected}
      aria-disabled={!inStock}
    >
      <div className="flex w-full flex-row items-center justify-between">
        <div className="flex gap-2 items-center w-full">
          {dietary && <DietaryImage dietary={dietary} />}
          <span
            className={cn(
              "text-sm",
              !inStock ? "text-gray-400" : "text-gray-800"
            )}
          >
            {option.name}
          </span>
        </div>
        <div className="flex  flex-row gap-3 items-center">
          <p className="text-sm font-medium">₹{option.price}</p>
          <div
            className={cn(
              "w-4 h-4 border rounded",
              isSelected ? "bg-primary border-primary" : "border-gray-300"
            )}
            aria-hidden="true"
          >
            {isSelected && (
              <span className="flex items-center justify-center text-white text-xs">
                ✓
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Renders a quantity-selection option UI with add/increment/decrement controls
 * @param props - Component props
 * @param props.option - The addon option to render
 * @param props.quantity - Current quantity of this addon
 * @param props.onQuantityChange - Handler called when quantity changes
 */
const ItemQtyOption: React.FC<{
  option: ItemAddonOption;
  quantity: number;
  onQuantityChange: (change: number) => void;
}> = ({ option, quantity, onQuantityChange }) => {
  return (
    <div className="flex justify-between items-center py-2">
      <div className="flex-1">
        <span
          className={`text-sm ${
            !option.inStock ? "text-gray-400" : "text-gray-800"
          }`}
        >
          {option.name}
        </span>
      </div>
      <div className="flex items-center">
        <span className="text-sm font-medium mr-4">₹{option.price}</span>
        {quantity === 0 ? (
          <button
            className="px-3 py-1 text-xs rounded bg-primary text-white disabled:bg-gray-300"
            onClick={() => option.inStock && onQuantityChange(1)}
            disabled={!option.inStock}
          >
            Add
          </button>
        ) : (
          <div className="flex border rounded-md">
            <button
              className="px-2 py-1 text-primary border-r"
              onClick={() => onQuantityChange(-1)}
              aria-label="Decrease quantity"
            >
              <MinusIcon className="w-4 h-4" />
            </button>
            <span className="px-3 py-1 flex items-center justify-center min-w-[30px] text-sm">
              {quantity}
            </span>
            <button
              className="px-2 py-1 text-primary border-l"
              onClick={() => option.inStock && onQuantityChange(1)}
              disabled={!option.inStock}
              aria-label="Increase quantity"
            >
              <PlusIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Renders a group of addon options with appropriate UI based on selection type
 * @param props - Component props
 * @param props.group - The addon group to render
 * @param props.handleAddonSelection - Handler for addon selection
 * @param props.handleAddonQuantityChange - Handler for quantity changes
 * @param props.isOptionSelected - Function to check if an option is selected
 * @param props.getAddonQuantity - Function to get current quantity of an addon
 * @param props.getSelectionText - Function to get explanatory text for the group
 */
const AddonGroup: React.FC<{
  group: ItemAddonGroup;
  handleAddonSelection: (
    group: ItemAddonGroup,
    option: ItemAddonOption
  ) => void;
  handleAddonQuantityChange: (
    groupId: number,
    option: ItemAddonOption,
    change: number
  ) => void;
  isOptionSelected: (groupId: number, optionId: string) => boolean;
  getAddonQuantity: (groupId: number, optionId: string) => number;
  getSelectionText: (group: ItemAddonGroup) => string;
}> = ({
  group,
  handleAddonSelection,
  handleAddonQuantityChange,
  isOptionSelected,
  getAddonQuantity,
  getSelectionText
}) => {
  return (
    <div className="bg-white rounded-md shadow-sm p-4 mb-2">
      <div className="mb-2">
        <h3 className="font-medium text-gray-800">{group.name}</h3>
        <p className="text-xs text-gray-500">{getSelectionText(group)}</p>
      </div>

      <div className="space-y-2">
        {group.items.map((option) =>
          group.selectionType === "item-qty" ? (
            <ItemQtyOption
              key={option.id}
              option={option}
              quantity={getAddonQuantity(group.id, option.id)}
              onQuantityChange={(change) =>
                handleAddonQuantityChange(group.id, option, change)
              }
            />
          ) : group.selectionType === "multiple" ? (
            <MultipleSelectionOption
              key={option.id}
              option={option}
              isSelected={isOptionSelected(group.id, option.id)}
              inStock={option.inStock}
              onSelect={() => handleAddonSelection(group, option)}
              dietary={option.dietary || null}
            />
          ) : (
            <SingleSelectionOption
              key={option.id}
              option={option}
              isSelected={isOptionSelected(group.id, option.id)}
              inStock={option.inStock}
              onSelect={() => handleAddonSelection(group, option)}
              dietary={option.dietary || null}
            />
          )
        )}
      </div>
    </div>
  );
};

/**
 * Displays the price and unit information
 */
const PriceDisplay: React.FC<{
  totalPrice: number;
}> = ({ totalPrice }) => {
  return (
    <div className="flex flex-col justify-center">
      <span className="font-bold text-sm text-gray-800">₹{totalPrice}</span>
    </div>
  );
};

/**
 * Renders the bottom action bar with price display and add/quantity controls
 */
const ActionBar: React.FC<{
  item: AvailableItem;
  totalPrice: number;
  itemQuantity: number;
  areRequiredSelectionsMade: () => boolean;
  handleRemoveItem: () => void;
  changeQuantity: (qty: number) => void;
  handleItemAddComplete: () => void;
}> = ({
  item,
  totalPrice,
  itemQuantity,
  areRequiredSelectionsMade,
  handleRemoveItem,
  changeQuantity,
  handleItemAddComplete
}) => {
  const hasCustomizations =
    (item.aogList && item.aogList.length > 0) ||
    (item.itemVariationList && item.itemVariationList.length > 0);

  return (
    <div className="flex w-full justify-between bg-white py-4 px-2 border-gray-200 sticky bottom-0 shadow-md">
      <div className="flex w-full items-center justify-between">
        {hasCustomizations ? null : (
          <PriceDisplay totalPrice={totalPrice * (itemQuantity || 1)} />
        )}
        <AddItemButtonV2
          qty={itemQuantity}
          onAdd={() => changeQuantity(itemQuantity + 1)}
          onRemove={handleRemoveItem}
          isDisabled={!areRequiredSelectionsMade()}
          unit={item.unit}
          className="w-28 h-10 bg-teal-50 rounded-[.25rem]"
          btnConfig={{
            showUnit: false,
            btnType: "secondary"
          }}
        />
        {hasCustomizations && (
          <button
            className={cn(
              "w-full px-4 py-2 h-10 rounded-md text-white ml-4",
              areRequiredSelectionsMade() ? "bg-primary" : "bg-gray-400"
            )}
            disabled={!areRequiredSelectionsMade()}
            onClick={() => {
              if (itemQuantity === 0) {
                changeQuantity(itemQuantity + 1);
              }
              handleItemAddComplete();
            }}
          >
            {/* // TODO: totalPrice should be multiplied by itemQuantity using setTotalPrice in useEffect */}
            <div className="flex gap-2 items-center justify-center">
              <p className="text-md">
                {itemQuantity > 0 ? `Update` : `Add to Cart`}
              </p>
              {item.discPerc > 0 && (
                <p className="text-md line-through">{` ${formatCurrency(
                  totalPrice * (itemQuantity || 1),
                  0
                )}`}</p>
              )}
              <p className="text-md">{` ${formatCurrency(
                totalPrice * (itemQuantity || 1),
                0
              )}`}</p>
            </div>
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Main component for item customization.
 * Allows users to view item details, select addons with different selection types
 * (single, multiple, item-qty), and add the customized item to their cart.
 */
const ItemCustomization: React.FC<ItemCustomizationProps> = ({
  item,
  cart,
  approxPricing = false,
  onAddonsUpdate,
  onAdd
}) => {
  const { addItem, removeItem, cartKey } = useCartStore();

  const variations =
    item?.itemVariationList && item?.itemVariationList?.length > 0;

  const [selectedVariation, setSelectedVariation] =
    useState<ItemVariationDto | null>(null);

  // Initialize selected variation from cart if exists
  useEffect(() => {
    if (item?.sellerItemId && cart[item.sellerItemId]) {
      const cartItem = cart[item.sellerItemId];
      if (cartItem.variationId && item.itemVariationList) {
        const variation = item.itemVariationList.find(
          (v) => v.id === cartItem.variationId
        );
        if (variation) {
          setSelectedVariation(variation);
        }
      }
    } else if (item?.itemVariationList && item.itemVariationList.length > 0) {
      // Select first variation by default if no variation is selected in cart
      setSelectedVariation(item.itemVariationList[0]);
    }
  }, [item, cart]);

  const addonGroups = useMemo(() => {
    // If there's a selected variation, use its addon groups
    if (selectedVariation) {
      return aogListToItemAddonGroup(
        selectedVariation.addOnGroupList || [],
        cart[item?.sellerItemId || ""]
      );
    }
    // Otherwise use the item's addon groups
    return aogListToItemAddonGroup(
      item?.aogList || [],
      cart[item?.sellerItemId || ""]
    );
  }, [selectedVariation, item?.aogList, item?.sellerItemId, cart]);

  // State to track selected addons for each group
  const [selectedAddons, setSelectedAddons] = useState<
    Record<string, ItemAddonOption[]>
  >({});

  // State to track quantity of each addon option for item-qty type
  const [addonQuantities, setAddonQuantities] = useState<
    Record<string, Record<string, number>>
  >({});

  const [itemQuantity, setItemQuantity] = useState(
    item?.sellerItemId ? cart[item.sellerItemId]?.qty || 0 : 0
  );

  const [totalPrice, setTotalPrice] = useState(item?.pricePerUnit || 0);
  const [hasCustomizations, setHasCustomizations] = useState(false);

  // Initialize state based on item and customizations
  useEffect(() => {
    if (!item) return;

    // Set initial quantity based on cart
    const cartItem = cart[item.sellerItemId];
    if (cartItem) {
      setItemQuantity(cartItem.qty);
      // Use amount from cartItem if available or use the price from item
      setTotalPrice(cartItem.amount || item.pricePerUnit);
    } else {
      setItemQuantity(0);
      setTotalPrice(item.pricePerUnit);
    }

    // Detect if the item has customizations
    const hasGroups = Array.isArray(addonGroups) && addonGroups.length > 0;
    setHasCustomizations(hasGroups);

    // Initialize addon quantities from addonGroups
    if (hasGroups) {
      const initialQuantities: Record<string, Record<string, number>> = {};
      const initialSelections: Record<string, ItemAddonOption[]> = {};

      addonGroups.forEach((group) => {
        const groupId = group.id.toString();
        initialSelections[groupId] = [];

        // Initialize quantity tracking for item-qty groups
        if (group.selectionType === "item-qty") {
          initialQuantities[groupId] = {};

          // Set initial quantities from selected items in cart
          group.items.forEach((option) => {
            initialQuantities[groupId][option.id] = option.quantity || 0;

            // If item has quantity, add it to selected addons
            if ((option.quantity || 0) > 0) {
              initialSelections[groupId].push(option);
            }
          });
        }
        // For single/multiple selection groups, initialize from selected items
        else {
          // Add all pre-selected items to selections
          const selectedItems = group.items.filter((item) => item.isSelected);
          if (selectedItems.length > 0) {
            initialSelections[groupId] = selectedItems;
          }
          // If no selected items but group is mandatory and single select, select default
          else if (
            group.isMandatory &&
            (group.selectionType === "single" ||
              group.selectionType === "multiple")
          ) {
            const defaultItem =
              group.items.find((item) => item.isDefault) || group.items[0];
            if (defaultItem) {
              initialSelections[groupId] = [defaultItem];
            }
          }
        }
      });

      setAddonQuantities(initialQuantities);
      setSelectedAddons(initialSelections);
    }
  }, [item, cart, addonGroups]);

  // Calculate total price based on selected addons
  useEffect(() => {
    if (!item) return;

    const basePrice = item.pricePerUnit;
    if (!selectedAddons) return;

    let addonsTotal = 0;

    // Calculate price for regular addons (single/multiple selection)
    // Only count prices for non-item-qty groups from selectedAddons
    addonGroups.forEach((group) => {
      if (group.selectionType !== "item-qty") {
        const addons = selectedAddons[group.id.toString()] || [];
        addons.forEach((addon) => {
          addonsTotal += addon.price || 0;
        });
      }
    });

    // Calculate price for item-qty addons separately
    // Only use addonQuantities for item-qty groups to avoid double counting
    Object.entries(addonQuantities).forEach(([groupId, items]) => {
      // Check if this group is an item-qty type
      const group = addonGroups.find((g) => g.id.toString() === groupId);
      if (group && group.selectionType === "item-qty") {
        Object.entries(items).forEach(([itemId, quantity]) => {
          // Find the addon option price
          const option = group.items.find((item) => item.id === itemId);
          if (option && quantity > 0) {
            addonsTotal += (option.price || 0) * quantity;
          }
        });
      }
    });

    setTotalPrice(basePrice + addonsTotal);
  }, [selectedAddons, addonQuantities, item, addonGroups]);

  // Handle selection of an addon option
  const handleAddonSelection = (
    group: ItemAddonGroup,
    option: ItemAddonOption
  ) => {
    setSelectedAddons((prev) => {
      const newSelections = { ...prev };

      // For single selection groups
      if (group.selectionType === "single") {
        newSelections[group.id.toString()] = [option];
      }
      // For multiple selection groups
      else if (group.selectionType === "multiple") {
        const currentGroupSelections = prev[group.id.toString()] || [];
        const isCurrentlySelected = currentGroupSelections.some(
          (opt) => opt.id === option.id
        );

        if (isCurrentlySelected) {
          // Remove if already selected and not below min selection
          if (currentGroupSelections.length > group.minSelection) {
            newSelections[group.id.toString()] = currentGroupSelections.filter(
              (opt) => opt.id !== option.id
            );
          }
        } else {
          // Add if not selected and not above max selection
          if (
            !group.maxSelection ||
            currentGroupSelections.length < group.maxSelection
          ) {
            newSelections[group.id.toString()] = [
              ...currentGroupSelections,
              option
            ];
          }
        }
      }

      // Update cart if item is already in cart and we have valid cartKey
      if (item && itemQuantity > 0 && cartKey) {
        // Use setTimeout to ensure the state update completes first
        setTimeout(() => {
          const selectedAogs = convertSelectedAddonsToAogList(newSelections);
          updateCartWithCurrentAddons(selectedAogs);

          // Call precheck API after updating addons
          if (onAddonsUpdate) {
            onAddonsUpdate();
          }
        }, 0);
      }

      return newSelections;
    });
  };

  // Handle quantity change for item-qty type addons
  const handleAddonQuantityChange = (
    groupId: number,
    option: ItemAddonOption,
    change: number
  ) => {
    // Get the current quantity before updating
    const currentQty = addonQuantities[groupId.toString()]?.[option.id] || 0;
    const newQty = Math.max(0, currentQty + change);

    // Update quantity state with the calculated new quantity
    setAddonQuantities((prev) => {
      const newQuantities = { ...prev };
      const groupKey = groupId.toString();

      if (!newQuantities[groupKey]) {
        newQuantities[groupKey] = {};
      }

      // Set the exact new quantity (not using the change value again)
      newQuantities[groupKey][option.id] = newQty;

      // Schedule cart update after state changes
      if (item && itemQuantity > 0 && cartKey) {
        setTimeout(() => {
          // We need to prepare updated selections before calling convertSelectedAddonsToAogList
          const updatedSelectedAddons = updateSelectedAddonsWithQuantities(
            selectedAddons,
            newQuantities
          );
          const selectedAogs = convertSelectedAddonsToAogList(
            updatedSelectedAddons
          );
          updateCartWithCurrentAddons(selectedAogs);

          // Call precheck API after updating addons
          if (onAddonsUpdate) {
            onAddonsUpdate();
          }
        }, 0);
      }

      return newQuantities;
    });

    // Update selected addons based on the same calculated new quantity
    // This ensures consistency between the two state updates
    setSelectedAddons((prevAddons) => {
      const newAddons = { ...prevAddons };
      const groupKey = groupId.toString();

      if (!newAddons[groupKey]) {
        newAddons[groupKey] = [];
      }

      // If the quantity would be zero, remove the option from selected addons
      if (newQty === 0) {
        newAddons[groupKey] = newAddons[groupKey].filter(
          (item) => item.id !== option.id
        );
      }
      // If the quantity is positive and not already in selected addons, add it once
      else if (!newAddons[groupKey].some((item) => item.id === option.id)) {
        newAddons[groupKey] = [...newAddons[groupKey], { ...option }];
      }

      return newAddons;
    });
  };

  // Helper function to update selected addons with quantity information
  const updateSelectedAddonsWithQuantities = (
    selections: Record<string, ItemAddonOption[]>,
    quantities: Record<string, Record<string, number>>
  ) => {
    const updatedSelections = { ...selections };

    // Update the items in item-qty groups with their quantities
    Object.entries(quantities).forEach(([groupId, itemQuantities]) => {
      if (!updatedSelections[groupId]) {
        updatedSelections[groupId] = [];
      }

      Object.entries(itemQuantities).forEach(([itemId, qty]) => {
        if (qty > 0) {
          // Find the item in the group
          const addonGroup = addonGroups.find(
            (g) => g.id.toString() === groupId
          );
          if (addonGroup) {
            const option = addonGroup.items.find((item) => item.id === itemId);
            if (option) {
              // Check if the item already exists in the selections
              const existingItemIndex = updatedSelections[groupId].findIndex(
                (item) => item.id === itemId
              );

              if (existingItemIndex >= 0) {
                // Update existing item
                updatedSelections[groupId][existingItemIndex] = {
                  ...updatedSelections[groupId][existingItemIndex],
                  quantity: qty
                };
              } else {
                // Add new item
                updatedSelections[groupId].push({
                  ...option,
                  quantity: qty
                });
              }
            }
          }
        }
      });
    });

    return updatedSelections;
  };

  // Modified updateCartWithCurrentAddons to include variationId
  const updateCartWithCurrentAddons = (selectedAogs?: AogList[]) => {
    if (!item || !cartKey || itemQuantity === 0) return;

    const currentQty = cart[item.sellerItemId]?.qty || 0;

    if (currentQty > 0) {
      const cartStore = useCartStore.getState();
      const tempItem = { ...item };

      // Remove existing items
      for (let i = 0; i < currentQty; i++) {
        cartStore.removeItem(tempItem, cartKey);
      }

      // Add back with new addons and variation
      for (let i = 0; i < currentQty; i++) {
        cartStore.addItem(tempItem, cartKey, selectedAogs);
        // Update the cart item with variationId after adding
        const cartItem = cartStore.getCartItem(tempItem.sellerItemId);
        if (cartItem) {
          cartItem.variationId = selectedVariation?.id;
        }
      }

      if (onAddonsUpdate) {
        onAddonsUpdate();
      }
    }
  };

  // Modified handleAddItemWithQuantity to include variationId
  const handleAddItemWithQuantity = (quantity: number = 1) => {
    if (!item || quantity <= 0 || !areRequiredSelectionsMade() || !cartKey)
      return;

    const selectedAogs = convertSelectedAddonsToAogList();

    for (let i = 0; i < quantity; i++) {
      addItem(item, cartKey, selectedAogs);
      // Update the cart item with variationId after adding
      const cartItem = useCartStore.getState().getCartItem(item.sellerItemId);
      if (cartItem) {
        cartItem.variationId = selectedVariation?.id;
      }
    }

    if (onAddonsUpdate) {
      onAddonsUpdate();
    }
  };

  // Modified convertSelectedAddonsToAogList to include variationId
  const convertSelectedAddonsToAogList = (
    customSelections?: Record<string, ItemAddonOption[]>
  ) => {
    const selectionsToUse = customSelections || selectedAddons;

    if (!selectionsToUse || Object.keys(selectionsToUse).length === 0)
      return undefined;

    const aogList: AogList[] = addonGroups
      .map((group) => {
        const groupId = group.id.toString();
        const addOnItemList: AddonItem[] = [];

        // For single/multiple selection groups
        if (group.selectionType !== "item-qty") {
          const selections = selectionsToUse[groupId] || [];
          for (const option of selections) {
            addOnItemList.push({
              id: option.id,
              name: option.name,
              price: option.price,
              qty: 1,
              seq: 0,
              diet: option.dietary || null
            });
          }
        }
        // For item-qty groups
        else {
          const quantities = addonQuantities[groupId] || {};
          for (const [itemId, qty] of Object.entries(quantities)) {
            if (qty > 0) {
              const option = group.items.find((item) => item.id === itemId);
              if (option) {
                addOnItemList.push({
                  id: itemId,
                  name: option.name,
                  price: option.price,
                  qty,
                  seq: 0,
                  diet: option.dietary || null
                });
              }
            }
          }
        }

        return {
          id: group.id,
          name: group.name,
          type: group.type,
          minSelect: group.minSelection,
          maxSelect: group.maxSelection,
          description: group.description || "",
          seq: 0,
          addOnItemList
        };
      })
      .filter((aog) => aog.addOnItemList.length > 0);

    return aogList.length > 0 ? aogList : undefined;
  };

  // Check if an option is selected
  const isOptionSelected = (groupId: number, optionId: string) => {
    const groupSelections = selectedAddons[groupId.toString()] || [];
    return groupSelections.some((item) => item.id === optionId);
  };

  // Get quantity for an item-qty option
  const getAddonQuantity = (groupId: number, optionId: string): number => {
    return addonQuantities[groupId.toString()]?.[optionId] || 0;
  };

  // Check if all required selections are made
  const areRequiredSelectionsMade = () => {
    if (!hasCustomizations) {
      // If there are variations, require one to be selected
      if (variations && !selectedVariation) {
        return false;
      }
      return true;
    }

    return addonGroups.every((group) => {
      if (!group.isMandatory) return true;

      // Check for regular addon groups (single/multiple)
      if (group.selectionType !== "item-qty") {
        const groupSelections = selectedAddons[group.id.toString()] || [];
        return groupSelections.length >= group.minSelection;
      }

      // For item-qty type, check total quantity of items
      const groupQuantities = addonQuantities[group.id.toString()] || {};
      const totalQuantity = Object.values(groupQuantities).reduce(
        (sum, qty) => sum + qty,
        0
      );
      return totalQuantity >= group.minSelection;
    });
  };

  // Handle quantity change for explicit quantity updates
  const changeQuantity = (newQty: number) => {
    if (!item || newQty < 0 || !cartKey) return;

    const diff = newQty - itemQuantity;

    if (diff > 0) {
      // Adding items (could be going from 0 to positive)
      // Use a single function call with the difference as quantity
      handleAddItemWithQuantity(diff);
    } else if (diff < 0) {
      // Removing items (could be going to 0)
      // Use a single function call with the difference as quantity
      handleRemoveItemWithQuantity(Math.abs(diff));
    }

    // Update local state directly to the new quantity
    setItemQuantity(newQty);
  };

  // Remove specified quantity of items from cart
  const handleRemoveItemWithQuantity = (quantity: number = 1) => {
    if (!item || itemQuantity <= 0 || quantity <= 0 || !cartKey) return;

    // Use cart store's removeItem method
    for (let i = 0; i < quantity; i++) {
      removeItem(item, cartKey);
    }

    // Call precheck API after removing items
    if (onAddonsUpdate) {
      onAddonsUpdate();
    }
  };

  // Simple remove one item from cart
  const handleRemoveItem = () => {
    if (item && itemQuantity > 0 && cartKey) {
      // Use cart store's removeItem method
      removeItem(item, cartKey);

      // Update local quantity state
      setItemQuantity((prevQty) => Math.max(0, prevQty - 1));

      // Call precheck API after removing item
      if (onAddonsUpdate) {
        onAddonsUpdate();
      }
    }
  };

  // Get group selection text helper
  const getSelectionText = (group: ItemAddonGroup) => {
    if (group.isMandatory) {
      if (group.selectionType === "item-qty") {
        return `Required • Select at least ${group.minSelection} items`;
      }
      return `Required • Select any ${group.minSelection} option`;
    }
    if (group.selectionType === "item-qty") {
      return `Add up to ${group.maxSelection || "multiple"} items`;
    }
    return `Select up to ${group.maxSelection} options`;
  };

  // Handle variation selection
  const handleVariationSelection = (variation: ItemVariationDto) => {
    setSelectedVariation(variation);
    // Reset selected addons when variation changes
    setSelectedAddons({});
  };

  // Loading skeleton UI
  if (!item) {
    return <LoadingSkeleton />;
  }

  // Render the full customization version
  return (
    <div className="flex flex-col rounded-t-lg bg-gray-100 text-lg">
      {/* Original Item Info with Images and Details */}
      <div className="rounded-md mb-2 w-full py-2 px-2">
        <div className="flex flex-col w-full h-full gap-2">
          <ItemImageGallery item={item} />
          <ItemBasicDetails
            item={item}
            totalPrice={totalPrice}
            hasCustomizations={hasCustomizations}
            approxPricing={approxPricing}
          />
        </div>
      </div>

      {/* Variation Selection */}
      {variations && (
        <div className="bg-white rounded-md shadow-sm p-4 mb-2">
          <div className="mb-2">
            <h3 className="font-medium text-gray-800">Select Variation</h3>
            <p className="text-xs text-gray-500">Choose one option</p>
          </div>
          <div className="space-y-2">
            {item.itemVariationList?.map((variation) => (
              <SingleSelectionOption
                key={variation.id}
                option={{
                  id: variation.id.toString(),
                  name: variation.name,
                  price: variation.price,
                  inStock: true,
                  isSelected: selectedVariation?.id === variation.id,
                  dietary: null
                }}
                isSelected={selectedVariation?.id === variation.id}
                inStock={true}
                onSelect={() => handleVariationSelection(variation)}
                dietary={null}
              />
            ))}
          </div>
        </div>
      )}

      {/* Customization Groups */}
      <div className="rounded-md overflow-auto py-2 px-2">
        {addonGroups.map((group) => (
          <AddonGroup
            key={group.id}
            group={group}
            handleAddonSelection={handleAddonSelection}
            handleAddonQuantityChange={handleAddonQuantityChange}
            isOptionSelected={isOptionSelected}
            getAddonQuantity={getAddonQuantity}
            getSelectionText={getSelectionText}
          />
        ))}
      </div>

      {/* Bottom action bar */}
      <ActionBar
        item={item}
        totalPrice={totalPrice}
        itemQuantity={itemQuantity}
        areRequiredSelectionsMade={areRequiredSelectionsMade}
        handleRemoveItem={handleRemoveItem}
        changeQuantity={changeQuantity}
        handleItemAddComplete={() => {
          if (onAdd) {
            onAdd(item, selectedAddons);
          }
        }}
      />
    </div>
  );
};

export default ItemCustomization;
