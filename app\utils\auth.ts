import { User } from "~/types";
import { commitSession, destroySession, getSession } from "./session.server";
import { redirect, json, Session, TypedResponse } from "@remix-run/node";
import { parseJWT } from "./token-utils";
import { DecodedToken } from "~/types/user";
import { getAppSource } from "./loader";

interface AuthSuccess {
  session: Session;
  access_token: string;
  user: User;
  decoded?: DecodedToken;
}

type AuthFailure = TypedResponse<null>;

export const requireAuth = async (
  request: Request,
  decodeToken = false
): Promise<AuthSuccess | AuthFailure> => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;
  const url = new URL(request.url);
  const appSource = getAppSource(request);
  let decoded = undefined;

  try {
    if (!access_token || !user) {
      const headers = new Headers();
      headers.append("Set-Cookie", await destroySession(session));
      session = await getSession();
      session.set("appConfig", { appSource, appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return redirect(`/login?redirectTo=${url.pathname}`, { headers });
    }

    if (decodeToken) {
      decoded = parseJWT(access_token) as DecodedToken;
      if (!decoded || !decoded.userDetails) {
        const headers = new Headers();
        session = await getSession();
        session.set("appConfig", { appSource, appStartRoute: url.pathname });
        headers.append("Set-Cookie", await commitSession(session));
        return redirect(`/login?redirectTo=${url.pathname}`, { headers });
      }
    }
  } catch (error) {
    console.error("Error while checking authentication:", error);
    throw json(
      { session, access_token: null, user: null },
      { status: 400, statusText: "Error while checking authentication:" }
    );
  }

  return { session, access_token, user, decoded };
};

export function isAuthSuccess(
  result: AuthSuccess | AuthFailure
): result is AuthSuccess {
  return (result as AuthSuccess).session !== undefined;
}
