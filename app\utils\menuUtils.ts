import { AogList, AvailableItem, CartItem, ItemCategoryDtos } from "~/types";
import { FilterTag } from "~/components/restaurant/FilterBar";
import {
  ItemAddonGroup,
  ItemAddonOption
} from "~/components/restaurant/ItemCustomization";
import { performFuzzySearch, FuseSearchOptions } from "~/utils/fuseUtils";

// Search configuration options
export const searchConfig = {
  useAbbreviationDictionary: true, // Can be toggled to disable food abbreviation dictionary
  debounceTime: 300, // milliseconds
  fuseThreshold: 0.6, // 0.0 = exact match, 1.0 = match anything
  maxResults: 50, // Maximum number of search results to return
  shortTermLength: 3 // Maximum length to consider as a short search term
};

// Cache for memoizing filter results
const filterCache = new Map<string, AvailableItem[]>();

// Map common food abbreviations to their full words for better matching
export const commonFoodAbbreviations: Record<string, string[]> = {
  chk: ["chicken"],
  ckn: ["chicken"],
  chkn: ["chicken"],
  mlk: ["milk"],
  brg: ["burger"],
  brgr: ["burger"],
  veg: ["vegetable", "vegetables", "vegetarian"],
  nvg: ["non veg", "non-veg", "nonveg"],
  nveg: ["non veg", "non-veg", "nonveg"],
  pnr: ["paneer"],
  mtn: ["mutton"],
  rst: ["roast", "roasted"],
  frd: ["fried"],
  tmt: ["tomato"],
  pts: ["potato", "potatoes"],
  msh: ["mushroom"],
  chs: ["cheese"],
  btr: ["butter"],
  spc: ["spicy", "spice"],
  egg: ["egg"],
  crd: ["curd"],
  rce: ["rice"],
  bns: ["beans"],
  crm: ["cream"],
  sld: ["salad"],
  scp: ["soup"],
  grl: ["grill", "grilled"],
  msl: ["masala"],
  // Additional abbreviations
  alu: ["aloo", "potato"],
  alo: ["aloo", "potato"],
  dhl: ["daal", "dal", "lentil"],
  dal: ["daal", "lentil"],
  pza: ["pizza"],
  ndn: ["indian"],
  chn: ["chinese", "china"],
  rstrnt: ["restaurant"],
  brn: ["biriyani", "biryani"],
  bry: ["biriyani", "biryani"],
  cury: ["curry"],
  rta: ["raita"],
  sft: ["soft"],
  drk: ["drink"],
  bvg: ["beverage"],
  frt: ["fruit", "fruits"],
  vgtb: ["vegetable", "vegetables"],
  sbs: ["sabzi", "sabji"],
  sbj: ["sabzi", "sabji"],
  chi: ["chicken", "chilli"],
  chli: ["chilli", "chili"],
  vgrl: ["veg roll", "vegetable roll"],
  sand: ["sandwich"],
  swt: ["sweet"],
  dsrt: ["dessert"],
  brd: ["bread"],
  frsh: ["fresh"],
  wtr: ["water"],
  jce: ["juice"],
  pckl: ["pickle"],
  ach: ["achar", "pickle"],
  cht: ["chaat", "chat"],
  smsa: ["samosa"],
  pkra: ["pakora", "pakoda"],
  scs: ["sauce", "sauces"],
  fsh: ["fish"]
};

// Common tag constants
export const COMMON_TAGS = {
  VEG: "Veg",
  NON_VEG: "NonVeg",
  EGG: "Egg",
  BESTSELLER: "BestSeller",
  SPICY: "Spicy"
};

/**
 * Extract tags from an item
 */
export const extractTags = (item: AvailableItem): Set<string> => {
  const tagSet = new Set<string>();

  // Add dietary tag based on the item's diet property
  if (item.diet && item.diet.toLowerCase() !== "na") {
    tagSet.add(item.diet);
  }
  // For backward compatibility, check itemTag as well
  else if (
    item.itemTag?.toLowerCase().includes("veg") &&
    !item.itemTag?.toLowerCase().includes("nonveg")
  ) {
    tagSet.add(COMMON_TAGS.VEG);
  } else if (item.itemTag?.toLowerCase().includes("nonveg")) {
    tagSet.add(COMMON_TAGS.NON_VEG);
  }

  // If the item has explicit tags, add them
  if (item.itemTag) {
    // Handle comma-separated tags in itemTag
    const tagParts = item.itemTag
      .split(",")
      .map((tag) => tag.trim().toLowerCase())
      .filter((tag) => tag && !tag.includes("tag"))
      .filter((tag) => tag !== "na");

    // Add each part as a separate tag
    tagParts.forEach((part) => {
      if (part) tagSet.add(part);
    });

    // Also check for specific tags in the full itemTag string
    const itemTagLower = item.itemTag.toLowerCase();

    // Check for special tags
    if (itemTagLower.includes(COMMON_TAGS.BESTSELLER.toLowerCase())) {
      tagSet.add(COMMON_TAGS.BESTSELLER);
    }

    if (itemTagLower.includes(COMMON_TAGS.SPICY.toLowerCase())) {
      tagSet.add(COMMON_TAGS.SPICY);
    }

    if (itemTagLower.includes(COMMON_TAGS.EGG.toLowerCase())) {
      tagSet.add(COMMON_TAGS.EGG);
    }
  }

  // Final filter to ensure no "na" tags
  const filteredTagSet = new Set<string>();
  tagSet.forEach((tag) => {
    if (tag.toLowerCase() !== "na") {
      filteredTagSet.add(tag);
    }
  });

  return filteredTagSet;
};

/**
 * Filters items based on tags selected by the user
 */
function filterByTags(
  items: AvailableItem[],
  selectedTags: string[]
): AvailableItem[] {
  if (!selectedTags || selectedTags.length === 0) {
    return items;
  }

  // Convert selected tags to lowercase for case-insensitive comparison
  const lowerSelectedTags = selectedTags.map((tag) => tag.toLowerCase());

  return items.filter((item) => {
    // Extract all tags for this item
    const itemTags = extractTags(item);

    // Convert item tags to lowercase for comparison
    const lowerItemTags = new Set(
      [...itemTags].map((tag) => tag.toLowerCase())
    );

    // Check if any of the selected tags exist in this item's tags
    return lowerSelectedTags.some((tag) => lowerItemTags.has(tag));
  });
}

/**
 * Create regexes for character-by-character matching and prefix matching
 */
function createSearchRegexes(searchText: string): {
  searchRegex: RegExp;
  prefixRegex: RegExp;
} {
  // Character-by-character search pattern (e.g., "mlk" -> /m.*l.*k/i)
  const searchRegex = new RegExp(searchText.split("").join(".*"), "i");

  // Word prefix search pattern (e.g., "chi" -> /\bchi/i)
  const prefixRegex = new RegExp(`\\b${searchText}`, "i");

  return { searchRegex, prefixRegex };
}

/**
 * Get possible full words from the abbreviation dictionary
 */
function getAbbreviationMatches(
  searchText: string,
  useAbbreviations: boolean
): RegExp[] {
  if (!useAbbreviations) return [];

  const possibleFullWords =
    commonFoodAbbreviations[searchText.toLowerCase()] || [];
  return possibleFullWords.map((word) => new RegExp(`\\b${word}\\b`, "i"));
}

/**
 * Check if an item matches any of the provided regexes across its searchable fields
 */
function itemMatchesRegexes(
  item: AvailableItem,
  regexes: RegExp[],
  checkNameOnly: boolean = false
): boolean {
  return regexes.some((regex) => {
    const nameMatch = regex.test(item.itemName);
    if (checkNameOnly || nameMatch) return nameMatch;

    const descMatch = item.description && regex.test(item.description);
    const packMatch = item.packaging && regex.test(item.packaging);

    return nameMatch || descMatch || packMatch;
  });
}

/**
 * Perform search for short terms using regex and abbreviation matching
 */
function searchShortTerm(
  items: AvailableItem[],
  searchText: string,
  useAbbreviations: boolean
): AvailableItem[] | null {
  // Create basic regex patterns for searching
  const { searchRegex, prefixRegex } = createSearchRegexes(searchText);
  const basicRegexes = [searchRegex, prefixRegex];

  // Get abbreviation regexes if enabled
  const wordRegexes = getAbbreviationMatches(searchText, useAbbreviations);

  // Combine results from all approaches
  const matches = items.filter((item) => {
    // Check direct regex matches first (always enabled)
    if (itemMatchesRegexes(item, basicRegexes)) return true;

    // If abbreviations are enabled, check those too
    if (wordRegexes.length > 0) {
      return itemMatchesRegexes(item, wordRegexes);
    }

    return false;
  });

  // If we found matches, return them, otherwise return null to try Fuse.js
  return matches.length > 0 ? matches : null;
}

/**
 * Search using Fuse.js fuzzy search (now using lazy loading)
 */
async function fuzzySearch(
  items: AvailableItem[],
  searchText: string,
  threshold: number = searchConfig.fuseThreshold
): Promise<AvailableItem[]> {
  const options: FuseSearchOptions = {
    threshold,
    maxResults: searchConfig.maxResults,
    searchFields: [
      { name: "itemName", weight: 3 },
      { name: "packaging", weight: 1 },
      { name: "description", weight: 1 }
    ]
  };

  return performFuzzySearch(items, searchText, options);
}

/**
 * Create a cache key for memoizing search results
 */
function createCacheKey(
  searchText: string,
  selectedTags: string[],
  itemsLength: number,
  useAbbreviations: boolean
): string {
  return `${searchText}|${selectedTags
    .sort()
    .join(",")}|${itemsLength}|${useAbbreviations}`;
}

/**
 * Manage the search results cache
 */
function manageCache(cacheKey: string, results: AvailableItem[]): void {
  // Limit cache size to prevent memory issues
  if (filterCache.size > 100) {
    filterCache.clear();
  }

  // Cache the result
  filterCache.set(cacheKey, results);
}

/**
 * Main search function that filters items based on search text and selected tags
 * This now supports both synchronous regex search and asynchronous fuzzy search
 */
export const filterItems = (
  items: AvailableItem[],
  searchText: string,
  selectedTags: string[],
  options?: { useAbbreviationDictionary?: boolean }
): Promise<AvailableItem[]> | AvailableItem[] => {
  if (!items || !Array.isArray(items)) return [];

  // Use provided options or fall back to global config
  const useAbbreviations =
    options?.useAbbreviationDictionary !== undefined
      ? options.useAbbreviationDictionary
      : searchConfig.useAbbreviationDictionary;

  const trimmedSearchText = searchText.trim().toLowerCase();

  // Create a cache key based on the inputs
  const cacheKey = createCacheKey(
    trimmedSearchText,
    selectedTags,
    items.length,
    useAbbreviations
  );

  // Return cached result if available
  if (filterCache.has(cacheKey)) {
    return filterCache.get(cacheKey) || [];
  }

  // No active filters, return all items
  if (!trimmedSearchText && selectedTags.length === 0) {
    filterCache.set(cacheKey, items);
    return items;
  }

  // Step 1: Handle tag filtering first
  const tagFilteredItems = filterByTags(items, selectedTags);

  // Step 2: Handle search if search text exists
  if (!trimmedSearchText) {
    // Just tag filtering, no search
    manageCache(cacheKey, tagFilteredItems);
    return tagFilteredItems;
  }

  // For short search terms, try our specialized short-term search first
  if (trimmedSearchText.length <= searchConfig.shortTermLength) {
    const shortTermMatches = searchShortTerm(
      tagFilteredItems,
      trimmedSearchText,
      useAbbreviations
    );

    // If we found matches with the regex approach, use those (this is synchronous)
    if (shortTermMatches) {
      manageCache(cacheKey, shortTermMatches);
      return shortTermMatches;
    }
  }

  // If we got here, we need to use fuzzy search which is async
  return fuzzySearch(tagFilteredItems, trimmedSearchText).then((results) => {
    manageCache(cacheKey, results);
    return results;
  });
};

// Backward compatibility - synchronous version that doesn't use fuzzy search
// It only uses the regex-based approach (for short terms)
export const filterItemsSync = (
  items: AvailableItem[],
  searchText: string,
  selectedTags: string[],
  options?: { useAbbreviationDictionary?: boolean }
): AvailableItem[] => {
  if (!items || !Array.isArray(items)) return [];

  const useAbbreviations =
    options?.useAbbreviationDictionary !== undefined
      ? options.useAbbreviationDictionary
      : searchConfig.useAbbreviationDictionary;

  const trimmedSearchText = searchText.trim().toLowerCase();

  // No active filters, return all items
  if (!trimmedSearchText && selectedTags.length === 0) {
    return items;
  }

  // Handle tag filtering
  const tagFilteredItems = filterByTags(items, selectedTags);

  // If no search text, just return tag-filtered items
  if (!trimmedSearchText) {
    return tagFilteredItems;
  }

  // Try regex search - this will work well for short/simple terms
  const shortTermMatches = searchShortTerm(
    tagFilteredItems,
    trimmedSearchText,
    useAbbreviations
  );

  // Return regex matches if found, otherwise just return the tag filtered items
  return shortTermMatches || tagFilteredItems;
};

/**
 * Extracts unique tags from an array of items
 * Maintain backward compatibility with existing imports
 */
export const extractTagsFromItems = (items: AvailableItem[]): FilterTag[] => {
  // Create a Set to store all unique tags
  const tagSet = new Set<string>();

  // Process each item to extract tags
  items.forEach((item) => {
    // Extract all tags from the item
    const itemTags = extractTags(item);

    // Add each tag from the item to the tagSet
    itemTags.forEach((tag) => {
      if (tag.toLowerCase() !== "na") {
        tagSet.add(tag);
      }
    });
  });

  // Convert to array
  const tagArray = Array.from(tagSet);

  // Sort tags to prioritize VEG and NON_VEG tags
  tagArray.sort((a, b) => {
    // VEG should always come first
    if (a.toLowerCase() === COMMON_TAGS.VEG) return -1;
    if (b.toLowerCase() === COMMON_TAGS.VEG) return 1;

    // NON_VEG should come second
    if (a.toLowerCase() === COMMON_TAGS.NON_VEG) return -1;
    if (b.toLowerCase() === COMMON_TAGS.NON_VEG) return 1;

    // EGG should come third
    if (a.toLowerCase() === COMMON_TAGS.EGG) return -1;
    if (b.toLowerCase() === COMMON_TAGS.EGG) return 1;

    // Alphabetical sort for remaining tags
    return a.localeCompare(b);
  });

  // Convert to array of FilterTag objects
  return tagArray.map((tag, index) => {
    // Use exact COMMON_TAGS value if it exists
    const displayTag =
      Object.values(COMMON_TAGS).find(
        (t) => t.toLowerCase() === tag.toLowerCase()
      ) || tag.charAt(0).toUpperCase() + tag.slice(1).toLowerCase();

    const isNonVeg = tag === "nonveg";

    return {
      id: `tag-${index}`,
      label: isNonVeg ? "Non-Veg" : displayTag, // Will show "Veg", "Non-Veg", etc. when matching
      value: displayTag // Use the formatted version as value too
    };
  });
};

export const aogListToItemAddonGroup = (
  aogList: AogList[],
  cartItem: CartItem
): ItemAddonGroup[] => {
  // If there's no item data, return an empty array
  if (!aogList || aogList.length === 0) {
    return [];
  }

  // Create a mapping of addon IDs to their quantities from the cart
  const cartAddonQuantityMap: Record<string, Record<string, number>> = {};

  // Use flatAddons directly if available
  if (cartItem?.flatAddons && cartItem.flatAddons.length > 0) {
    // Build the quantity map directly from flatAddons
    cartItem.flatAddons.forEach((addon) => {
      // We need to find which group this addon belongs to
      const groupId = findAddonGroupId(aogList, addon.id);
      if (groupId) {
        if (!cartAddonQuantityMap[groupId]) {
          cartAddonQuantityMap[groupId] = {};
        }
        cartAddonQuantityMap[groupId][addon.id] = addon.qty;
      }
    });
  }

  return aogList.map((aog) => {
    // Determine the selection type based on max selection count
    // For item-qty types we check if there are items with qty > 1 in the cart
    let selectionType: "single" | "multiple" | "item-qty" = "single";

    if (aog.maxSelect > 1) {
      selectionType = "multiple";

      // Check if any items have qty > 1, which indicates item-qty type
      const hasItemWithMultipleQty = aog.addOnItemList?.some((item) => {
        const cartQty = cartAddonQuantityMap[aog.id.toString()]?.[item.id] || 0;
        return cartQty > 1;
      });

      if (hasItemWithMultipleQty) {
        selectionType = "item-qty";
      }
    }

    return {
      id: aog.id,
      name: aog.name,
      // Attempt to infer type from name or default to addOn
      type: getAddonGroupType(aog.name),
      isMandatory: aog.minSelect > 0,
      selectionType,
      minSelection: aog.minSelect,
      maxSelection: aog.maxSelect,
      description: aog.description,
      items:
        aog.addOnItemList?.map((item): ItemAddonOption => {
          // Get the quantity from cart if available
          const cartQty =
            cartAddonQuantityMap[aog.id.toString()]?.[item.id] || 0;

          // Determine if this item was selected from cart data
          const isSelected = cartQty > 0;

          // Only mark as default for new items with no cart data
          const isDefault = item.qty > 0 && !cartItem;

          return {
            id: item.id,
            name: item.name,
            price: item.price,
            inStock: true, // Assume in stock unless specified otherwise
            isSelected,
            isDefault,
            quantity: cartQty, // Include quantity for item-qty types
            dietary: item.diet || null,
            sId: item.sId
          };
        }) || []
    };
  });
};

// Helper function to determine addon group type from its name
const getAddonGroupType = (
  name: string
): "variant" | "addOn" | "customization" | "extra" => {
  const lowerName = name.toLowerCase();

  if (
    lowerName.includes("variant") ||
    lowerName.includes("size") ||
    lowerName.includes("choice")
  ) {
    return "variant";
  } else if (lowerName.includes("extra")) {
    return "extra";
  } else if (lowerName.includes("custom")) {
    return "customization";
  } else {
    return "addOn";
  }
};

// Helper function to find which group an addon belongs to
const findAddonGroupId = (
  aogList: AogList[],
  addonId: string
): string | null => {
  for (const aog of aogList) {
    if (aog.addOnItemList.some((item) => item.id === addonId)) {
      return aog.id.toString();
    }
  }
  return null;
};

/**
 * Determines if an item is available for purchase
 */
export const isItemAvailable = (item: AvailableItem): boolean => {
  return item.pricePerUnit > 0 && !item.soldout && !item.closed;
};

/**
 * Sorts items by availability (available items first), then by veg/non-veg status
 */
export const sortItemsByAvailabilityAndType = (
  items: AvailableItem[]
): AvailableItem[] => {
  return [...items].sort((a, b) => {
    // First sort by availability - available items come first
    const aAvailable = isItemAvailable(a);
    const bAvailable = isItemAvailable(b);

    if (aAvailable && !bAvailable) return -1;
    if (!aAvailable && bAvailable) return 1;

    // Then sort by veg/non-veg status
    // Group veg items together and non-veg items together
    if (a.diet && b.diet) {
      if (a.diet === "veg" && b.diet !== "veg") return -1;
      if (a.diet !== "veg" && b.diet === "veg") return 1;
    } else if (a.diet === "veg") {
      return -1;
    } else if (b.diet === "veg") {
      return 1;
    }

    // Finally, sort by name for consistent ordering
    return a.itemName.localeCompare(b.itemName);
  });
};

/**
 * Checks if all items in a category are unavailable
 */
export const areCategoryItemsAllUnavailable = (
  categoryId: number,
  itemsByCategory: Record<number, AvailableItem[]>
): boolean => {
  const items = itemsByCategory[categoryId] || [];
  return items.length > 0 && items.every((item) => !isItemAvailable(item));
};

/**
 * Sorts categories by availability (categories with available items first)
 */
export const sortCategoriesByAvailability = (
  categories: ItemCategoryDtos[],
  itemsByCategory: Record<number, AvailableItem[]>
): ItemCategoryDtos[] => {
  return [...categories].sort((a, b) => {
    const aAllUnavailable = areCategoryItemsAllUnavailable(
      a.id,
      itemsByCategory
    );
    const bAllUnavailable = areCategoryItemsAllUnavailable(
      b.id,
      itemsByCategory
    );

    if (aAllUnavailable && !bAllUnavailable) return 1;
    if (!aAllUnavailable && bAllUnavailable) return -1;

    // Maintain original ordering if availability is the same
    return 0;
  });
};

/**
 * Applies all sorting logic to the items and categories
 */
export const getSortedMenuData = (
  categories: ItemCategoryDtos[],
  itemsByCategory: Record<number, AvailableItem[]>
): {
  sortedCategories: ItemCategoryDtos[];
  sortedItemsByCategory: Record<number, AvailableItem[]>;
} => {
  // Sort categories by availability
  const sortedCategories = sortCategoriesByAvailability(
    categories,
    itemsByCategory
  );

  // Sort items in each category
  const sortedItemsByCategory: Record<number, AvailableItem[]> = {};

  Object.keys(itemsByCategory).forEach((categoryIdStr) => {
    const categoryId = parseInt(categoryIdStr, 10);
    sortedItemsByCategory[categoryId] = sortItemsByAvailabilityAndType(
      itemsByCategory[categoryId]
    );
  });

  return {
    sortedCategories,
    sortedItemsByCategory
  };
};
