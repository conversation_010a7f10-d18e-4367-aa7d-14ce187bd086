import { useNavigate } from "@remix-run/react";
import React, { useState, useRef, useEffect } from "react";
// Global Error Boundary
export default function ErrorModel({
  title = "Oh no ..",
  message,
  onClose,
  onRetry,
}: {
  title: string;
  message: string;
  onClose?: () => void;
  onRetry?: () => void;
}) {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(true);
  const popupRef = useRef<HTMLDivElement>(null);

  const closePopup = () => {
    setIsOpen(false);

    if (!onClose) {
      // navigate(-1);
    } else {
      onClose();
    }
  };

  const onRetryHandle = () => {
    if (onRetry) {
      onRetry();
    } else {
      navigate(".", { replace: true });
    }
    setIsOpen(false);
  };

  // Close the popup when clicking outside of it
  // useEffect(() => {
  //   function handleClickOutside(event: MouseEvent) {
  //     if (
  //       popupRef.current &&
  //       !popupRef.current.contains(event.target as Node)
  //     ) {
  //       closePopup();
  //     }
  //   }
  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => document.removeEventListener("mousedown", handleClickOutside);
  // }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 h-full w-full">
      <div
        ref={popupRef}
        className="bg-white p-5 rounded-lg w-5/6 shadow-lg text-center "
      >
        <h2 className="text-xl font-bold mb-6 text-pink-600">{title}</h2>
        <p className="text-gray-700 mb-6">{message}</p>
        <div className="flex justify-center">
          <button
            className="bg-pink-600 hover:bg-pink-800 text-white py-2 px-4 rounded mr-12"
            onClick={closePopup}
          >
            Close
          </button>
          <button
            className="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded"
            onClick={onRetryHandle}
          >
            Retry
          </button>
        </div>
      </div>
    </div>
  );
}
