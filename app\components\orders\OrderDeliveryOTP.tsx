import { Order } from "~/types";

export default function OrderDeliveryOTP({ order }: { order: Order }) {

  return (
    <div className="bg-white p-3 rounded-md shadow-[0px_2px_16px_0px_#0000001A]">
      <div className="flex flex-col">
        <div className="flex flex-row justify-between gap-2">
          <div>
            <p className="text-xl font-bold text-gray-900">Share the code with <br />delivery partner</p>
            <p className="mt-2 text-sm text-typography-400 max-w-sm">Required when delivery is far from location or for high value order </p>
          </div>
          <div className="w-[100px] h-[100px] min-w-[100px] min-h-[100px]">
            <img src="/delivery_otp.png" className="w-full h-full object-cover" />
          </div>
        </div>
        <div className="max-w-sm flex flex-row justify-between items-center gap-0.5 mt-4 mb-2 mx-2">
          {order.deliveryCode ? order.deliveryCode.split("").map((digit, index) => (
            <span
              key={index}
              className="w-16 h-10 rounded-md bg-primary-50 text-primary border-2 border-primary-200 flex items-center justify-center text-xl font-bold"
            >
              {digit}
            </span>
          )): null}
        </div>
      </div>
    </div>
  );
}