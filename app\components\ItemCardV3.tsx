import React, { useState } from "react";
import { AvailableItem, ImageViewType } from "~/types";
import Button from "./Button";
import ItemDetails from "./chooseitem/ItemDetails";
import AddItemButton from "./chooseitem/AddItemButton";
import Badge from "./chooseitem/Badge";
import AddItemButtonV2 from "./chooseitem/AddItemButtonV2";
import { capitalizeFirstLetter, formatPrice } from "~/utils/string";

const GetBadge: React.FC<{ itemDetails: AvailableItem }> = ({
  itemDetails
}) => {
  if (itemDetails.closed || itemDetails.soldout) {
    return (
      <Badge
        color="orange"
        text={`Closed`}
        className="absolute right-0 top-0 rounded-tr-md bg-red-600"
      />
    );
  }

  if (itemDetails.discPerc > 0) {
    return (
      <Badge
        color="blue"
        text={`${itemDetails.discPerc}% OFF`}
        className="absolute right-0 top-0 rounded-tr-md"
      />
    );
  }

  if (itemDetails.newlyAdded) {
    return (
      <Badge
        color="orange"
        text="Newly Added"
        className="absolute right-0 top-0 rounded-tr-md"
      />
    );
  }
};

interface ItemCardProps {
  itemDetails: AvailableItem;
  amount: number;
  qty: number;
  onAdd: () => void;
  onRemove: () => void;
  approxPricing: boolean;
  imageViewType: ImageViewType;
}

const ItemCard: React.FC<ItemCardProps> = ({
  itemDetails,
  amount,
  qty,
  onAdd,
  onRemove,
  approxPricing,
  imageViewType
}) => {
  const [showItemDetails, setShowItemDetails] = useState(false);
  return (
    <div
      className={`relative flex flex-col justify-between bg-white rounded-xl`}
    >
      <div
      // className={`${
      //   itemDetails.closed || itemDetails.soldout ? "filter grayscale" : ""
      // }`}
      >
        <ItemInfo
          itemDetails={itemDetails}
          showItemDetails={showItemDetails}
          setShowItemDetails={setShowItemDetails}
          imageViewType={imageViewType}
          qty={qty}
          onAdd={onAdd}
          onRemove={onRemove}
          approxPricing={approxPricing}
        />
      </div>

      {/* current order calculations */}
      {amount > 0 && (
        <div className="py-1 px-2 rounded-b-xl flex justify-between items-center bg-gray-100 shadow-md shadow-gray-200 border-t border-dashed border-gray-300 transition-all duration-1000 ease-in-out">
          <span className="text-[.7rem] text-gray-700 font">{`${qty} x ${itemDetails.pricePerUnit}/${itemDetails.unit}`}</span>
          <span className="text text-[.7rem] font-semibold text-gray-700">
            {amount > 0 ? `₹ ${formatPrice(amount)}` : ""}
            {/* {!approxPricing && (
            <span className="text-[.6rem] text-gray-500">(approx)</span>
          )} */}
          </span>
        </div>
      )}

      {/* Show Badges */}
      {itemDetails.closed || itemDetails.soldout ? (
        <div className="absolute inset-0 bg-white bg-opacity-80 rounded-lg">
          <div className="relative">
            <GetBadge itemDetails={itemDetails} />
          </div>
        </div>
      ) : (
        <GetBadge itemDetails={itemDetails} />
      )}
      {showItemDetails && (
        <ItemDetails
          itemDetails={itemDetails}
          amount={amount}
          qty={qty}
          onAdd={onAdd}
          onRemove={onRemove}
          approxPricing={approxPricing}
          setShowItemDetails={setShowItemDetails}
        />
      )}
    </div>
  );
};

// Component for displaying item image, name, and badges
const ItemInfo: React.FC<{
  itemDetails: AvailableItem;
  showItemDetails: boolean;
  imageViewType: ImageViewType;
  setShowItemDetails: (value: boolean) => void;
  qty: number;
  onAdd: () => void;
  onRemove: () => void;
  approxPricing: boolean;
}> = ({
  itemDetails,
  showItemDetails,
  setShowItemDetails,
  qty,
  onAdd,
  onRemove,
  approxPricing
}) => (
  <div className="">
    <div className="relative flex flex-row w-full justify-between items-center">
      <div className="relative">
        <Button
          className=""
          onClick={() => setShowItemDetails(!showItemDetails)}
        >
          <img
            src={itemDetails.itemUrl}
            alt={""}
            className={`rounded-xl object-contain w-32`}
          />
        </Button>
        {/* <div className="absolute right-0 bottom-0"> */}
        {/* <AddItemButtonV2
          qty={qty}
          onAdd={onAdd}
          onRemove={onRemove}
          isDisabled={itemDetails.closed || itemDetails.soldout}
          unit={itemDetails.unit}
        /> */}
        {/* </div> */}
      </div>
      <div className="flex flex-col items-start p-2">
        <p className="text-xs text-gray-400 mt-1">{itemDetails.packaging}</p>
        {/* <p className="text-[.5rem] text-gray-600 mt-1 bg-gray-100 py-1 px-2 rounded-md mb-2 border font-semibold tracking-wider">
          {"10 pkt"}
        </p> */}

        <h2 className="text-xs font-medium text-gray-700 ">
          {itemDetails.itemName}
        </h2>
        <p className="text-[.65rem] text-gray-500 mt-1">
          {itemDetails.itemRegionalLanguageName}
        </p>
        <div className="flex flex-col flex-wrap pl-2 pb-2 items-start">
          <div className="flex">
            {itemDetails.discPerc > 0 && (
              <span className="text-[.6rem] font-normal text-gray-500 line-through">
                {`₹${itemDetails.strikeoffPrice}/${capitalizeFirstLetter(
                  itemDetails.unit
                )}`}
              </span>
            )}
            {itemDetails.discPerc > 0 && (
              <span className="text-[.6rem] font-semibold text-blue-600 ml-1">{`${itemDetails.discPerc}% OFF`}</span>
            )}
          </div>
          <div className="flex items-center">
            <span className="text-[.7rem] font-bold text-gray-700 tracking-wider">
              {`₹${itemDetails.pricePerUnit}/${capitalizeFirstLetter(
                itemDetails.unit
              )}`}
            </span>
            {approxPricing && (
              <span className="text-[.6rem] text-gray-500 ml-1">(approx)</span>
            )}
          </div>
        </div>
      </div>
      <div className="flex flex-col-reverse pr-2 pb-2">
        <div className="self-end">
          <AddItemButtonV2
            qty={qty}
            onAdd={onAdd}
            onRemove={onRemove}
            isDisabled={itemDetails.closed || itemDetails.soldout}
            unit={itemDetails.unit}
          />
        </div>
      </div>
    </div>
  </div>
);

export default ItemCard;
