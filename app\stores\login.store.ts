import { create } from "zustand";

interface LoginState {
  // State
  isLoginOpen: boolean;
  dismissible: boolean;
  redirectPath: string | null;

  // Actions
  openLogin: (dismissible?: boolean) => void;
  closeLogin: () => void;
  setRedirectPath: (path: string | null) => void;
}

export const useLoginStore = create<LoginState>((set) => ({
  // Initial state
  isLoginOpen: false,
  redirectPath: null,
  dismissible: false,

  // Actions
  openLogin: (dismissible = false) => {
    set({ isLoginOpen: true });
    if (dismissible) {
      set({ dismissible: true });
    } else {
      set({ dismissible: false });
    }
  },
  closeLogin: () => set({ isLoginOpen: false }),
  setRedirectPath: (path) => set({ redirectPath: path })
}));
