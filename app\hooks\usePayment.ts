import { useState, useCallback, useEffect, useRef } from "react";
import { useFetcher } from "@remix-run/react";
import {
  PaymentInitiateRequest,
  PaymentResponse,
  PaymentState,
  UsePaymentOptions
} from "~/types/payment.types";
import { RPPaymentDetails } from "~/types";

export const usePayment = (options?: UsePaymentOptions) => {
  const { onToast } = options || {};
  const fetcher = useFetcher<PaymentResponse>();
  const lastToastRef = useRef<string>("");
  const hasRedirectedRef = useRef(false);
  const razorpayShownRef = useRef(false);

  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<PaymentState["status"]>("idle");
  const [message, setMessage] = useState("");
  const [refId, setRefId] = useState<number | undefined>(undefined);
  const [pollingCount, setPollingCount] = useState(0);
  const [razorpayOptions, setRazorpayOptions] =
    useState<RPPaymentDetails | null>(null);
  const [showRazorpay, setShowRazorpay] = useState(false);
  const MAX_POLLING_COUNT = 25; // 2 minutes (24 * 5 seconds)
  const POLLING_INTERVAL = 5000; // 5 seconds

  const resetState = useCallback(() => {
    setIsOpen(false);
    setIsLoading(false);
    setStatus("idle");
    setMessage("");
    setPollingCount(0);
    setRefId(undefined);
    setRazorpayOptions(null);
    setShowRazorpay(false);
    lastToastRef.current = "";
    hasRedirectedRef.current = false;
    razorpayShownRef.current = false;
  }, []);

  const showToast = useCallback(
    (message: string, type: "success" | "error") => {
      // Only show toast if message is different from last shown message
      if (message !== lastToastRef.current && onToast) {
        lastToastRef.current = message;
        onToast(message, type);
      }
    },
    [onToast]
  );

  const handlePayNow = useCallback(
    (paymentRequest: PaymentInitiateRequest) => {
      // Complete reset before starting new payment
      resetState();

      // Add extra validation to prevent TypeError
      if (!paymentRequest || typeof paymentRequest !== "object") {
        console.error("Invalid payment request:", paymentRequest);
        showToast("Unable to process payment: Invalid request data", "error");
        return;
      }

      if (
        typeof paymentRequest.amount !== "number" ||
        isNaN(paymentRequest.amount)
      ) {
        console.error("Invalid payment amount:", paymentRequest.amount);
        showToast("Unable to process payment: Invalid amount", "error");
        return;
      }

      if (!paymentRequest.orderGroupId) {
        console.error("Invalid order group ID:", paymentRequest.orderGroupId);
        showToast("Unable to process payment: Invalid order ID", "error");
        return;
      }

      setIsOpen(true);
      setIsLoading(true);
      setStatus("idle");
      setMessage("Initiating payment...");

      try {
        const formData = new FormData();
        formData.append("requestName", "InitiatePayment");
        formData.append("amount", paymentRequest.amount.toFixed(2));
        formData.append("orderGroupId", paymentRequest.orderGroupId.toString());
        if (paymentRequest.note) {
          formData.append("note", paymentRequest.note);
        }

        fetcher.submit(formData, {
          method: "post",
          action: "/resources/payment"
        });
      } catch (error) {
        console.error("Error submitting payment:", error);
        setStatus("failed");
        setMessage("Error initializing payment");
        setIsLoading(false);
        showToast("Payment initialization failed. Please try again.", "error");
      }
    },
    [fetcher, showToast, resetState]
  );

  const handleDeposit = useCallback(
    (preconfirmUid: string, amount: number) => {
      // Complete reset before starting new payment
      resetState();

      if (!preconfirmUid) {
        console.error("Invalid preconfirmUid:", preconfirmUid);
        showToast("Unable to process payment: Missing preconfirm ID", "error");
        return;
      }

      if (typeof amount !== "number" || isNaN(amount) || amount <= 0) {
        console.error("Invalid deposit amount:", amount);
        showToast("Unable to process payment: Invalid amount", "error");
        return;
      }

      setIsOpen(true);
      setIsLoading(true);
      setStatus("idle");
      setMessage("Initiating deposit payment...");

      try {
        const formData = new FormData();
        formData.append("requestName", "InitiateDeposit");
        formData.append("preconfirmUid", preconfirmUid);
        formData.append("amount", amount.toFixed(2));

        fetcher.submit(formData, {
          method: "post",
          action: "/resources/payment"
        });
      } catch (error) {
        console.error("Error submitting deposit:", error);
        setStatus("failed");
        setMessage("Error initializing deposit");
        setIsLoading(false);
        showToast("Deposit initialization failed. Please try again.", "error");
      }
    },
    [fetcher, showToast, resetState]
  );

  // Handle Razorpay payment success
  const handleRazorpaySuccess = useCallback(() => {
    console.log("Razorpay payment successful");
    razorpayShownRef.current = false;
    setShowRazorpay(false);

    setIsOpen(true);
    setIsLoading(true);
    setStatus("idle");
    setMessage("Verifying payment...");

    if (refId) {
      const formData = new FormData();
      formData.append("requestName", "PaymentStatus");
      formData.append("refId", refId.toString());
      formData.append("razorpaySuccess", "true");

      fetcher.submit(formData, {
        method: "post",
        action: "/resources/payment"
      });
    }
  }, [fetcher, refId]);

  // Handle Razorpay payment failure
  const handleRazorpayFailure = useCallback(
    (error: Error) => {
      console.log("Razorpay payment failed:", error);
      razorpayShownRef.current = false;
      setStatus("failed");
      setMessage(error.message || "Payment failed. Please try again.");
      setIsLoading(false);
      setShowRazorpay(false);
      showToast(error.message || "Payment failed. Please try again.", "error");
    },
    [showToast]
  );

  // Handle Razorpay modal close
  const handleRazorpayClose = useCallback(() => {
    console.log("Razorpay modal closed");
    razorpayShownRef.current = false;
    setShowRazorpay(false);

    // If payment was not completed, set status to failed
    if (status !== "success") {
      setStatus("failed");
      setMessage("Payment cancelled by user");
      setIsLoading(false);
      showToast("Payment was cancelled", "error");
    }
  }, [status, showToast]);

  // Handle payment URL and start polling
  useEffect(() => {
    const data = fetcher.data as PaymentResponse | undefined;
    if (!data) return;

    console.log("Payment response received:", data);

    // Handle errors from API
    if (data.errors || (data.success === false && data.message)) {
      const errorMsg =
        data.message ||
        data.errors?.auth ||
        data.errors?.requestName ||
        data.errors?.refId ||
        "Unable to process payment";

      setStatus("failed");
      setMessage(errorMsg);
      setIsLoading(false);

      showToast(errorMsg, "error");
      return;
    }

    // Handle Razorpay payment option
    if (data.razorpayOptions && !razorpayShownRef.current) {
      console.log("Showing Razorpay with options:", data.razorpayOptions);
      setStatus("processing");
      setMessage("Processing payment...");
      setRefId(data.refId as number);
      setRazorpayOptions(data.razorpayOptions);

      // Set flag before showing to prevent multiple instances
      razorpayShownRef.current = true;
      setShowRazorpay(true);
      return;
    }

    // Handle UPI payment URL
    if (data.paymentUrl && !hasRedirectedRef.current) {
      setStatus("processing");
      setMessage("Processing payment...");
      setRefId(data.refId as number);
      hasRedirectedRef.current = true;
      window.location.href = data.paymentUrl;
    } else if (data.errors) {
      setStatus("failed");
      setMessage("Unable to process payment. Please try again.");
      setIsLoading(false);

      showToast("Unable to process payment. Please try again.", "error");
    }
  }, [fetcher.data, showToast]);

  // Payment status polling
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    // Don't start polling if payment is already successful
    if (status === "success") {
      return;
    }

    // Only start polling if modal is open, status is processing, refId exists, and not showing Razorpay
    if (isOpen && status === "processing" && refId && !showRazorpay) {
      setMessage("Verifying payment status...");
      setIsLoading(true);

      intervalId = setInterval(() => {
        if (pollingCount >= MAX_POLLING_COUNT) {
          const timeoutMsg =
            "Payment verification timeout. Please check your bank account or try again.";

          // Update these states to show failed state in modal
          setStatus("failed");
          setMessage(timeoutMsg);
          setIsLoading(false);

          // Reset Razorpay flags
          razorpayShownRef.current = false;

          // Show toast notification
          showToast(timeoutMsg, "error");

          // Clear the interval to stop polling
          clearInterval(intervalId);
          return;
        }

        const formData = new FormData();
        formData.append("requestName", "PaymentStatus");
        formData.append("refId", refId.toString());

        fetcher.submit(formData, {
          method: "post",
          action: "/resources/payment"
        });

        setPollingCount((prev) => prev + 1);
      }, POLLING_INTERVAL);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isOpen, status, refId, pollingCount, fetcher, showToast, showRazorpay]);

  // Handle payment status response
  useEffect(() => {
    const data = fetcher.data as PaymentResponse | undefined;
    if (!data?.paymentStatus) return;

    switch (data.paymentStatus) {
      case "SUCCESS":
        setStatus("success");
        setMessage("Payment successful!");
        setIsLoading(false);
        setShowRazorpay(false);
        razorpayShownRef.current = false;

        showToast("Payment successful!", "success");
        break;
      case "SUCCESS_NOT_CNF":
        setStatus("success_not_cnf");
        setMessage(data.message || "Payment success but order not confirmed");
        setIsLoading(false);
        setShowRazorpay(false);
        razorpayShownRef.current = false;

        showToast(
          data.message || "Payment success but order not confirmed",
          "success"
        );
        break;
      case "FAILED":
        setStatus("failed");
        setMessage(data.message || "Payment failed. Please try again.");
        setIsLoading(false);
        setShowRazorpay(false);
        razorpayShownRef.current = false;

        showToast(data.message || "Payment failed. Please try again.", "error");
        break;
      case "PENDING":
        setStatus("processing");
        setMessage("Payment is being processed...");
        setIsLoading(true);
        break;
    }
  }, [fetcher.data, showToast]);

  // Handle API errors during submission
  useEffect(() => {
    if (fetcher.state === "submitting") {
      setIsLoading(true);
    } else if (
      fetcher.state === "idle" &&
      fetcher.data === undefined &&
      isLoading
    ) {
      const hasError = fetcher.data === undefined && status !== "success";

      if (hasError) {
        setStatus("failed");
        setMessage("Network error. Please try again.");
        setIsLoading(false);
        razorpayShownRef.current = false;

        showToast("Network error. Please try again.", "error");
      }
    }
  }, [fetcher.state, fetcher.data, isLoading, status, showToast]);

  return {
    isOpen,
    isLoading,
    status,
    message,
    refId,
    razorpayOptions,
    showRazorpay,
    handlePayNow,
    handleDeposit,
    handleClose: resetState,
    handleRazorpaySuccess,
    handleRazorpayFailure,
    handleRazorpayClose,
    isProcessing: fetcher.state === "submitting" || isLoading
  };
};
