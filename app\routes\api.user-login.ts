// import PrimaryButton from "~/components/PrimaryButton";
import { json, ActionFunction } from "@remix-run/node";
import { requestOtp, verifyOtp, getDeviceInfo } from "~/services/auth.server";
import { getSession, commitSession } from "~/utils/session.server";
import { parseJWT } from "@utils/token-utils";

import { User } from "~/types";

interface ActionData {
  success?: boolean;
  message?: string;
  user?: User;
  intent?: string;
}

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent");
  const phoneNumber = formData.get("phoneNumber") as string;
  const otp = formData.get("otp") as string;

  const deviceInfo = getDeviceInfo();

  try {
    if (intent === "getOtp") {
      const payload = {
        app: "seller_app",
        mobileNumber: phoneNumber,
        password: "",
        admin: false,
        deviceInfo
      };

      await requestOtp(payload);

      return json<ActionData>({
        success: true,
        message: "OTP sent successfully",
        intent: "getOtp"
      });
    } else if (intent === "verifyOtp") {
      if (!phoneNumber) {
        return json<ActionData>(
          {
            success: false,
            message: "Phone number is required for OTP verification",
            intent: "verifyOtp"
          },
          { status: 400 }
        );
      }

      const verifyResponse = (await verifyOtp(phoneNumber, otp, "", request))
        .data;

      const session = await getSession(request.headers.get("Cookie"));
      session.set("access_token", verifyResponse.access_token ?? "");
      session.set("refresh_token", verifyResponse.refresh_token ?? "");

      const tokenData = parseJWT(verifyResponse.access_token ?? "");
      const userDetails = tokenData.userDetails;

      const user: User = {
        userId: userDetails.userId,
        userName: userDetails.userName,
        businessName: userDetails.businessName,
        buyerId: userDetails.buyerId
      };
      session.set("user", user);

      return json<ActionData>(
        {
          success: true,
          message: "OTP verified successfully",
          intent: "verifyOtp",
          user
        },
        {
          headers: {
            "Set-Cookie": await commitSession(session)
          }
        }
      );
    } else if (intent === "clearActionData") {
      return json<ActionData>({
        success: true,
        message: "",
        intent: "clearActionData"
      });
    } else {
      return json<ActionData>(
        { success: false, message: "Invalid intent" },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.log("Request error", error);
    return json<ActionData>({
      success: false,
      message: error instanceof Error ? error.message : "Something went wrong"
    });
  }
};
