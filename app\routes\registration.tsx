import { BackNavHeader } from "./../components/BackNavHeader";
// ~/routes/registration.tsx
import { useState, useEffect } from "react";
import {
  Form,
  useActionData,
  useNavigation,
  useNavigate,
  useLoaderData
} from "@remix-run/react";
import {
  json,
  ActionFunction,
  redirect,
  LoaderFunction
} from "@remix-run/node";
import Button from "@components/Button"; // Using existing Button component
import { registerUser } from "~/services/auth.server";
import MapPicker from "~/components/MapPicker";
// import ChangeAddress from "~/routes/changeaddress";

interface ActionData {
  success?: boolean;
  message?: string;
  errors?: {
    [key: string]: string;
  };
}

interface LoaderData {
  token: string;
  phoneNumber: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  const url = new URL(request.url);
  const token = url.searchParams.get("token");
  const phoneNumber = url.searchParams.get("phoneNumber");

  if (!token || !phoneNumber) {
    return redirect("/login");
  }

  return json<LoaderData>({ token, phoneNumber });
};

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const fullName = formData.get("fullName") as string;
  const latitude = formData.get("latitude") as string;
  const longitude = formData.get("longitude") as string;
  const address = formData.get("address") as string;
  const mobileNumber = formData.get("mobileNumber") as string;
  const token = formData.get("token") as string;

  if (!fullName) {
    throw json<ActionData>({
      success: false,
      message: "Full name is required"
    });
  }

  if (!latitude || !longitude || !address) {
    throw json<ActionData>({
      success: false,
      message: "Address is required"
    });
  }

  try {
    const response = await registerUser(
      request,
      {
        fullName,
        mobileNumber,
        address,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        networkId: 0
      },
      token
    );

    if (response.data.userId) {
      return redirect("/login?phoneNumber=" + mobileNumber);
    } else {
      throw json<ActionData>({
        success: false,
        message: "Registration failed"
      });
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw json<ActionData>(
        {
          success: false,
          message: error.message
        },
        500
      );
    } else {
      throw json<ActionData>(
        {
          success: false,
          message: "Something went wrong"
        },
        500
      );
    }
  }
};

const googleMapsApiKey = "AIzaSyDBh6D6NIEiH08bj01ybByaayfM1T7W6XY"; // Adjust based on your setup

export default function Registration() {
  const [step, setStep] = useState(1);
  const [fullName, setFullName] = useState("");
  const [latitude, setLatitude] = useState("");
  const [longitude, setLongitude] = useState("");
  const [address, setAddress] = useState("");
  const [error, setError] = useState("");

  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const { token, phoneNumber } = useLoaderData<LoaderData>();
  const navigate = useNavigate();

  const handleNext = () => {
    setError("");

    if (step === 1) {
      if (!fullName || fullName.trim() === "") {
        setError("Full Name is required");
        setStep(1);
        return;
      }
    }

    if (step < 3) setStep(step + 1);
  };

  const handleBack = () => {
    if (step > 1) setStep(step - 1);
    else if (step === 1) {
      navigate(`/login?token=${token}&phoneNumber=${phoneNumber}`);
    }
  };

  const handleLocationSelect = (lat: string, lng: string) => {
    setLatitude(lat);
    setLongitude(lng);
  };

  // Optional: Reset form on successful registration
  useEffect(() => {
    if (actionData?.success) {
      // Optionally navigate or reset form fields
    }

    if (actionData?.message) {
      setError(actionData.message);
    }
  }, [actionData]);

  return (
    <div className="fixed inset-0 flex flex-col bg-white">
      {/* Header with back button */}
      {step === 1 ? (
        <BackNavHeader buttonText="Create Profile" handleBack={handleBack} />
      ) : (
        <BackNavHeader
          buttonText="Choose delivery location"
          handleBack={handleBack}
        />
      )}

      {/* Form Content */}
      <div className="flex-1 flex flex-col justify-between w-full h-full">
        <div className="w-full max-w-lg  h-full">
          <Form
            method="post"
            className="flex flex-col h-full"
            onSubmit={(e) => {
              if (fullName.trim().length === 0 || address.trim().length === 0) {
                e.preventDefault();
              }
            }}
          >
            {/* Hidden Inputs for All Fields */}
            <input type="hidden" name="token" value={token} />
            <input type="hidden" name="mobileNumber" value={phoneNumber} />
            <input type="hidden" name="fullName" value={fullName} />
            <input type="hidden" name="latitude" value={latitude} />
            <input type="hidden" name="longitude" value={longitude} />
            <input type="hidden" name="address" value={address} />
            {step === 1 && (
              <div className="mb-6 px-4 py-8">
                <label
                  htmlFor="fullName"
                  className="block text-sm font-semibold text-gray-700 mb-2"
                >
                  Enter Your Name
                </label>
                <input
                  type="text"
                  name="fullName"
                  id="fullName"
                  placeholder="Full Name"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-600 bg-white"
                />
              </div>
            )}
            {step === 2 && (
              <div className="h-full w-full">
                <MapPicker
                  googleMapsApiKey={googleMapsApiKey}
                  latitude={latitude}
                  longitude={longitude}
                  onLocationSelect={handleLocationSelect}
                />
              </div>
            )}
            {step === 3 && (
              <div className="mb-6 px-4 pt-8">
                <label
                  htmlFor="address"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Address
                </label>
                <textarea
                  name="address"
                  id="address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-600 bg-white"
                  rows={4}
                />
              </div>
            )}
            {/* Error Message */}
            {error && (
              <div className="mt-4 p-3  text-red-500 rounded-md mx-4 text-sm">
                {"* "}
                {error}
              </div>
            )}
            {/* Spacer to push buttons to the bottom */}
            <div className="flex-grow"></div>
            {/* Navigation Buttons */}
            <div className="flex justify-end space-x-4 z-10">
              {step < 3 ? (
                <Button
                  type="button"
                  onClick={handleNext}
                  disabled={fullName.trim().length === 0}
                  className="m-4 px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 w-full"
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={
                    navigation.state === "submitting" ||
                    address.trim().length === 0
                  }
                  className={`m-4 px-4 py-2 rounded-md w-full ${
                    navigation.state === "submitting"
                      ? "bg-teal-300 text-teal-700 cursor-not-allowed"
                      : "bg-teal-600 hover:bg-teal-700 text-white"
                  }`}
                >
                  {navigation.state === "submitting"
                    ? "Submitting..."
                    : "Complete Registration"}
                </Button>
              )}
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}

import ErrorBoundaryComponent from "~/components/ErrorBoundary";
export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(-1)} />;
}
