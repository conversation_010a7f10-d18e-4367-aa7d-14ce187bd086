import React from "react";
import { useNavigate } from "@remix-run/react";
import AddressCard from "./AddressCard";
import Button from "~/components/Button";
import { ChevronRight, Plus } from "lucide-react";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { AddressDto, AddressType } from "~/types/address.types";

export interface AddressListProps {
  addresses: AddressDto[];
  selectedAddressId?: number;
  isLoading?: boolean;
  onSelect?: (addressId: number) => void;
  onEdit?: (addressId: number) => void;
  onDelete?: (addressId: number) => void;
  className?: string;
  redirectPath?: string;
  showDeliveryStatus?: boolean;
}

const AddressList: React.FC<AddressListProps> = ({
  addresses,
  selectedAddressId,
  isLoading = false,
  onSelect,
  onEdit,
  onDelete,
  className = "",
  redirectPath = "/changeaddress?redirectTo=/select-address",
  showDeliveryStatus = false
}) => {
  const navigate = useNavigate();

  const handleAddressClick = (addressId: number) => {
    if (onSelect) {
      onSelect(addressId);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent, addressId: number) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleAddressClick(addressId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <SpinnerLoader size={8} loading={true} />
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center overflow-y-auto ${className}`}>
      {addresses?.length === 0 ? (
        <div className="text-center py-8 px-2 w-full">
          <p className="text-typography-500 mb-8">No addresses found</p>
          <div
            onClick={() => navigate(redirectPath)}
            className="w-full inline-flex items-center gap-3 p-3.5 bg-white text-primary rounded-lg hover:bg-neutral-50 shadow-sm cursor-pointer"
          >
            <Plus className="w-5 h-5" />
            <span className="font-semibold whitespace-nowrap">Add Address</span>
            <ChevronRight className="w-5 h-5 text-gray-500 ml-auto" />
          </div>
        </div>
      ) : addresses.length === 1 &&
        parseInt(addresses[0].latitude || "0") === 0 &&
        parseInt(addresses[0]?.longitude || "0") === 0 ? (
        <div className="text-center py-8 px-2 w-full">
          <p className="text-typography-500 mb-8">No addresses found</p>
          <div
            onClick={() =>
              navigate(redirectPath, {
                state: {
                  address: addresses[0],
                  isEdit: true,
                  from: "/select-address"
                }
              })
            }
            className="w-full inline-flex items-center gap-3 p-3.5 bg-white text-primary rounded-lg hover:bg-neutral-50 shadow-sm cursor-pointer"
          >
            <Plus className="w-5 h-5" />
            <span className="font-semibold whitespace-nowrap">Add Address</span>
            <ChevronRight className="w-5 h-5 text-gray-500 ml-auto" />
          </div>
        </div>
      ) : (
        <>
          <div className="w-full px-2">
            <div
              onClick={() => navigate(redirectPath)}
              className="w-full inline-flex items-center gap-3 p-3.5 bg-white text-primary rounded-lg hover:bg-neutral-50 shadow-sm cursor-pointer"
            >
              <Plus className="w-5 h-5" />
              <span className="font-semibold whitespace-nowrap">
                Add New Address
              </span>
              <ChevronRight className="w-5 h-5 text-gray-500 ml-auto" />
            </div>
          </div>

          <div className="w-full pt-5 pb-2 px-2 flex flex-row items-center justify-center gap-3">
            <div className="flex-1 border-t border-neutral-500"></div>
            <span className="text-typography-500 text-sm whitespace-nowrap">
              SAVED ADDRESSES
            </span>
            <div className="flex-1 border-t border-neutral-500"></div>
          </div>

          {/* {showDeliveryStatus && (
            <div className="w-full px-2">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span className="text-sm text-typography-500">
                    Delivers to
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <span className="text-sm text-typography-500">
                    Doesn&apos;t deliver to
                  </span>
                </div>
              </div>
            </div>
          )} */}

          <div className="space-y-4 w-full py-2">
            {addresses.map((address, index) => (
              <Button
                key={address.addressId}
                onClick={() =>
                  showDeliveryStatus && address.buyerInServiceArea
                    ? handleAddressClick(address.addressId)
                    : null
                }
                onKeyDown={(e) =>
                  showDeliveryStatus && address.buyerInServiceArea
                    ? handleKeyPress(e, address.addressId)
                    : null
                }
                className="w-full p-0 bg-transparent hover:bg-transparent focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg"
                aria-label={`Select ${address.name || ""} address`}
              >
                <AddressCard
                  id={address.addressId}
                  type={address.name as AddressType}
                  address={address.address || ""}
                  isSelected={address.addressId === selectedAddressId}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  showTrashIcon={addresses?.length > 1}
                  buyerInServiceArea={address.buyerInServiceArea}
                  showDeliveryStatus={showDeliveryStatus}
                />
              </Button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default AddressList;
