// import { useState } from "react";

const ForceUpdate = ({
  isVisible,
  updateUrl
}: {
  isVisible: boolean;
  updateUrl: string;
}) => {
  // const [showPopup, setShowPopup] = useState(forceUpdate);

  // useEffect(() => {
  //   // Compare versions and show the popup if an update is required
  //   if (currentVersion < latestVersion) {
  //     setShowPopup(true);
  //   }
  // }, [currentVersion, latestVersion]);

  console.log(isVisible, updateUrl);

  const handleUpdate = () => {
    // Redirect the user to the Play Store
    window.location.href = updateUrl;
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-6 w-11/12 sm:w-96 text-center">
        <h2 className="text-lg font-bold mb-4">Update Required</h2>
        <p className="text-gray-700 mb-6">
          A new version of the app is available. Please update to continue using
          the app.
        </p>
        <button
          onClick={handleUpdate}
          className="bg-teal-500 text-white px-4 py-2 rounded-lg hover:bg-teal-600 transition"
        >
          Update Now
        </button>
      </div>
    </div>
  );
};

export default ForceUpdate;
